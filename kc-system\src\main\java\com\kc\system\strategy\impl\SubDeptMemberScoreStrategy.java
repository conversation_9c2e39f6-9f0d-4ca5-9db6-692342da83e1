package com.kc.system.strategy.impl;

import com.kc.system.domain.ProjectEvaluation;
import com.kc.system.dto.ScoreCalculationContext;
import com.kc.system.strategy.ScoreCalculationStrategy;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import com.kc.system.service.IEvaluationConfigService;
import com.kc.system.service.ISysUserService;
import com.kc.system.domain.EvaluationConfig;
import com.kc.common.core.domain.entity.SysUser;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Calendar;

/**
 * 子部门成员评分策略
 * 适用于子部门成员
 * 计算规则：
 * 1. 如果是项目工作人员：机构负责人评分×60% + 项目负责人评分×40%
 * 2. 如果不是项目人员：子部门负责人评分×50% + 父部门负责人评分×50%
 * 3. 默认评分：超过评分周期时使用80分作为默认值
 *
 * <AUTHOR>
 */
@Component
public class SubDeptMemberScoreStrategy implements ScoreCalculationStrategy {

    @Autowired
    private IEvaluationConfigService evaluationConfigService;

    @Autowired
    private ISysUserService userService;

    private static final String STRATEGY_TYPE = "SUB_DEPT_MEMBER";

    // 非项目人员权重（子部门负责人50% + 父部门负责人50%）
    private static final BigDecimal SUB_DEPT_WEIGHT = new BigDecimal("0.5");
    private static final BigDecimal PARENT_DEPT_WEIGHT = new BigDecimal("0.5");

    // 项目人员权重（机构负责人60% + 项目负责人40%）
    private static final BigDecimal MANAGER_WEIGHT = new BigDecimal("0.6");
    private static final BigDecimal PROJECT_WEIGHT = new BigDecimal("0.4");

    // 默认评分
    private static final BigDecimal DEFAULT_SCORE = new BigDecimal("80");
    
    @Override
    public BigDecimal calculateScore(ScoreCalculationContext context) {
        List<ProjectEvaluation> evaluations = context.getEvaluations();

        // 判断是否为项目工作人员
        boolean hasProjectParticipation = hasProjectParticipation(context);

        if (hasProjectParticipation) {
            // 项目工作人员：使用机构负责人60% + 项目负责人40%的计算规则
            return calculateProjectMemberScore(evaluations, context);
        } else {
            // 非项目人员：使用子部门负责人50% + 父部门负责人50%的计算规则
            return calculateNonProjectMemberScore(evaluations, context);
        }
    }

    /**
     * 计算项目工作人员的评分
     * 规则：机构负责人评分×60% + 项目负责人评分×40%
     */
    private BigDecimal calculateProjectMemberScore(List<ProjectEvaluation> evaluations, ScoreCalculationContext context) {
        // 获取机构负责人评分
        BigDecimal managerScore = getManagerScore(evaluations);

        // 获取项目负责人评分
        BigDecimal projectScore = getProjectScore(evaluations);

        // 如果都没有评分，检查是否超过评分周期，使用默认评分
        if (managerScore == null && projectScore == null) {
            if (isOverEvaluationPeriod(context)) {
                return DEFAULT_SCORE;
            }
            return BigDecimal.ZERO;
        }

        // 如果只有一个评分，使用该评分
        if (managerScore == null) {
            managerScore = isOverEvaluationPeriod(context) ? DEFAULT_SCORE : BigDecimal.ZERO;
        }

        if (projectScore == null) {
            projectScore = isOverEvaluationPeriod(context) ? DEFAULT_SCORE : BigDecimal.ZERO;
        }

        // 计算加权平均分：60% + 40%
        BigDecimal weightedManagerScore = managerScore.multiply(MANAGER_WEIGHT);
        BigDecimal weightedProjectScore = projectScore.multiply(PROJECT_WEIGHT);

        return weightedManagerScore.add(weightedProjectScore)
                .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算非项目人员的评分
     * 规则：子部门负责人评分×50% + 父部门负责人评分×50%
     */
    private BigDecimal calculateNonProjectMemberScore(List<ProjectEvaluation> evaluations, ScoreCalculationContext context) {
        // 获取子部门负责人评分
        BigDecimal subDeptScore = getSubDeptManagerScore(evaluations, context.getSubDeptLeader());

        // 获取父部门负责人评分
        BigDecimal parentDeptScore = getParentDeptManagerScore(evaluations, context.getParentDeptLeader());

        // 如果都没有评分，检查是否超过评分周期，使用默认评分
        if (subDeptScore == null && parentDeptScore == null) {
            if (isOverEvaluationPeriod(context)) {
                return DEFAULT_SCORE;
            }
            return BigDecimal.ZERO;
        }

        // 如果只有一个评分，使用该评分
        if (subDeptScore == null) {
            subDeptScore = isOverEvaluationPeriod(context) ? DEFAULT_SCORE : BigDecimal.ZERO;
        }

        if (parentDeptScore == null) {
            parentDeptScore = isOverEvaluationPeriod(context) ? DEFAULT_SCORE : BigDecimal.ZERO;
        }

        // 计算加权平均分：50% + 50%
        BigDecimal weightedSubDeptScore = subDeptScore.multiply(SUB_DEPT_WEIGHT);
        BigDecimal weightedParentDeptScore = parentDeptScore.multiply(PARENT_DEPT_WEIGHT);

        return weightedSubDeptScore.add(weightedParentDeptScore)
                .setScale(2, RoundingMode.HALF_UP);
    }
    
    @Override
    public String getStrategyType() {
        return STRATEGY_TYPE;
    }
    
    @Override
    public boolean isApplicable(ScoreCalculationContext context) {
        // 适用于子部门成员
        return Boolean.TRUE.equals(context.getIsSubDeptMember());
    }
    
    @Override
    public String getCalculationDetails(ScoreCalculationContext context) {
        List<ProjectEvaluation> evaluations = context.getEvaluations();
        boolean hasProjectParticipation = hasProjectParticipation(context);

        if (hasProjectParticipation) {
            BigDecimal managerScore = getManagerScore(evaluations);
            BigDecimal projectScore = getProjectScore(evaluations);
            return String.format("子部门项目成员评分：机构负责人评分(%.2f)×60%% + 项目负责人评分(%.2f)×40%%",
                    managerScore != null ? managerScore : 0,
                    projectScore != null ? projectScore : 0);
        } else {
            BigDecimal subDeptScore = getSubDeptManagerScore(evaluations, context.getSubDeptLeader());
            BigDecimal parentDeptScore = getParentDeptManagerScore(evaluations, context.getParentDeptLeader());
            return String.format("子部门非项目成员评分：子部门负责人评分(%.2f)×50%% + 父部门负责人评分(%.2f)×50%%",
                    subDeptScore != null ? subDeptScore : 0,
                    parentDeptScore != null ? parentDeptScore : 0);
        }
    }

    /**
     * 获取子部门负责人评分
     */
    private BigDecimal getSubDeptManagerScore(List<ProjectEvaluation> evaluations, String subDeptLeader) {
        if (subDeptLeader == null) {
            return null;
        }

        // 根据用户名获取用户ID
        SysUser leaderUser = userService.selectUserByUserName(subDeptLeader);
        if (leaderUser == null) {
            return null;
        }

        Long leaderId = leaderUser.getUserId();

        return evaluations.stream()
                .filter(eval -> "manager".equals(eval.getEvaluationType())
                            && leaderId.equals(eval.getEvaluatorId()))
                .map(ProjectEvaluation::getScore)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取父部门负责人评分
     */
    private BigDecimal getParentDeptManagerScore(List<ProjectEvaluation> evaluations, String parentDeptLeader) {
        if (parentDeptLeader == null) {
            return null;
        }

        // 根据用户名获取用户ID
        SysUser leaderUser = userService.selectUserByUserName(parentDeptLeader);
        if (leaderUser == null) {
            return null;
        }

        Long leaderId = leaderUser.getUserId();

        return evaluations.stream()
                .filter(eval -> "parent_manager".equals(eval.getEvaluationType())
                            && leaderId.equals(eval.getEvaluatorId()))
                .map(ProjectEvaluation::getScore)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取机构负责人评分
     */
    private BigDecimal getManagerScore(List<ProjectEvaluation> evaluations) {
        return evaluations.stream()
                .filter(eval -> "manager".equals(eval.getEvaluationType()))
                .map(ProjectEvaluation::getScore)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取项目负责人评分（平均值）
     */
    private BigDecimal getProjectScore(List<ProjectEvaluation> evaluations) {
        List<ProjectEvaluation> projectEvals = evaluations.stream()
                .filter(eval -> "project_leader".equals(eval.getEvaluationType()))
                .collect(java.util.stream.Collectors.toList());

        if (projectEvals.isEmpty()) {
            return null;
        }

        BigDecimal sum = projectEvals.stream()
                .map(ProjectEvaluation::getScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return sum.divide(BigDecimal.valueOf(projectEvals.size()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 检查是否有项目参与
     */
    private boolean hasProjectParticipation(ScoreCalculationContext context) {
        return context.getProjectParticipations() != null
                && !context.getProjectParticipations().isEmpty();
    }

    /**
     * 检查是否超过评分周期
     * 根据评价配置判断当前月份是否超过了评分周期
     */
    private boolean isOverEvaluationPeriod(ScoreCalculationContext context) {
        try {
            String evaluationMonth = context.getEvaluationMonth();
            if (evaluationMonth == null || evaluationMonth.isEmpty()) {
                return false;
            }

            // 获取当前日期
            LocalDate currentDate = LocalDate.now();
            String currentMonth = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));

            // 如果评价月份是当前月份，检查是否超过填报周期
            if (evaluationMonth.equals(currentMonth)) {
                // 查询机构负责人评分的填报周期配置
                EvaluationConfig managerConfig = evaluationConfigService.selectEvaluationConfigByTypeAndMonth("manager_score", evaluationMonth);
                if (managerConfig != null && "Y".equals(managerConfig.getEnabled())) {
                    // 检查当前日期是否超过了填报结束日期
                    if (managerConfig.getEndDate() != null) {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(managerConfig.getEndDate());
                        LocalDate endDate = LocalDate.of(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1, cal.get(Calendar.DAY_OF_MONTH));
                        return currentDate.isAfter(endDate);
                    }
                }

                // 查询项目负责人评分的填报周期配置
                EvaluationConfig projectConfig = evaluationConfigService.selectEvaluationConfigByTypeAndMonth("leader_score", evaluationMonth);
                if (projectConfig != null && "Y".equals(projectConfig.getEnabled())) {
                    // 检查当前日期是否超过了填报结束日期
                    if (projectConfig.getEndDate() != null) {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(projectConfig.getEndDate());
                        LocalDate endDate = LocalDate.of(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1, cal.get(Calendar.DAY_OF_MONTH));
                        return currentDate.isAfter(endDate);
                    }
                }
            } else {
                // 如果评价月份不是当前月份，认为已经超过了评分周期
                return true;
            }

            return false;
        } catch (Exception e) {
            // 如果出现异常，默认不使用默认评分
            return false;
        }
    }
}
