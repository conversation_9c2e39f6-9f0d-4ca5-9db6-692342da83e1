-- 配额管理系统权限配置（最简版本）
-- 执行前请确保已经创建了配额管理相关的表

-- 1. 清理旧的权限配置
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%' OR perms LIKE 'system:quotaGroup:%'
);

DELETE FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%' OR perms LIKE 'system:quotaGroup:%';

-- 2. 插入配额管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES 
('配额管理', 2000, 6, 'quotaManagement', 'system/quotaManagement/index', '', 1, 0, 'C', '0', '0', 'system:quotaManagement:list', 'chart', 'admin', now(), '配额管理菜单');

-- 3. 插入配额组管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES 
('配额组管理', 2000, 7, 'quotaGroup', 'system/quotaGroup/index', '', 1, 0, 'C', '0', '0', 'system:quotaGroup:list', 'peoples', 'admin', now(), '配额组管理菜单');

-- 4. 查询刚插入的菜单ID（手动执行）
-- 请执行以下查询获取菜单ID，然后手动替换下面SQL中的 YOUR_QUOTA_MENU_ID 和 YOUR_QUOTA_GROUP_MENU_ID
SELECT menu_id, menu_name, perms FROM sys_menu WHERE perms IN ('system:quotaManagement:list', 'system:quotaGroup:list');

-- 5. 插入配额管理子菜单权限
-- 请将 YOUR_QUOTA_MENU_ID 替换为上面查询到的配额管理菜单ID
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES 
('配额查询', YOUR_QUOTA_MENU_ID, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:query', '#', 'admin', now(), ''),
('配额新增', YOUR_QUOTA_MENU_ID, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:add', '#', 'admin', now(), ''),
('配额修改', YOUR_QUOTA_MENU_ID, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:edit', '#', 'admin', now(), ''),
('配额删除', YOUR_QUOTA_MENU_ID, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:remove', '#', 'admin', now(), ''),
('配额导出', YOUR_QUOTA_MENU_ID, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:export', '#', 'admin', now(), ''),
('批量设置', YOUR_QUOTA_MENU_ID, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:batch', '#', 'admin', now(), ''),
('配额重置', YOUR_QUOTA_MENU_ID, 7, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:reset', '#', 'admin', now(), ''),
('配额统计', YOUR_QUOTA_MENU_ID, 8, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:statistics', '#', 'admin', now(), '');

-- 6. 插入配额组管理子菜单权限
-- 请将 YOUR_QUOTA_GROUP_MENU_ID 替换为上面查询到的配额组管理菜单ID
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES 
('配额组查询', YOUR_QUOTA_GROUP_MENU_ID, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:query', '#', 'admin', now(), ''),
('配额组新增', YOUR_QUOTA_GROUP_MENU_ID, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:add', '#', 'admin', now(), ''),
('配额组修改', YOUR_QUOTA_GROUP_MENU_ID, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:edit', '#', 'admin', now(), ''),
('配额组删除', YOUR_QUOTA_GROUP_MENU_ID, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:remove', '#', 'admin', now(), ''),
('配额组导出', YOUR_QUOTA_GROUP_MENU_ID, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:export', '#', 'admin', now(), '');

-- 7. 为管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%' OR perms LIKE 'system:quotaGroup:%';

-- 8. 验证权限配置
SELECT 
    m.menu_name,
    m.perms,
    m.menu_type,
    m.parent_id,
    m.visible,
    m.status
FROM sys_menu m 
WHERE m.perms LIKE 'system:quotaManagement:%' OR m.perms LIKE 'system:quotaGroup:%'
ORDER BY m.perms;

-- 9. 验证角色权限分配
SELECT 
    r.role_name,
    m.menu_name,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.perms LIKE 'system:quotaManagement:%' OR m.perms LIKE 'system:quotaGroup:%'
ORDER BY r.role_name, m.perms;

-- 执行说明：
-- 1. 先执行步骤1-3，插入主菜单
-- 2. 执行步骤4的查询，获取菜单ID
-- 3. 手动替换步骤5-6中的 YOUR_QUOTA_MENU_ID 和 YOUR_QUOTA_GROUP_MENU_ID
-- 4. 执行步骤5-6，插入子菜单
-- 5. 执行步骤7，分配权限
-- 6. 执行步骤8-9，验证配置

-- 权限说明：
-- system:quotaManagement:list - 配额管理列表查看
-- system:quotaManagement:query - 配额查询
-- system:quotaManagement:add - 新增配额
-- system:quotaManagement:edit - 修改配额
-- system:quotaManagement:remove - 删除配额
-- system:quotaManagement:export - 导出配额数据
-- system:quotaManagement:batch - 批量设置配额
-- system:quotaManagement:reset - 重置配额使用情况
-- system:quotaManagement:statistics - 配额统计

-- system:quotaGroup:list - 配额组管理列表查看
-- system:quotaGroup:query - 配额组查询
-- system:quotaGroup:add - 新增配额组
-- system:quotaGroup:edit - 修改配额组
-- system:quotaGroup:remove - 删除配额组
-- system:quotaGroup:export - 导出配额组数据
