package com.kc.system.service;

import java.util.List;
import com.kc.system.domain.QuotaManagement;

/**
 * 配额管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public interface IQuotaManagementService 
{
    /**
     * 查询配额管理
     * 
     * @param id 配额管理主键
     * @return 配额管理
     */
    public QuotaManagement selectQuotaManagementById(Long id);

    /**
     * 查询配额管理列表
     * 
     * @param quotaManagement 配额管理
     * @return 配额管理集合
     */
    public List<QuotaManagement> selectQuotaManagementList(QuotaManagement quotaManagement);

    /**
     * 新增配额管理
     * 
     * @param quotaManagement 配额管理
     * @return 结果
     */
    public int insertQuotaManagement(QuotaManagement quotaManagement);

    /**
     * 修改配额管理
     * 
     * @param quotaManagement 配额管理
     * @return 结果
     */
    public int updateQuotaManagement(QuotaManagement quotaManagement);

    /**
     * 批量删除配额管理
     * 
     * @param ids 需要删除的配额管理主键集合
     * @return 结果
     */
    public int deleteQuotaManagementByIds(Long[] ids);

    /**
     * 删除配额管理信息
     * 
     * @param id 配额管理主键
     * @return 结果
     */
    public int deleteQuotaManagementById(Long id);

    /**
     * 根据部门ID和年度查询配额管理
     * 
     * @param deptId 部门ID
     * @param year 评价年度
     * @return 配额管理
     */
    public QuotaManagement getQuotaByDeptAndYear(Long deptId, String year);

    /**
     * 检查配额是否可用
     * 
     * @param deptId 部门ID
     * @param year 评价年度
     * @param requestCount 请求数量
     * @return 是否可用
     */
    public boolean checkQuotaAvailable(Long deptId, String year, int requestCount);

    /**
     * 使用配额
     * 
     * @param deptId 部门ID
     * @param year 评价年度
     * @param count 使用数量
     * @return 是否成功
     */
    public boolean useQuota(Long deptId, String year, int count);

    /**
     * 释放配额
     * 
     * @param deptId 部门ID
     * @param year 评价年度
     * @param count 释放数量
     * @return 是否成功
     */
    public boolean releaseQuota(Long deptId, String year, int count);

    /**
     * 批量设置配额
     * 
     * @param quotaManagementList 配额管理列表
     * @return 结果
     */
    public int batchSetQuota(List<QuotaManagement> quotaManagementList);

    /**
     * 重置年度配额使用情况
     * 
     * @param year 评价年度
     * @return 结果
     */
    public int resetYearQuotaUsage(String year);

    /**
     * 查询配额统计
     * 
     * @param year 评价年度
     * @return 配额统计列表
     */
    public List<QuotaManagement> getQuotaStatistics(String year);

    /**
     * 初始化年度配额
     * 
     * @param year 评价年度
     * @return 结果
     */
    public int initYearQuota(String year);

    /**
     * 自动计算部门人数并更新
     * 
     * @param deptId 部门ID
     * @param year 评价年度
     * @return 结果
     */
    public int updateDeptEmployeeCount(Long deptId, String year);

    /**
     * 批量更新部门人数
     * 
     * @param year 评价年度
     * @return 结果
     */
    public int batchUpdateDeptEmployeeCount(String year);

    /**
     * 获取配额使用详情
     * 
     * @param deptId 部门ID
     * @param year 评价年度
     * @return 配额详情
     */
    public QuotaManagement getQuotaDetail(Long deptId, String year);
}
