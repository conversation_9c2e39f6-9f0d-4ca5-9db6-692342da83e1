-- 处理重复数据的SQL脚本
-- 保留最新的记录，删除旧的重复记录

-- 1. 创建临时表存储要保留的记录
CREATE TEMPORARY TABLE records_to_keep AS
SELECT 
    evaluator_id, 
    evaluatee_id, 
    project_id, 
    evaluation_type, 
    evaluation_month,
    MAX(id) as keep_id,
    MAX(created_at) as latest_created
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07')
GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month;

-- 2. 预览要删除的重复记录
SELECT 
    '即将删除的重复记录' as step,
    pe.id, pe.evaluator_id, pe.evaluatee_id, pe.project_id, 
    pe.evaluation_type, pe.evaluation_month, pe.score, pe.created_at
FROM project_evaluation pe
LEFT JOIN records_to_keep rtk ON 
    pe.evaluator_id = rtk.evaluator_id 
    AND pe.evaluatee_id = rtk.evaluatee_id 
    AND pe.project_id = rtk.project_id 
    AND pe.evaluation_type = rtk.evaluation_type 
    AND pe.evaluation_month = rtk.evaluation_month
    AND pe.id = rtk.keep_id
WHERE pe.evaluation_month IN ('2025-06', '2025-07')
AND rtk.keep_id IS NULL
ORDER BY pe.evaluator_id, pe.evaluatee_id, pe.project_id, pe.created_at;

-- 3. 删除重复记录
DELETE pe FROM project_evaluation pe
LEFT JOIN records_to_keep rtk ON 
    pe.evaluator_id = rtk.evaluator_id 
    AND pe.evaluatee_id = rtk.evaluatee_id 
    AND pe.project_id = rtk.project_id 
    AND pe.evaluation_type = rtk.evaluation_type 
    AND pe.evaluation_month = rtk.evaluation_month
    AND pe.id = rtk.keep_id
WHERE pe.evaluation_month IN ('2025-06', '2025-07')
AND rtk.keep_id IS NULL;

-- 4. 清理临时表
DROP TEMPORARY TABLE records_to_keep;

-- 5. 验证结果
SELECT 
    '去重后数据统计' as step,
    evaluation_month, 
    evaluation_type, 
    COUNT(*) as count 
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
GROUP BY evaluation_month, evaluation_type;
