-- 验证修复结果的SQL脚本

-- 1. 最终数据分布
SELECT 
    '最终数据分布' as check_type,
    evaluation_month, 
    evaluation_type, 
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
GROUP BY evaluation_month, evaluation_type 
ORDER BY evaluation_month, evaluation_type;

-- 2. 检查是否还有重复数据
SELECT 
    '重复数据检查' as check_type,
    CASE WHEN COUNT(*) = 0 THEN '✓ 无重复数据' ELSE CONCAT('✗ 仍有', COUNT(*), '组重复数据') END as result
FROM (
    SELECT 
        evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month,
        COUNT(*) as cnt
    FROM project_evaluation 
    WHERE evaluation_month IN ('2025-06', '2025-07')
    GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month
    HAVING COUNT(*) > 1
) duplicates;

-- 3. 检查数据完整性
SELECT 
    '数据完整性检查' as check_type,
    '总记录数' as metric,
    COUNT(*) as value
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07');

-- 4. 检查评分范围是否正常
SELECT 
    '评分范围检查' as check_type,
    evaluation_month,
    MIN(score) as min_score,
    MAX(score) as max_score,
    AVG(score) as avg_score
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07')
GROUP BY evaluation_month;

-- 5. 对比修复前后的数据量
SELECT 
    '修复前后对比' as check_type,
    '修复前总数' as period,
    COUNT(*) as count
FROM project_evaluation_backup_20250715
UNION ALL
SELECT 
    '修复前后对比' as check_type,
    '修复后总数' as period,
    COUNT(*) as count
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07');
