-- 配额组管理系统完整SQL脚本（不使用外键）

-- 1. 配额组表
CREATE TABLE `quota_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_name` varchar(100) NOT NULL COMMENT '配额组名称',
  `group_code` varchar(50) NOT NULL COMMENT '配额组编码',
  `description` varchar(500) DEFAULT NULL COMMENT '配额组描述',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_code` (`group_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额组表';

-- 2. 配额组部门关系表
CREATE TABLE `quota_group_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` bigint(20) NOT NULL COMMENT '配额组ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `is_primary` char(1) DEFAULT '0' COMMENT '是否主部门（0否 1是）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_dept` (`group_id`, `dept_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_dept_id` (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额组部门关系表';

-- 3. 配额组配额表
CREATE TABLE `quota_group_quota` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` bigint(20) NOT NULL COMMENT '配额组ID',
  `evaluation_year` varchar(4) NOT NULL COMMENT '评价年度',
  `total_employees` int(11) NOT NULL DEFAULT '0' COMMENT '组内总人数',
  `high_score_quota` int(11) NOT NULL DEFAULT '0' COMMENT '高分配额',
  `used_quota` int(11) NOT NULL DEFAULT '0' COMMENT '已使用配额',
  `remaining_quota` int(11) NOT NULL DEFAULT '0' COMMENT '剩余配额',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_year` (`group_id`, `evaluation_year`),
  KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额组配额表';

-- 4. 创建索引优化查询性能
CREATE INDEX `idx_quota_group_status` ON `quota_group` (`status`);
CREATE INDEX `idx_quota_group_dept_primary` ON `quota_group_dept` (`is_primary`);
CREATE INDEX `idx_quota_group_quota_year` ON `quota_group_quota` (`evaluation_year`);

-- 5. 初始化科技创新部和共享商务中心的配额组
INSERT INTO `quota_group` (`group_name`, `group_code`, `description`, `create_by`) 
VALUES ('科技创新配额组', 'TECH_INNOVATION_GROUP', '科技创新部与共享商务中心共享配额组', 'admin');

-- 获取刚插入的配额组ID
SET @group_id = LAST_INSERT_ID();

-- 添加部门到配额组（200为主部门，210为子部门）
INSERT INTO `quota_group_dept` (`group_id`, `dept_id`, `is_primary`, `create_by`) VALUES 
(@group_id, 200, '1', 'admin'),
(@group_id, 210, '0', 'admin');

-- 6. 为配额组初始化2025年配额（基于当前数据）
INSERT INTO `quota_group_quota` (`group_id`, `evaluation_year`, `total_employees`, `high_score_quota`, `used_quota`, `remaining_quota`, `create_by`)
SELECT 
    @group_id,
    '2025',
    SUM(total_employees) as total_employees,
    SUM(high_score_quota) as high_score_quota,
    SUM(used_quota) as used_quota,
    SUM(remaining_quota) as remaining_quota,
    'admin'
FROM dept_high_score_quota 
WHERE dept_id IN (200, 210) AND evaluation_year = '2025';

-- 7. 配额组管理菜单和权限配置
-- 插入配额组管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('配额组管理', 2000, 7, 'quotaGroup', 'system/quotaGroup/index', '', 1, 0, 'C', '0', '0', 'system:quotaGroup:list', 'peoples', 'admin', sysdate(), '', null, '配额组管理菜单');

-- 获取刚插入的菜单ID
SET @menu_id = LAST_INSERT_ID();

-- 插入配额组管理的子菜单（按钮权限）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('配额组查询', @menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:query', '#', 'admin', sysdate(), '', null, ''),
('配额组新增', @menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:add', '#', 'admin', sysdate(), '', null, ''),
('配额组修改', @menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:edit', '#', 'admin', sysdate(), '', null, ''),
('配额组删除', @menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:remove', '#', 'admin', sysdate(), '', null, ''),
('配额组导出', @menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:export', '#', 'admin', sysdate(), '', null, ''),
('配额组管理', @menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:manage', '#', 'admin', sysdate(), '', null, '');

-- 获取子菜单ID
SET @query_id = LAST_INSERT_ID() - 5;
SET @add_id = LAST_INSERT_ID() - 4;
SET @edit_id = LAST_INSERT_ID() - 3;
SET @remove_id = LAST_INSERT_ID() - 2;
SET @export_id = LAST_INSERT_ID() - 1;
SET @manage_id = LAST_INSERT_ID();

-- 为管理员角色分配配额组管理权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(1, @menu_id),
(1, @query_id),
(1, @add_id),
(1, @edit_id),
(1, @remove_id),
(1, @export_id),
(1, @manage_id);

-- 8. 验证查询
-- 查看配额组信息
SELECT 
    qg.id,
    qg.group_name,
    qg.group_code,
    qg.description,
    qg.status
FROM quota_group qg;

-- 查看配额组部门关系
SELECT 
    qgd.id,
    qgd.group_id,
    qgd.dept_id,
    qgd.is_primary,
    sd.dept_name,
    qg.group_name
FROM quota_group_dept qgd
LEFT JOIN sys_dept sd ON qgd.dept_id = sd.dept_id
LEFT JOIN quota_group qg ON qgd.group_id = qg.id;

-- 查看配额组配额信息
SELECT 
    qgq.id,
    qgq.group_id,
    qgq.evaluation_year,
    qgq.total_employees,
    qgq.high_score_quota,
    qgq.used_quota,
    qgq.remaining_quota,
    qg.group_name
FROM quota_group_quota qgq
LEFT JOIN quota_group qg ON qgq.group_id = qg.id;

-- 注意事项：
-- 1. 请根据实际的父菜单ID调整parent_id值（这里假设系统管理的parent_id为2000）
-- 2. 如果需要为其他角色分配权限，请参考上述模式添加相应的INSERT语句
-- 3. 执行前请备份数据库，确保可以回滚
-- 4. 建议在测试环境先验证SQL的正确性
-- 5. 不使用外键约束，通过应用层逻辑保证数据一致性
