package com.kc.system.service;

import java.util.List;
import com.kc.system.domain.EmployeeSalary;

/**
 * 员工薪酬Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-17
 */
public interface IEmployeeSalaryService 
{
    /**
     * 查询员工薪酬
     * 
     * @param id 员工薪酬主键
     * @return 员工薪酬
     */
    public EmployeeSalary selectEmployeeSalaryById(Long id);

    /**
     * 查询员工薪酬列表
     * 
     * @param employeeSalary 员工薪酬
     * @return 员工薪酬集合
     */
    public List<EmployeeSalary> selectEmployeeSalaryList(EmployeeSalary employeeSalary);

    /**
     * 新增员工薪酬
     * 
     * @param employeeSalary 员工薪酬
     * @return 结果
     */
    public int insertEmployeeSalary(EmployeeSalary employeeSalary);

    /**
     * 修改员工薪酬
     * 
     * @param employeeSalary 员工薪酬
     * @return 结果
     */
    public int updateEmployeeSalary(EmployeeSalary employeeSalary);

    /**
     * 批量删除员工薪酬
     * 
     * @param ids 需要删除的员工薪酬主键集合
     * @return 结果
     */
    public int deleteEmployeeSalaryByIds(Long[] ids);

    /**
     * 删除员工薪酬信息
     * 
     * @param id 员工薪酬主键
     * @return 结果
     */
    public int deleteEmployeeSalaryById(Long id);

    /**
     * 导入薪资数据
     * 
     * @param salaryList 薪资数据列表
     * @param updateSupport 是否支持更新
     * @param operName 操作用户
     * @return 结果
     */
    public String importSalary(List<EmployeeSalary> salaryList, boolean updateSupport, String operName);
}
