package com.kc.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 项目评价对象 project_evaluation
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
public class ProjectEvaluation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 评价人ID */
    @Excel(name = "评价人ID")
    private Long evaluatorId;

    /** 被评价人ID */
    @Excel(name = "被评价人ID")
    private Long evaluateeId;

    /** 评分 */
    @Excel(name = "评分")
    private BigDecimal score;

    /** 评价月份 格式：yyyy-MM */
    @Excel(name = "评价月份 格式：yyyy-MM")
    private String evaluationMonth;

    /** 评价类型：manager-机构负责人评价, project_leader-项目负责人评价 */
    @Excel(name = "评价类型：manager-机构负责人评价, project_leader-项目负责人评价")
    private String evaluationType;

    /** 评价意见 */
    @Excel(name = "评价意见")
    private String comments;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 项目名称（非数据库字段） */
    @Excel(name = "项目名称", sort = 1)
    private String projectName;

    /** 评价人姓名（非数据库字段） */
    @Excel(name = "评价人", sort = 2)
    private String evaluatorName;

    /** 被评价人姓名（非数据库字段） */
    @Excel(name = "被评价人", sort = 3)
    private String evaluateeName;

    /** 部门ID（非数据库字段，用于查询） */
    private Long deptId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setEvaluatorId(Long evaluatorId) 
    {
        this.evaluatorId = evaluatorId;
    }

    public Long getEvaluatorId() 
    {
        return evaluatorId;
    }
    public void setEvaluateeId(Long evaluateeId) 
    {
        this.evaluateeId = evaluateeId;
    }

    public Long getEvaluateeId() 
    {
        return evaluateeId;
    }
    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }
    public void setEvaluationMonth(String evaluationMonth) 
    {
        this.evaluationMonth = evaluationMonth;
    }

    public String getEvaluationMonth() 
    {
        return evaluationMonth;
    }
    public void setEvaluationType(String evaluationType) 
    {
        this.evaluationType = evaluationType;
    }

    public String getEvaluationType() 
    {
        return evaluationType;
    }
    public void setComments(String comments) 
    {
        this.comments = comments;
    }

    public String getComments() 
    {
        return comments;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }
    
    public String getProjectName() 
    {
        return projectName;
    }

    public void setEvaluatorName(String evaluatorName)
    {
        this.evaluatorName = evaluatorName;
    }

    public String getEvaluatorName()
    {
        return evaluatorName;
    }

    public void setEvaluateeName(String evaluateeName)
    {
        this.evaluateeName = evaluateeName;
    }

    public String getEvaluateeName()
    {
        return evaluateeName;
    }

    public void setProjectName(String projectName)
    {
        this.projectName = projectName;
    }

    public Long getDeptId()
    {
        return deptId;
    }
    
    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("evaluatorId", getEvaluatorId())
            .append("evaluateeId", getEvaluateeId())
            .append("score", getScore())
            .append("evaluationMonth", getEvaluationMonth())
            .append("evaluationType", getEvaluationType())
            .append("comments", getComments())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .append("projectName", getProjectName())
            .append("evaluatorName", getEvaluatorName())
            .append("evaluateeName", getEvaluateeName())
            .append("deptId", getDeptId())
            .toString();
    }
}
