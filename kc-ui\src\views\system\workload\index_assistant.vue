<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes class="default-theme" :push-other-panes="false">
        <!-- 左侧项目列表 -->
        <pane min-size="15" size="15">
          <div class="head-container">
            <el-input
              v-model="projectName"
              placeholder="请输入项目名称"
              clearable
              size="small"
              prefix-icon="el-icon-search"
              style="margin-bottom: 20px"
            />
          </div>
          <div class="head-container">
            <el-tree
              :data="projectOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="tree"
              node-key="id"
              default-expand-all
              highlight-current
              @node-click="handleNodeClick"
            >
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <!-- 如果是项目节点 -->
                <span v-if="!data.isProjectMember">
                  <i class="el-icon-document" style="color: #909399; margin-right: 5px;"></i>
                  {{ node.label }}
                </span>
                <!-- 如果是成员节点 -->
                <span v-else class="member-node">
                  <i class="el-icon-user" style="color: #909399; margin-right: 5px;"></i>
                  {{ node.label }}
                  <!-- 只有非负责人才显示删除按钮 -->
                  <i 
                    v-if="data.role !== '负责人'"
                    class="el-icon-close delete-icon" 
                    v-hasPermi="['system:members:remove']"
                    @click.stop="handleDeleteMember(data)"
                  ></i>
                  <!-- 显示角色标签 -->
                  <!-- <span class="member-role">{{ data.role }}</span> -->
                </span>
              </span>
            </el-tree>
          </div>
        </pane>
        
        <!-- 右侧内容 -->
        <pane>
          <!-- 搜索表单 -->
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
            <el-form-item label="工作月份" prop="workMonth">
              <el-date-picker
                v-model="queryParams.workMonth"
                type="month"
                placeholder="选择月份"
                value-format="yyyy-MM"
                style="width: 140px"
                @change="handleMonthChange"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <!-- 按钮工具栏 - 移到外层，让两个组件都能看到 -->
          <el-row :gutter="10" class="mb8">
            <!-- 工时表专用按钮 -->
            <template v-if="!showProjectStats">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  @click="handleAdd"
                  v-hasPermi="['system:workload:add']"
                >工时填报</el-button>
              </el-col>
              
              <el-col :span="1.5">
                <el-button
                  type="success"
                  plain
                  icon="el-icon-edit"
                  size="mini"
                  :disabled="single"
                  @click="handleUpdate"
                  v-hasPermi="['system:workload:edit']"
                >修改</el-button>
              </el-col>
              
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['system:workload:remove']"
                >删除</el-button>
              </el-col>
            </template>
   
            <!-- 添加项目成员按钮始终显示 -->
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAddMember"
                v-hasPermi="['system:members:add']"
              >添加项目成员</el-button>
            </el-col>

            <!-- 添加一键填报按钮 -->
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-check"
                size="mini"
                @click="handleBatchAddWorkload"
                v-hasPermi="['system:workload:add']"
              >一键填报</el-button>
            </el-col>

            <!-- 搜索框控制按钮 -->
            <right-toolbar :showSearch.sync="showSearch" @queryTable="handleQuery"></right-toolbar>
          </el-row>

          <!-- 动态组件区域 -->
          <project-statistics 
            v-if="showProjectStats" 
            :projectId="currentProjectId"
            :workMonth="queryParams.workMonth"
            ref="projectStats"
          />
          <div v-else>
            <!-- 工时表内容 -->
            <div class="table-wrapper">
              <!-- 工时列表表格 -->
              <el-table 
                v-loading="loading" 
                :data="workloadList"
                @selection-change="handleSelectionChange"
                style="width: 100%"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="序号" type="index" width="50" align="center" />
                <el-table-column label="工作月份" align="center" prop="workMonth" width="120" />
                <el-table-column label="项目参与度" align="center" prop="involvement" width="120">
                  <template slot-scope="scope">
                    {{ scope.row.involvement.toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="handleUpdate(scope.row)"
                      v-hasPermi="['system:workload:edit']"
                    >修改</el-button>
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDelete(scope.row)"
                      v-hasPermi="['system:workload:remove']"
                    >删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-show="total>0 && !showProjectStats"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
              />
            </div>
          </div>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改工时记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="姓名" prop="userName">
          <el-input :value="selectedMember ? selectedMember.nickName : ''" disabled />
        </el-form-item>
        <el-form-item label="工作月份" prop="workMonth">
          <el-date-picker
            v-model="form.workMonth"
            type="month"
            placeholder="选择月份"
            value-format="yyyy-MM"
            :picker-options="{
              disabledDate: time => {
                const currentDate = new Date();
                const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                return time.getTime() > currentMonth.getTime();
              }
            }"
            style="width: 100%"
            @change="month => {
              if (month && this.selectedMember) {
                this.getUserProjectWorkloads(this.selectedMember.userName, month);
              }
            }"
          />
        </el-form-item>
        <el-form-item label="项目工时" style="margin-bottom: 0">
          <div style="width: 100%">
            <el-table :data="userProjectWorkloads">
              <el-table-column prop="projectName" label="项目名称" />
              <el-table-column label="项目参与度" width="200">
                <template slot-scope="scope">
                  <el-input-number
                    v-if="scope.row.projectId === currentProjectId"
                    v-model="form.involvement"
                    :precision="2"
                    :step="0.1"
                    :min="0"
                    :max="1"
                    style="width: 100%"
                  />
                  <span v-else>
                    {{ scope.row.involvement === -1 ? '-' : scope.row.involvement.toFixed(2) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    
    <!-- 添加或修改项目成员关联对话框 -->
    <el-dialog :title="title" :visible.sync="memberDialogOpen" width="500px" append-to-body>
      <el-form ref="memberForm" :model="form" :rules="memberRules" label-width="100px">
        <el-form-item label="当前项目">
          <el-input v-model="currentProjectName" disabled />
        </el-form-item>
        <el-form-item label="成员姓名" prop="userNames">
          <el-select
            v-model="form.userNames"
            multiple
            placeholder="请选择成员"
            clearable
            filterable
            style="width: 100%"
            :loading="userLoading"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.userName"
              :label="user.nickName + ' - ' + (user.dept ? user.dept.deptName : '')"
              :value="user.userName"
              :disabled="existingMembers.includes(user.userName)"
            >
              <span style="float: left">{{ user.nickName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ user.dept ? user.dept.deptName : '' }}
                <span v-if="existingMembers.includes(user.userName)" style="color: #F56C6C; margin-left: 5px">
                  (已在项目中)
                </span>
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目角色" prop="role">
          <el-input v-model="form.role" disabled />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMemberForm">确 定</el-button>
        <el-button @click="cancelMember">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWorkload, getWorkload, delWorkload, addWorkload, updateWorkload, getProjectStats } from "@/api/system/workload";
import { listInfo, batchAddWorkload } from "@/api/system/info";
import { listUser } from "@/api/system/user";
import { listMembers } from "@/api/system/members";
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import { listUserLeadProjects, listDeptProjects } from "@/api/system/info";
import { addMembers, delMembers } from "@/api/system/members";
import { getUserProjectWorkloads } from "@/api/system/info";
import ProjectStatistics from './statistics/projectstatistics'

export default {
  name: "Workload",
  components: {
    Splitpanes,
    Pane,
    ProjectStatistics
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工时记录表格数据
      workloadList: [],
      // 项目列表数据
      projectOptions: [],
      // 用户列表数据
      userOptions: [],
      // 项目名称
      projectName: "",
      // 当前选中的项目ID
      currentProjectId: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 默认树形配置
      defaultProps: {
        children: "children",
        label: node => {
          if (node.isProjectMember) {
            // console.log('成员节点数据:', node);
            return node.nickName || '未知用户';
          }
          return node.projectName;
        }
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        userName: null,
        workMonth: null
      },
      // 表单参数
      form: {
        id: null,
        projectId: null,
        userName: null,
        workMonth: null,
        involvement: 0,  // 确保初始化为数字
        createdAt: null,
        updatedAt: null
      },
      // 表单校验
      rules: {
        userNames: [
          { required: true, message: "请选择成员", trigger: "change" },
          { type: 'array', min: 1, message: '请至少选择一个成员', trigger: 'change' }
        ],
        role: [
          { required: true, message: "请选择角色", trigger: "change" }
        ]
      },
      // 添加选中的成员信息
      selectedMember: null,
      // 项目成员表单校验规则
      memberRules: {
        userName: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        role: [
          { required: true, message: "项目角色不能为空", trigger: "blur" }
        ]
      },
      // 控制成员对话框的显示
      memberDialogOpen: false,
      // 当前项目名称
      currentProjectName: '',
      // 当前项目的成员列表
      existingMembers: [],
      // 用户所有项目工时数据
      userProjectWorkloads: [],
      // 添加新的数据属性
      showProjectStats: false,  // 是否显示项目统计
      projectStatsList: [],     // 项目统计数据
      currentComponent: 'ProjectStatistics',
      userLoading: false,  // 添加用户列表加载状态
    };
  },
  watch: {
    projectName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    // 移除 getProjectList，改为在 mounted 中调用
    this.getUserList();
  },
  mounted() {
    // 获取项目列表并选中第一个项目
    this.getProjectList().then(() => {
      if (this.projectOptions && this.projectOptions.length > 0) {
        const firstProject = this.projectOptions[0];
        this.$nextTick(() => {
          // 通过 tree 组件的 setCurrentKey 方法选中节点
          this.$refs.tree.setCurrentKey(firstProject.id);
          // 触发节点点击事件，显示项目统计
          this.handleNodeClick(firstProject);
        });
      }
    });
  },
  methods: {
    /** 查询项目列表 */
    getProjectList() {
      return new Promise((resolve, reject) => {
        listDeptProjects().then(response => {
          this.projectOptions = this.handleProjectData(response.rows);
          this.$nextTick(() => {
            if (this.projectName) {
              this.$refs.tree.filter(this.projectName);
            }
            // 如果有项目，选中第一个项目
            if (this.projectOptions && this.projectOptions.length > 0) {
              const firstProject = this.projectOptions[0];
              this.$refs.tree.setCurrentKey(firstProject.id);
              this.handleNodeClick(firstProject);
            }
            resolve();
          });
        }).catch(error => {
          reject(error);
        });
      });
    },
    /** 查询用户列表 */
    getUserList() {
      listUser().then(response => {
        this.userOptions = response.rows;
      });
    },
    /** 筛选节点 */
    filterNode(value, data) {
      if (!value) return true;
      if (data.isProjectMember) {
        return data.nickName.indexOf(value) !== -1;
      }
      return data.projectName.indexOf(value) !== -1;
    },
    /** 节点单击事件 */
    handleNodeClick(data) {
      if (!data) return;
      
      // 重置选中状态
      this.ids = [];
      this.single = true;
      this.multiple = true;
      
      if (data.isProjectMember) {
        // 点击的是成员节点
        this.currentProjectId = data.projectId;
        this.queryParams.projectId = data.projectId;
        this.queryParams.userName = data.userName;
        this.showProjectStats = false;  // 显示工时表
        // 设置选中的成员信息
        this.selectedMember = {
          userName: data.userName,
          nickName: data.nickName || '未知用户',
          role: data.role
        };
      } else {
        // 点击的是项目节点
        this.currentProjectId = data.id;
        this.currentProjectName = data.projectName;
        this.queryParams.projectId = data.id;
        this.queryParams.userName = null;
        this.showProjectStats = true;  // 显示项目统计
        this.selectedMember = null;  // 清空选中的成员
      }
      
      // 重新加载数据
      this.getList();
    },
    /** 查询工时记录列表 */
    getList() {
      if (this.showProjectStats) {
        // 如果是显示项目统计，则不需要获取工时列表
        return;
      }

      if (!this.queryParams.projectId || !this.queryParams.userName) {
        this.workloadList = [];
        this.total = 0;
        this.loading = false;
        return;
      }
      
      this.loading = true;
      listWorkload(this.queryParams).then(response => {
        // 使用 Set 来存储唯一的 workMonth，避免重复
        const uniqueWorkloads = new Map();
        console.log(response.rows);
        
        // 先过滤掉 id 为 0 的记录，再处理剩余数据
        (response.rows || [])
          .filter(workload => workload.id !== 0)  // 添加这个过滤条件
          .forEach(workload => {
            if (workload.workMonth) {
              const key = workload.workMonth;
              // 如果该月份还没有记录，或者当前记录更新时间更晚，则更新记录
              if (!uniqueWorkloads.has(key) || 
                  new Date(workload.updatedAt) > new Date(uniqueWorkloads.get(key).updatedAt)) {
                uniqueWorkloads.set(key, workload);
              }
            }
          });

        // 将 Map 转换为数组并按月份排序
        this.workloadList = Array.from(uniqueWorkloads.values())
          .sort((a, b) => b.workMonth.localeCompare(a.workMonth));
        
        this.total = this.workloadList.length;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 重置表单
    reset() {
      this.form = {
        id: null,
        projectId: null,
        userName: null,
        workMonth: null,
        involvement: 0,  // 重置时也设置为数字
        createdAt: null,
        updatedAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.handleMonthChange();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.workMonth = null;
      this.handleMonthChange();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      if (!this.selectedMember || !this.selectedMember.userName) {
        this.$modal.msgWarning("请选择填报对象");
        return;
      }

      this.reset();
      this.form.projectId = this.currentProjectId;
      this.form.userName = this.selectedMember.userName;
      
      // 设置当前月份并获取工时数据
      const now = new Date();
      const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      this.$nextTick(() => {
        this.form.workMonth = currentMonth;
        // 获取用户所有项目工时
        this.getUserProjectWorkloads(this.selectedMember.userName, currentMonth);
      });
      
      this.open = true;
      this.title = "添加项目工时记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getWorkload(id).then(response => {
        this.form = response.data;
        
        // 获取用户所有项目工时
        this.getUserProjectWorkloads(this.form.userName, this.form.workMonth);
        
        this.open = true;
        this.title = "修改项目工时记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id == null) {
            // 新增时才检查重复填报
            const hasMonthRecord = this.workloadList.some(item => 
              item.workMonth === this.form.workMonth
            );
            
            if (hasMonthRecord) {
              this.$modal.msgError("该月份已填报工时，请勿重复填报");
              return;
            }
          }

          // 计算所有项目参与度之和
          const totalInvolvement = this.userProjectWorkloads.reduce((sum, project) => {
            // 如果是当前编辑的项目，使用表单中的值
            if (project.projectId === this.currentProjectId) {
              return sum + (this.form.involvement || 0);
            }
            // 如果是正在修改的记录，跳过原值的计算
            if (this.form.id != null && project.projectId === this.form.projectId) {
              return sum;
            }
            // 其他项目使用原有的值，-1 表示未填报，计为 0
            return sum + (project.involvement === -1 ? 0 : project.involvement);
          }, 0);

          // 检查总参与度是否超过1
          if (totalInvolvement > 1) {
            this.$modal.msgError("所有项目参与度之和不能超过1，当前总和为：" + totalInvolvement.toFixed(2));
            return;
          }

          if (this.form.id != null) {
            updateWorkload(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWorkload(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      let workMonths;
      
      if (row && row.workMonth) {
        // 单个删除
        workMonths = row.workMonth;
      } else {
        // 批量删除，获取所有选中记录的月份
        workMonths = this.workloadList
          .filter(item => this.ids.includes(item.id))
          .map(item => item.workMonth)
          .join('、');
      }

      if (!workMonths) {
        this.$modal.msgError("获取工时记录信息失败");
        return;
      }
      
      this.$modal.confirm('是否确认删除' + workMonths + '月的工时记录？').then(function() {
        return delWorkload(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 获取用户列表 */
    getUsers() {
      listUser({}).then(response => {
        this.userOptions = response.rows;
        // 添加调试日志
        // console.log('获取到的用户列表:', this.userOptions);
      });
    },
    /** 添加项目成员按钮操作 */
    handleAddMember() {
      // 检查是否选择了项目
      if (!this.currentProjectId) {
        this.$modal.msgWarning("请先选择项目");
        return;
      }

      this.reset();
      // 设置当前选中的项目ID和角色
      this.form.projectId = this.currentProjectId;
      this.form.role = "参与";
      
      // 设置当前项目名称
      const currentProject = this.projectOptions.find(p => p.id === this.currentProjectId);
      this.currentProjectName = currentProject ? currentProject.projectName : '';
      
      // 获取当前项目的成员列表
      this.existingMembers = currentProject?.children?.map(member => member.userName) || [];
      
      // 获取用户列表
      this.getUsers();
      
      this.memberDialogOpen = true;
      this.title = "添加项目成员";
    },
    // 提交成员表单
    submitMemberForm() {
      this.$refs["memberForm"].validate(valid => {
        if (valid) {
          if (!this.form.userNames || this.form.userNames.length === 0) {
            this.$modal.msgError("请选择要添加的成员");
            return;
          }

          // 创建批量添加的请求数组
          const addRequests = this.form.userNames.map(userName => {
            return {
              projectId: this.currentProjectId,
              userName: userName,
              role: this.form.role
            };
          });

          // 使用 Promise.all 批量添加成员
          Promise.all(addRequests.map(request => addMembers(request)))
            .then(() => {
              this.$modal.msgSuccess("添加成功");
              this.memberDialogOpen = false;
              
              // 重新获取项目列表以更新成员信息
              return this.getProjectList().then(() => {
                // 然后获取并更新树
                return listInfo().then(response => {
                  this.projectOptions = this.handleProjectData(response.data);
                  
                  // 在数据更新后，重新选中当前项目
                  this.$nextTick(() => {
                    this.$refs.tree.setCurrentKey(this.currentProjectId);
                    const node = this.$refs.tree.getNode(this.currentProjectId);
                    if (node) {
                      this.handleNodeClick(node.data);
                    }
                  });
                });
              });
            })
            .catch((error) => {
              // console.error('添加成员失败:', error);
              // this.$modal.msgError("添加成员失败");
            });
        }
      });
    },
    // 取消成员表单
    cancelMember() {
      this.memberDialogOpen = false;
      this.reset();
    },
    /** 删除项目成员按钮操作 */
    handleDeleteMember(data) {
      // 检查是否为负责人
      if (data.role === '负责人') {
        this.$modal.msgError('不能删除项目负责人');
        return;
      }

      const memberName = data.nickName || data.userName;
      // 保存当前项目ID
      const projectId = this.currentProjectId;

      this.$modal.confirm('是否确认删除成员"' + memberName + '"？').then(() => {
        return delMembers(data.id);
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        
        // 先获取最新的项目列表
        return this.getProjectList().then(() => {
          // 然后获取并更新树
          return listInfo().then(response => {
            this.projectOptions = this.handleProjectData(response.data);
            
            // 在数据更新后，重新选中当前项目
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(projectId);
              const node = this.$refs.tree.getNode(projectId);
              if (node) {
                this.currentProjectId = projectId;
                this.handleNodeClick(node.data);
              }
            });
          });
        });
      }).catch(() => {});
    },
    // 月份变化时获取工时数据
    handleMonthChange() {
      if (this.showProjectStats) {
        // 如果是项目统计视图，调用其刷新方法
        this.$refs.projectStats.getProjectStats();
      } else {
        // 如果是工时表视图，调用其查询方法
        this.getList();
      }
    },
    // 获取用户所有项目工时
    getUserProjectWorkloads(userName, month) {
      getUserProjectWorkloads(userName, month).then(response => {
        this.userProjectWorkloads = response.data;
        
        // 找到当前项目的工时记录
        const currentProject = this.userProjectWorkloads.find(p => p.projectId === this.currentProjectId);
        
        if (currentProject && currentProject.involvement !== -1) {
          // 如果当月有数据，使用当月数据
          this.form.involvement = currentProject.involvement;
        } else {
          // 如果当月没有数据，获取上月数据
          const lastMonth = this.getLastMonth(month);
          getUserProjectWorkloads(userName, lastMonth).then(lastMonthResponse => {
            const lastMonthProject = lastMonthResponse.data.find(p => p.projectId === this.currentProjectId);
            // 如果有上月数据，使用上月数据，否则默认为0
            this.form.involvement = lastMonthProject && lastMonthProject.involvement !== -1 ? 
              lastMonthProject.involvement : 0;
          });
        }
      });
    },
    // 获取上个月的日期字符串 (yyyy-MM)
    getLastMonth(currentMonth) {
      const [year, month] = currentMonth.split('-').map(Number);
      const date = new Date(year, month - 1, 1); // 月份从0开始
      date.setMonth(date.getMonth() - 1);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    },
    // 获取项目统计数据
    getProjectStats(projectId) {
      this.loading = true;
      getProjectStats(projectId).then(response => {
        this.projectStatsList = response.data;
        this.loading = false;
      });
    },
    // 确保 getProjectTree 方法正确实现
    getProjectTree() {
      listInfo().then(response => {
        this.projectOptions = this.handleProjectData(response.data);
        this.$nextTick(() => {
          if (this.projectName) {
            this.$refs.tree.filter(this.projectName);
          }
        });
      });
    },
    /** 处理项目数据，添加成员作为子节点 */
    handleProjectData(projects) {
      return projects.map(project => {
        // 使用 Map 来去重，以 userName 为 key
        const memberMap = new Map();
        
        // 过滤并转换成员为树节点格式
        (project.memberList || [])
          .filter(member => member.role !== '配合人员')
          .forEach(member => {
            // 如果该用户名还未添加过，则添加到 Map 中
            if (!memberMap.has(member.userName)) {
              memberMap.set(member.userName, {
                id: member.id,
                projectId: project.id,
                label: member.nickName,
                userName: member.userName,
                role: member.role,
                isProjectMember: true,
                nickName: member.nickName
              });
            }
          });

        // 将 Map 转换回数组
        const memberNodes = Array.from(memberMap.values());

        return {
          id: project.id,
          label: project.projectName,
          projectName: project.projectName,
          children: memberNodes
        };
      });
    },
    // 新增方法: 刷新树并保持选中状态
    async refreshTreeWithSelection() {
      const currentProjectId = this.currentProjectId;
      
      // 重新获取项目树
      await this.getProjectTreeAsPromise();
      
      // 如果之前有选中的项目,则重新选中
      if (currentProjectId) {
        this.$nextTick(() => {
          // 找到对应的节点并选中
          const node = this.$refs.tree.getNode(currentProjectId);
          if (node) {
            this.$refs.tree.setCurrentKey(currentProjectId);
            // 触发节点点击事件
            this.handleNodeClick(node.data);
          }
        });
      }
    },

    // 将 getProjectTree 改造成返回 Promise 的方法
    getProjectTreeAsPromise() {
      return new Promise((resolve, reject) => {
        listInfo().then(response => {
          this.projectOptions = this.handleProjectData(response.data);
          this.$nextTick(() => {
            if (this.projectName) {
              this.$refs.tree.filter(this.projectName);
            }
            resolve();
          });
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 处理输入框获得焦点
    handleFocus(event) {
      // 确保组件可以响应点击事件
      event.target.select();
    },

    // 处理输入框失去焦点
    handleBlur(event) {
      // 确保值在合法范围内
      const value = parseFloat(this.form.involvement);
      if (isNaN(value)) {
        this.form.involvement = 0;
      } else if (value < 0) {
        this.form.involvement = 0;
      } else if (value > 1) {
        this.form.involvement = 1;
      } else {
        this.form.involvement = parseFloat(value.toFixed(2));
      }
    },
    // 处理参与度变化
    handleInvolvementChange(value) {
      // 确保值在合法范围内
      if (value < 0) {
        this.form.involvement = 0;
      } else if (value > 1) {
        this.form.involvement = 1;
      } else {
        // 保留两位小数
        this.form.involvement = parseFloat(value.toFixed(2));
      }
    },
    // 添加新的计算属性
    filteredWorkloadList() {
      return this.workloadList.filter(item => item.workMonth);
    },
    /** 一键填报按钮操作 */
    handleBatchAddWorkload() {
      this.$modal.confirm('是否确认一键填报所有项目成员工时？').then(() => {
        this.loading = true;
        batchAddWorkload().then(response => {
          this.$modal.msgSuccess(response.msg);
          // 刷新当前显示的数据
          if (this.showProjectStats) {
            this.$refs.projectStats.getProjectStats();
          } else {
            this.getList();
          }
        }).finally(() => {
          this.loading = false;
        });
      }).catch(() => {});
    },
  }
};
</script>

<style scoped>
.head-container {
  padding: 10px;
}
.splitpanes.default-theme .splitpanes__pane {
  background: #fff;
}
.splitpanes.default-theme .splitpanes__splitter {
  background: #f0f2f5;
  margin: 0 -1px;
}
.splitpanes.default-theme .splitpanes__splitter:before {
  background: #f0f2f5;
}
.splitpanes.default-theme .splitpanes__splitter:hover:before {
  background: #409eff;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
}
.el-tree-node__content {
  height: 32px;
}
.el-tree-node__content:hover {
  background-color: #f5f7fa;
}
.member-node {
  position: relative;
  display: inline-block;
  width: 100%;
  
  .delete-icon {
    display: none;
    margin-left: 4px;
    color: #909399;
    font-size: 12px;
    cursor: pointer;
    
    &:hover {
      color: #f56c6c;
    }
  }
  
  &:hover .delete-icon {
    display: inline-block;
  }
}
.el-tooltip__popper {
  max-width: 500px;
  white-space: pre-line;
}
.table-wrapper {
  margin-top: 8px;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}
.member-role {
  color: #909399;
  font-size: 12px;
  margin-left: 4px;
}
</style>
