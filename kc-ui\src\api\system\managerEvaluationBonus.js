import request from '@/utils/request'

// 获取部门信息（包含奖金分配信息）
export function getDeptInfo(month) {
  return request({
    url: '/system/managerEvaluationBonus/getDeptInfo/' + month,
    method: 'get'
  })
}

// 获取部门员工列表（用于奖金分配）
export function getDeptEmployees(deptId, month) {
  return request({
    url: '/system/managerEvaluationBonus/getDeptEmployees/' + deptId + '/' + month,
    method: 'get'
  })
}

// 提交员工奖金分配
export function allocateBonus(data) {
  return request({
    url: '/system/managerEvaluationBonus/allocateBonus',
    method: 'post',
    data: data
  })
}

// 获取部门奖金分配状态
export function getBonusStatus(deptId, month) {
  return request({
    url: '/system/managerEvaluationBonus/getBonusStatus/' + deptId + '/' + month,
    method: 'get'
  })
}

// 获取员工奖金分配历史
export function getAllocationHistory(deptId, month) {
  return request({
    url: '/system/managerEvaluationBonus/getAllocationHistory/' + deptId + '/' + month,
    method: 'get'
  })
}
