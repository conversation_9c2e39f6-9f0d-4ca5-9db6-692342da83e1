-- ===== 简单修改：将2025-06的project_leader和manager评分改为2025-07 =====
-- 警告：此方案可能产生重复数据

-- 第一步：创建备份
CREATE TABLE project_evaluation_backup_simple_20250715 AS 
SELECT * FROM project_evaluation 
WHERE evaluation_month = '2025-06' 
AND evaluation_type IN ('project_leader', 'manager');

-- 第二步：查看修改前状态
SELECT 
    '修改前状态' as step,
    evaluation_month,
    evaluation_type,
    COUNT(*) as count
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
AND evaluation_type IN ('project_leader', 'manager')
GROUP BY evaluation_month, evaluation_type
ORDER BY evaluation_month, evaluation_type;

-- 第三步：预览要修改的数据
SELECT 
    '即将修改的数据' as step,
    evaluation_type,
    COUNT(*) as count,
    MIN(updated_at) as earliest_updated,
    MAX(updated_at) as latest_updated
FROM project_evaluation 
WHERE evaluation_month = '2025-06' 
AND evaluation_type IN ('project_leader', 'manager')
GROUP BY evaluation_type;

-- 第四步：执行修改
UPDATE project_evaluation 
SET evaluation_month = '2025-07'
WHERE evaluation_month = '2025-06' 
AND evaluation_type IN ('project_leader', 'manager');

-- 第五步：查看修改后状态
SELECT 
    '修改后状态' as step,
    evaluation_month,
    evaluation_type,
    COUNT(*) as count
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
AND evaluation_type IN ('project_leader', 'manager')
GROUP BY evaluation_month, evaluation_type
ORDER BY evaluation_month, evaluation_type;

-- 第六步：检查重复数据
SELECT 
    '重复数据检查' as step,
    evaluation_type,
    evaluator_id,
    evaluatee_id,
    project_id,
    COUNT(*) as duplicate_count
FROM project_evaluation 
WHERE evaluation_month = '2025-07'
AND evaluation_type IN ('project_leader', 'manager')
GROUP BY evaluation_type, evaluator_id, evaluatee_id, project_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, evaluation_type
LIMIT 10;

SELECT 
    '修改完成' as status,
    NOW() as completion_time,
    '警告：可能存在重复数据，建议检查' as note;
