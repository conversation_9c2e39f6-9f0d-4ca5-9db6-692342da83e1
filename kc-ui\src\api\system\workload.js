import request from '@/utils/request'

// 查询项目工时记录列表
export function listWorkload(query) {
  return request({
    url: '/system/workload/list',
    method: 'get',
    params: query
  })
}

// 查询项目工时记录详细
export function getWorkload(id) {
  return request({
    url: '/system/workload/' + id,
    method: 'get'
  })
}

// 新增项目工时记录
export function addWorkload(data) {
  return request({
    url: '/system/workload',
    method: 'post',
    data: data
  })
}

// 修改项目工时记录
export function updateWorkload(data) {
  return request({
    url: '/system/workload',
    method: 'put',
    data: data
  })
}

// 删除项目工时记录
export function delWorkload(id) {
  return request({
    url: '/system/workload/' + id,
    method: 'delete'
  })
}

// 获取项目统计数据
export function getProjectStats(projectId, workMonth) {
  return request({
    url: `/system/workload/stats/${projectId}`,
    method: 'get',
    params: { workMonth }
  });
}

// 获取所有项目统计数据（不分页）
export function getAllProjectStats(workMonth) {
  return request({
    url: '/system/workload/stats/all',
    method: 'get',
    params: { workMonth }
  });
}
export function getUnfilledWorkloadCount(workMonth) {
  return request({
    url: `/system/workload/unfilled`,
    method: 'get',
    params: { workMonth }
  });
}

// 获取用户当月工时记录
export function getUserMonthlyWorkload(userName, workMonth) {
  return request({
    url: '/system/workload/userMonthly',
    method: 'get',
    params: {
      userName,
      workMonth
    }
  })
}