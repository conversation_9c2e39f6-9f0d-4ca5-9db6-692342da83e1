-- ===== 修改project_participation表中的month字段 =====
-- 将month字段值为'2025-07'的记录改为'2025-06'
-- 注意：存在重复的用户-项目组合，需要处理冲突

-- ===== 第一步：数据备份 =====
-- 创建备份表
CREATE TABLE project_participation_backup_20250715 AS
SELECT * FROM project_participation WHERE month IN ('2025-06', '2025-07');

-- ===== 第二步：分析数据冲突 =====
-- 查看当前数据分布
SELECT
    '修改前数据分布' as step,
    month,
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_participation
WHERE month IN ('2025-06', '2025-07', '2025-08')
GROUP BY month
ORDER BY month;

-- 检查重复的用户-项目组合
SELECT
    '重复组合检查' as step,
    COUNT(*) as duplicate_combinations
FROM project_participation pp1
INNER JOIN project_participation pp2 ON pp1.user_name = pp2.user_name AND pp1.project_id = pp2.project_id
WHERE pp1.month = '2025-06' AND pp2.month = '2025-07';

-- ===== 第三步：预览要修改的数据 =====
SELECT 
    '即将修改的数据预览' as step,
    id, user_name, project_id, project_name, month, created_at
FROM project_participation 
WHERE month = '2025-07'
ORDER BY created_at
LIMIT 10;

-- ===== 第四步：执行修改 =====
-- 将month字段从'2025-07'改为'2025-06'
UPDATE project_participation 
SET month = '2025-06'
WHERE month = '2025-07';

-- ===== 第五步：验证修改结果 =====
SELECT 
    '修改后数据分布' as step,
    month, 
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_participation 
WHERE month IN ('2025-06', '2025-07', '2025-08') 
GROUP BY month 
ORDER BY month;

-- ===== 第六步：检查修改影响 =====
-- 检查修改了多少条记录
SELECT 
    '修改统计' as check_type,
    '原2025-07记录数' as metric,
    COUNT(*) as value
FROM project_participation_backup_20250715
WHERE month = '2025-07'
UNION ALL
SELECT 
    '修改统计' as check_type,
    '现2025-06记录数' as metric,
    COUNT(*) as value
FROM project_participation
WHERE month = '2025-06'
UNION ALL
SELECT 
    '修改统计' as check_type,
    '现2025-07记录数' as metric,
    COUNT(*) as value
FROM project_participation
WHERE month = '2025-07';

-- ===== 第七步：回滚方案（如果需要） =====
/*
-- 如果需要回滚修改，可以执行以下语句：

-- 方案1：从备份表恢复特定数据
UPDATE project_participation pp
INNER JOIN project_participation_backup_20250715 backup ON pp.id = backup.id
SET pp.month = backup.month
WHERE backup.month = '2025-07';

-- 方案2：完全回滚（删除后重新插入）
DELETE FROM project_participation WHERE month IN ('2025-06', '2025-07');
INSERT INTO project_participation SELECT * FROM project_participation_backup_20250715;
*/

-- ===== 第八步：清理备份表（确认修改成功后执行） =====
-- DROP TABLE project_participation_backup_20250715;

-- 显示完成时间
SELECT 
    '修改完成' as status,
    NOW() as completion_time,
    'project_participation表month字段修改完成' as result;
