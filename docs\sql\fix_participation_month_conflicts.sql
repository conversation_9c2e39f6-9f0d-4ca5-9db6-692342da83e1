-- ===== 处理project_participation表month字段冲突 =====
-- 问题：存在用户在同一项目上既有2025-06又有2025-07的记录
-- 解决方案：保留最新的记录，删除旧的记录，然后统一改为2025-06

-- ===== 第一步：数据备份 =====
CREATE TABLE project_participation_backup_20250715 AS 
SELECT * FROM project_participation WHERE month IN ('2025-06', '2025-07');

-- ===== 第二步：分析冲突数据 =====
-- 查看冲突统计
SELECT 
    '冲突数据统计' as analysis_type,
    COUNT(DISTINCT CONCAT(pp1.user_name, '-', pp1.project_id)) as conflict_combinations,
    COUNT(*) as total_conflict_records
FROM project_participation pp1
INNER JOIN project_participation pp2 ON pp1.user_name = pp2.user_name AND pp1.project_id = pp2.project_id
WHERE pp1.month = '2025-06' AND pp2.month = '2025-07';

-- 查看冲突详情（前10个）
SELECT 
    '冲突详情预览' as analysis_type,
    pp1.user_name,
    pp1.project_id,
    pp1.project_name,
    pp1.participation_rate as rate_2025_06,
    pp2.participation_rate as rate_2025_07,
    pp1.created_at as created_2025_06,
    pp2.created_at as created_2025_07,
    CASE WHEN pp2.created_at > pp1.created_at THEN '保留2025-07' ELSE '保留2025-06' END as recommendation
FROM project_participation pp1
INNER JOIN project_participation pp2 ON pp1.user_name = pp2.user_name AND pp1.project_id = pp2.project_id
WHERE pp1.month = '2025-06' AND pp2.month = '2025-07'
ORDER BY pp1.user_name, pp1.project_id
LIMIT 10;

-- ===== 第三步：方案一 - 保留最新记录，删除旧记录 =====
-- 3.1 创建临时表存储要保留的记录
/*
CREATE TEMPORARY TABLE records_to_keep AS
SELECT 
    user_name,
    project_id,
    MAX(created_at) as latest_created,
    CASE 
        WHEN MAX(created_at) = MAX(CASE WHEN month = '2025-07' THEN created_at END) THEN '2025-07'
        ELSE '2025-06'
    END as keep_month
FROM project_participation 
WHERE month IN ('2025-06', '2025-07')
GROUP BY user_name, project_id;

-- 3.2 删除冲突的旧记录
DELETE pp FROM project_participation pp
INNER JOIN records_to_keep rtk ON pp.user_name = rtk.user_name AND pp.project_id = rtk.project_id
WHERE pp.month IN ('2025-06', '2025-07')
AND NOT (pp.month = rtk.keep_month AND pp.created_at = rtk.latest_created);

-- 3.3 将所有剩余记录的month改为2025-06
UPDATE project_participation 
SET month = '2025-06'
WHERE month = '2025-07';

DROP TEMPORARY TABLE records_to_keep;
*/

-- ===== 第四步：方案二 - 合并精力分配比例 =====
-- 如果需要合并精力分配比例而不是简单保留最新记录
/*
-- 4.1 创建合并后的数据
CREATE TEMPORARY TABLE merged_participation AS
SELECT 
    user_name,
    project_id,
    project_name,
    AVG(participation_rate) as avg_participation_rate,  -- 或使用MAX取最大值
    MAX(created_at) as latest_created,
    MAX(updated_at) as latest_updated,
    MAX(dept_id) as dept_id,
    MAX(assigner_id) as assigner_id,
    MAX(assigner_name) as assigner_name,
    MAX(comments) as comments
FROM project_participation 
WHERE month IN ('2025-06', '2025-07')
GROUP BY user_name, project_id, project_name;

-- 4.2 删除原有冲突记录
DELETE FROM project_participation 
WHERE month IN ('2025-06', '2025-07');

-- 4.3 插入合并后的记录
INSERT INTO project_participation (
    user_name, project_id, project_name, participation_rate, month,
    dept_id, assigner_id, assigner_name, comments, created_at, updated_at
)
SELECT 
    user_name, project_id, project_name, avg_participation_rate, '2025-06',
    dept_id, assigner_id, assigner_name, comments, latest_created, latest_updated
FROM merged_participation;

DROP TEMPORARY TABLE merged_participation;
*/

-- ===== 第五步：验证修改结果 =====
-- 5.1 检查修改后的数据分布
SELECT 
    '修改后数据分布' as check_type,
    month, 
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_participation 
WHERE month IN ('2025-06', '2025-07', '2025-08') 
GROUP BY month 
ORDER BY month;

-- 5.2 检查是否还有重复数据
SELECT 
    '重复数据检查' as check_type,
    user_name,
    project_id,
    COUNT(*) as duplicate_count
FROM project_participation 
WHERE month = '2025-06'
GROUP BY user_name, project_id
HAVING COUNT(*) > 1;

-- 5.3 对比修改前后的记录数
SELECT 
    '记录数对比' as check_type,
    '修改前总数' as period,
    COUNT(*) as count
FROM project_participation_backup_20250715
WHERE month IN ('2025-06', '2025-07')
UNION ALL
SELECT 
    '记录数对比' as check_type,
    '修改后2025-06数' as period,
    COUNT(*) as count
FROM project_participation
WHERE month = '2025-06'
UNION ALL
SELECT 
    '记录数对比' as check_type,
    '修改后2025-07数' as period,
    COUNT(*) as count
FROM project_participation
WHERE month = '2025-07';

-- ===== 第六步：回滚方案 =====
/*
-- 如果需要回滚，可以执行以下语句：
DELETE FROM project_participation WHERE month IN ('2025-06', '2025-07');
INSERT INTO project_participation SELECT * FROM project_participation_backup_20250715;
*/

-- ===== 第七步：清理备份表 =====
-- DROP TABLE project_participation_backup_20250715;

-- 显示完成时间
SELECT 
    '处理完成' as status,
    NOW() as completion_time,
    '请选择合适的方案执行' as note;
