<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.QuotaGroupMapper">
    
    <resultMap type="QuotaGroup" id="QuotaGroupResult">
        <result property="id"    column="id"    />
        <result property="groupName"    column="group_name"    />
        <result property="groupCode"    column="group_code"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="QuotaGroupDept" id="QuotaGroupDeptResult">
        <result property="id"    column="id"    />
        <result property="groupId"    column="group_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="isPrimary"    column="is_primary"    />
        <result property="deptName"    column="dept_name"    />
        <result property="groupName"    column="group_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <resultMap type="QuotaGroupQuota" id="QuotaGroupQuotaResult">
        <result property="id"    column="id"    />
        <result property="groupId"    column="group_id"    />
        <result property="evaluationYear"    column="evaluation_year"    />
        <result property="totalEmployees"    column="total_employees"    />
        <result property="highScoreQuota"    column="high_score_quota"    />
        <result property="usedQuota"    column="used_quota"    />
        <result property="remainingQuota"    column="remaining_quota"    />
        <result property="groupName"    column="group_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQuotaGroupVo">
        select id, group_name, group_code, description, status, create_by, create_time, update_by, update_time from quota_group
    </sql>

    <select id="selectQuotaGroupList" parameterType="QuotaGroup" resultMap="QuotaGroupResult">
        <include refid="selectQuotaGroupVo"/>
        <where>  
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="groupCode != null  and groupCode != ''"> and group_code = #{groupCode}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectQuotaGroupById" parameterType="Long" resultMap="QuotaGroupResult">
        <include refid="selectQuotaGroupVo"/>
        where id = #{id}
    </select>

    <select id="selectQuotaGroupByDeptId" parameterType="Long" resultMap="QuotaGroupResult">
        select qg.id, qg.group_name, qg.group_code, qg.description, qg.status, 
               qg.create_by, qg.create_time, qg.update_by, qg.update_time 
        from quota_group qg
        inner join quota_group_dept qgd on qg.id = qgd.group_id
        where qgd.dept_id = #{deptId} and qg.status = '0'
    </select>
        
    <insert id="insertQuotaGroup" parameterType="QuotaGroup" useGeneratedKeys="true" keyProperty="id">
        insert into quota_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">group_name,</if>
            <if test="groupCode != null and groupCode != ''">group_code,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">#{groupName},</if>
            <if test="groupCode != null and groupCode != ''">#{groupCode},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateQuotaGroup" parameterType="QuotaGroup">
        update quota_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">group_name = #{groupName},</if>
            <if test="groupCode != null and groupCode != ''">group_code = #{groupCode},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQuotaGroupById" parameterType="Long">
        delete from quota_group where id = #{id}
    </delete>

    <delete id="deleteQuotaGroupByIds" parameterType="String">
        delete from quota_group where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 配额组部门关系相关SQL -->
    <select id="selectQuotaGroupDeptList" parameterType="QuotaGroupDept" resultMap="QuotaGroupDeptResult">
        select qgd.id, qgd.group_id, qgd.dept_id, qgd.is_primary, qgd.create_by, qgd.create_time,
               sd.dept_name, qg.group_name
        from quota_group_dept qgd
        left join sys_dept sd on qgd.dept_id = sd.dept_id
        left join quota_group qg on qgd.group_id = qg.id
        <where>
            <if test="groupId != null">and qgd.group_id = #{groupId}</if>
            <if test="deptId != null">and qgd.dept_id = #{deptId}</if>
            <if test="isPrimary != null and isPrimary != ''">and qgd.is_primary = #{isPrimary}</if>
        </where>
        order by qgd.is_primary desc, qgd.dept_id
    </select>

    <insert id="insertQuotaGroupDept" parameterType="QuotaGroupDept">
        insert into quota_group_dept(group_id, dept_id, is_primary, create_by, create_time)
        values (#{groupId}, #{deptId}, #{isPrimary}, #{createBy}, sysdate())
    </insert>

    <delete id="deleteQuotaGroupDeptByGroupId" parameterType="Long">
        delete from quota_group_dept where group_id = #{groupId}
    </delete>

    <!-- 配额组配额相关SQL -->
    <select id="selectQuotaGroupQuotaList" parameterType="QuotaGroupQuota" resultMap="QuotaGroupQuotaResult">
        select qgq.id, qgq.group_id, qgq.evaluation_year, qgq.total_employees, qgq.high_score_quota,
               qgq.used_quota, qgq.remaining_quota, qgq.create_by, qgq.create_time, qgq.update_by, qgq.update_time,
               qg.group_name
        from quota_group_quota qgq
        left join quota_group qg on qgq.group_id = qg.id
        <where>
            <if test="groupId != null">and qgq.group_id = #{groupId}</if>
            <if test="evaluationYear != null and evaluationYear != ''">and qgq.evaluation_year = #{evaluationYear}</if>
        </where>
        order by qgq.evaluation_year desc
    </select>

    <select id="selectQuotaGroupQuotaByGroupAndYear" resultMap="QuotaGroupQuotaResult">
        select qgq.id, qgq.group_id, qgq.evaluation_year, qgq.total_employees, qgq.high_score_quota,
               qgq.used_quota, qgq.remaining_quota, qgq.create_by, qgq.create_time, qgq.update_by, qgq.update_time,
               qg.group_name
        from quota_group_quota qgq
        left join quota_group qg on qgq.group_id = qg.id
        where qgq.group_id = #{groupId} and qgq.evaluation_year = #{year}
    </select>

    <insert id="insertQuotaGroupQuota" parameterType="QuotaGroupQuota">
        insert into quota_group_quota(group_id, evaluation_year, total_employees, high_score_quota, used_quota, remaining_quota, create_by, create_time)
        values (#{groupId}, #{evaluationYear}, #{totalEmployees}, #{highScoreQuota}, #{usedQuota}, #{remainingQuota}, #{createBy}, sysdate())
    </insert>

    <update id="updateQuotaGroupQuota" parameterType="QuotaGroupQuota">
        update quota_group_quota
        <trim prefix="SET" suffixOverrides=",">
            <if test="totalEmployees != null">total_employees = #{totalEmployees},</if>
            <if test="highScoreQuota != null">high_score_quota = #{highScoreQuota},</if>
            <if test="usedQuota != null">used_quota = #{usedQuota},</if>
            <if test="remainingQuota != null">remaining_quota = #{remainingQuota},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where group_id = #{groupId} and evaluation_year = #{evaluationYear}
    </update>

    <delete id="deleteQuotaGroupQuotaByGroupId" parameterType="Long">
        delete from quota_group_quota where group_id = #{groupId}
    </delete>

</mapper>
