package com.kc.system.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.kc.common.core.domain.entity.SysUser;
import com.kc.common.core.domain.entity.SysDept;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.kc.common.utils.DateUtils;
import com.kc.common.utils.SecurityUtils;
import com.kc.system.mapper.EmployeeBonusAllocationMapper;
import com.kc.system.domain.EmployeeBonusAllocation;
import com.kc.system.domain.dto.EmployeeBonusAllocationDTO;
import com.kc.system.service.IEmployeeBonusAllocationService;
import com.kc.system.service.IDeptBonusAllocationService;
import com.kc.system.service.ISysUserService;
import com.kc.system.service.ISysDeptService;
import com.kc.system.domain.DeptBonusAllocation;

/**
 * 员工奖金分配Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class EmployeeBonusAllocationServiceImpl implements IEmployeeBonusAllocationService 
{
    @Autowired
    private EmployeeBonusAllocationMapper employeeBonusAllocationMapper;

    @Autowired
    private IDeptBonusAllocationService deptBonusAllocationService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询员工奖金分配
     * 
     * @param id 员工奖金分配主键
     * @return 员工奖金分配
     */
    @Override
    public EmployeeBonusAllocation selectEmployeeBonusAllocationById(Long id)
    {
        return employeeBonusAllocationMapper.selectEmployeeBonusAllocationById(id);
    }

    /**
     * 查询员工奖金分配列表
     * 
     * @param employeeBonusAllocation 员工奖金分配
     * @return 员工奖金分配
     */
    @Override
    public List<EmployeeBonusAllocation> selectEmployeeBonusAllocationList(EmployeeBonusAllocation employeeBonusAllocation)
    {
        return employeeBonusAllocationMapper.selectEmployeeBonusAllocationList(employeeBonusAllocation);
    }

    /**
     * 根据用户ID和月份查询员工奖金分配
     */
    @Override
    public EmployeeBonusAllocation selectByUserIdAndMonth(Long userId, String allocationMonth)
    {
        return employeeBonusAllocationMapper.selectByUserIdAndMonth(userId, allocationMonth);
    }

    /**
     * 根据部门ID和月份查询员工奖金分配列表
     */
    @Override
    public List<EmployeeBonusAllocation> selectByDeptIdAndMonth(Long deptId, String allocationMonth)
    {
        return employeeBonusAllocationMapper.selectByDeptIdAndMonth(deptId, allocationMonth);
    }

    /**
     * 根据月份查询所有员工奖金分配
     */
    @Override
    public List<EmployeeBonusAllocation> selectByMonth(String allocationMonth)
    {
        return employeeBonusAllocationMapper.selectByMonth(allocationMonth);
    }

    /**
     * 根据分配者ID和月份查询员工奖金分配列表
     */
    @Override
    public List<EmployeeBonusAllocation> selectByAllocatorAndMonth(Long allocatorId, String allocationMonth)
    {
        return employeeBonusAllocationMapper.selectByAllocatorAndMonth(allocatorId, allocationMonth);
    }

    /**
     * 新增员工奖金分配
     * 
     * @param employeeBonusAllocation 员工奖金分配
     * @return 结果
     */
    @Override
    public int insertEmployeeBonusAllocation(EmployeeBonusAllocation employeeBonusAllocation)
    {
        employeeBonusAllocation.setCreateTime(DateUtils.getNowDate());
        employeeBonusAllocation.setCreateBy(SecurityUtils.getUsername());
        return employeeBonusAllocationMapper.insertEmployeeBonusAllocation(employeeBonusAllocation);
    }

    /**
     * 批量分配员工奖金
     */
    @Override
    @Transactional
    public int batchAllocateEmployeeBonus(EmployeeBonusAllocationDTO employeeBonusAllocationDTO)
    {
        Long deptBonusId = employeeBonusAllocationDTO.getDeptBonusId();
        String allocationMonth = employeeBonusAllocationDTO.getAllocationMonth();
        Long deptId = employeeBonusAllocationDTO.getDeptId();

        // 获取部门奖金分配信息
        DeptBonusAllocation deptBonusAllocation = deptBonusAllocationService.selectDeptBonusAllocationById(deptBonusId);
        if (deptBonusAllocation == null) {
            throw new RuntimeException("部门奖金分配不存在");
        }

        // 获取部门信息
        SysDept dept = deptService.selectDeptById(deptId);
        String deptName = dept != null ? dept.getDeptName() : "未知部门";

        // 计算总分配金额
        BigDecimal totalAllocateAmount = BigDecimal.ZERO;
        for (EmployeeBonusAllocationDTO.EmployeeBonusItem item : employeeBonusAllocationDTO.getEmployeeBonusList()) {
            totalAllocateAmount = totalAllocateAmount.add(item.getBonusAmount());
        }

        // 检查分配金额是否超过剩余奖金
        if (totalAllocateAmount.compareTo(deptBonusAllocation.getRemainingBonus()) > 0) {
            throw new RuntimeException("分配金额超过剩余奖金，剩余奖金：" + deptBonusAllocation.getRemainingBonus());
        }

        // 先删除该部门该月份的现有分配记录
        employeeBonusAllocationMapper.deleteByDeptBonusId(deptBonusId);

        List<EmployeeBonusAllocation> allocations = new ArrayList<>();
        String currentUser = SecurityUtils.getUsername();
        Long currentUserId = SecurityUtils.getUserId();
        Date now = DateUtils.getNowDate();

        for (EmployeeBonusAllocationDTO.EmployeeBonusItem item : employeeBonusAllocationDTO.getEmployeeBonusList()) {
            // 获取用户信息
            SysUser user = userService.selectUserById(item.getUserId());
            if (user == null) {
                throw new RuntimeException("用户不存在：" + item.getUserId());
            }

            EmployeeBonusAllocation allocation = new EmployeeBonusAllocation();
            allocation.setDeptBonusId(deptBonusId);
            allocation.setUserId(item.getUserId());
            allocation.setUserName(user.getUserName());
            allocation.setNickName(user.getNickName());
            allocation.setDeptId(deptId);
            allocation.setDeptName(deptName);
            allocation.setAllocationMonth(allocationMonth);
            allocation.setBonusAmount(item.getBonusAmount());
            allocation.setAllocationReason(item.getAllocationReason());
            allocation.setAllocatorId(currentUserId);
            allocation.setAllocatorName(currentUser);
            allocation.setCreateBy(currentUser);
            allocation.setCreateTime(now);

            allocations.add(allocation);
        }

        // 批量插入员工奖金分配记录
        int result = employeeBonusAllocationMapper.batchInsertEmployeeBonusAllocation(allocations);

        // 更新部门奖金分配状态
        BigDecimal newAllocatedBonus = deptBonusAllocation.getAllocatedBonus().add(totalAllocateAmount);
        BigDecimal newRemainingBonus = deptBonusAllocation.getTotalBonus().subtract(newAllocatedBonus);
        
        String newStatus = "1"; // 部分分配
        if (newRemainingBonus.compareTo(BigDecimal.ZERO) == 0) {
            newStatus = "2"; // 已完成
        }

        deptBonusAllocation.setAllocatedBonus(newAllocatedBonus);
        deptBonusAllocation.setRemainingBonus(newRemainingBonus);
        deptBonusAllocation.setAllocationStatus(newStatus);
        deptBonusAllocationService.updateAllocationStatus(deptBonusAllocation);

        return result;
    }

    /**
     * 修改员工奖金分配
     * 
     * @param employeeBonusAllocation 员工奖金分配
     * @return 结果
     */
    @Override
    public int updateEmployeeBonusAllocation(EmployeeBonusAllocation employeeBonusAllocation)
    {
        employeeBonusAllocation.setUpdateTime(DateUtils.getNowDate());
        employeeBonusAllocation.setUpdateBy(SecurityUtils.getUsername());
        return employeeBonusAllocationMapper.updateEmployeeBonusAllocation(employeeBonusAllocation);
    }

    /**
     * 批量删除员工奖金分配
     * 
     * @param ids 需要删除的员工奖金分配主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeBonusAllocationByIds(Long[] ids)
    {
        return employeeBonusAllocationMapper.deleteEmployeeBonusAllocationByIds(ids);
    }

    /**
     * 删除员工奖金分配信息
     * 
     * @param id 员工奖金分配主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeBonusAllocationById(Long id)
    {
        return employeeBonusAllocationMapper.deleteEmployeeBonusAllocationById(id);
    }

    /**
     * 根据部门奖金分配ID删除员工奖金分配
     */
    @Override
    @Transactional
    public int deleteByDeptBonusId(Long deptBonusId)
    {
        // 获取部门奖金分配信息
        DeptBonusAllocation deptBonusAllocation = deptBonusAllocationService.selectDeptBonusAllocationById(deptBonusId);
        if (deptBonusAllocation == null) {
            throw new RuntimeException("部门奖金分配不存在");
        }

        // 删除员工奖金分配记录
        int result = employeeBonusAllocationMapper.deleteByDeptBonusId(deptBonusId);

        // 重置部门奖金分配状态
        deptBonusAllocation.setAllocatedBonus(BigDecimal.ZERO);
        deptBonusAllocation.setRemainingBonus(deptBonusAllocation.getTotalBonus());
        deptBonusAllocation.setAllocationStatus("0"); // 未分配
        deptBonusAllocationService.updateAllocationStatus(deptBonusAllocation);

        return result;
    }

    /**
     * 根据月份删除员工奖金分配
     */
    @Override
    public int deleteByMonth(String allocationMonth)
    {
        return employeeBonusAllocationMapper.deleteByMonth(allocationMonth);
    }

    /**
     * 获取部门负责人可分配的员工列表
     */
    @Override
    public List<EmployeeBonusAllocation> getAvailableEmployeesForAllocation(Long deptId, String allocationMonth)
    {
        // 获取部门信息
        SysDept dept = deptService.selectDeptById(deptId);
        String deptName = dept != null ? dept.getDeptName() : "未知部门";

        // 获取部门所有用户
        SysUser queryUser = new SysUser();
        queryUser.setDeptId(deptId);
        List<SysUser> deptUsers = userService.selectUserList(queryUser);

        List<EmployeeBonusAllocation> availableEmployees = new ArrayList<>();
        for (SysUser user : deptUsers) {
            EmployeeBonusAllocation allocation = new EmployeeBonusAllocation();
            allocation.setUserId(user.getUserId());
            allocation.setUserName(user.getUserName());
            allocation.setNickName(user.getNickName());
            allocation.setDeptId(deptId);
            allocation.setDeptName(deptName);
            allocation.setAllocationMonth(allocationMonth);
            allocation.setBonusAmount(BigDecimal.ZERO);
            availableEmployees.add(allocation);
        }

        return availableEmployees;
    }
}
