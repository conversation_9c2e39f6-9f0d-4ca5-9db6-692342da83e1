-- ===== 将project_evaluation中2025-06的project_leader和manager评分改为2025-07 =====
-- 策略：保留最新记录，删除旧记录，然后修改月份

-- ===== 第一步：创建备份表 =====
CREATE TABLE project_evaluation_backup_update_20250715 AS 
SELECT * FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
AND evaluation_type IN ('project_leader', 'manager');

-- 验证备份
SELECT 
    '备份验证' as step,
    evaluation_month,
    evaluation_type,
    COUNT(*) as count
FROM project_evaluation_backup_update_20250715 
GROUP BY evaluation_month, evaluation_type 
ORDER BY evaluation_month, evaluation_type;

-- ===== 第二步：分析当前数据 =====
-- 查看待修改的数据统计
SELECT 
    '待修改数据统计' as step,
    evaluation_month,
    evaluation_type,
    COUNT(*) as count,
    MIN(updated_at) as earliest_updated,
    MAX(updated_at) as latest_updated
FROM project_evaluation 
WHERE evaluation_month = '2025-06' 
AND evaluation_type IN ('project_leader', 'manager')
GROUP BY evaluation_month, evaluation_type;

-- 查看冲突数据统计
SELECT 
    '冲突数据统计' as step,
    COUNT(*) as conflict_count,
    '同一评价人对同一被评价人在同一项目的重复评价' as description
FROM project_evaluation pe1
INNER JOIN project_evaluation pe2 ON 
    pe1.evaluator_id = pe2.evaluator_id 
    AND pe1.evaluatee_id = pe2.evaluatee_id 
    AND pe1.project_id = pe2.project_id 
    AND pe1.evaluation_type = pe2.evaluation_type
WHERE pe1.evaluation_month = '2025-06' 
AND pe2.evaluation_month = '2025-07'
AND pe1.evaluation_type IN ('project_leader', 'manager')
AND pe2.evaluation_type IN ('project_leader', 'manager');

-- ===== 第三步：创建要保留的记录列表 =====
-- 对于冲突的记录，保留更新时间最新的
CREATE TEMPORARY TABLE records_to_keep_evaluation AS
SELECT 
    evaluator_id,
    evaluatee_id,
    project_id,
    evaluation_type,
    MAX(updated_at) as latest_updated,
    CASE 
        WHEN MAX(updated_at) = MAX(CASE WHEN evaluation_month = '2025-06' THEN updated_at END) THEN '2025-06'
        WHEN MAX(updated_at) = MAX(CASE WHEN evaluation_month = '2025-07' THEN updated_at END) THEN '2025-07'
        ELSE '2025-06'  -- 默认保留2025-06
    END as keep_month
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07')
AND evaluation_type IN ('project_leader', 'manager')
GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type;

-- 查看保留策略统计
SELECT 
    '保留策略统计' as step,
    keep_month,
    COUNT(*) as count
FROM records_to_keep_evaluation 
GROUP BY keep_month;

-- ===== 第四步：删除冲突的旧记录 =====
-- 预览要删除的记录数量
SELECT 
    '即将删除的记录统计' as step,
    COUNT(*) as delete_count
FROM project_evaluation pe
INNER JOIN records_to_keep_evaluation rtk ON 
    pe.evaluator_id = rtk.evaluator_id 
    AND pe.evaluatee_id = rtk.evaluatee_id 
    AND pe.project_id = rtk.project_id 
    AND pe.evaluation_type = rtk.evaluation_type
WHERE pe.evaluation_month IN ('2025-06', '2025-07')
AND pe.evaluation_type IN ('project_leader', 'manager')
AND NOT (pe.evaluation_month = rtk.keep_month AND pe.updated_at = rtk.latest_updated);

-- 执行删除操作
DELETE pe FROM project_evaluation pe
INNER JOIN records_to_keep_evaluation rtk ON 
    pe.evaluator_id = rtk.evaluator_id 
    AND pe.evaluatee_id = rtk.evaluatee_id 
    AND pe.project_id = rtk.project_id 
    AND pe.evaluation_type = rtk.evaluation_type
WHERE pe.evaluation_month IN ('2025-06', '2025-07')
AND pe.evaluation_type IN ('project_leader', 'manager')
AND NOT (pe.evaluation_month = rtk.keep_month AND pe.updated_at = rtk.latest_updated);

-- ===== 第五步：将剩余的2025-06记录改为2025-07 =====
-- 预览要修改的记录
SELECT 
    '即将修改的记录统计' as step,
    evaluation_type,
    COUNT(*) as update_count
FROM project_evaluation 
WHERE evaluation_month = '2025-06' 
AND evaluation_type IN ('project_leader', 'manager')
GROUP BY evaluation_type;

-- 执行修改
UPDATE project_evaluation 
SET evaluation_month = '2025-07'
WHERE evaluation_month = '2025-06' 
AND evaluation_type IN ('project_leader', 'manager');

-- 清理临时表
DROP TEMPORARY TABLE records_to_keep_evaluation;

-- ===== 第六步：验证修改结果 =====
-- 检查修改后的数据分布
SELECT 
    '修改后数据分布' as step,
    evaluation_month,
    evaluation_type,
    COUNT(*) as count,
    MIN(updated_at) as earliest_updated,
    MAX(updated_at) as latest_updated
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
AND evaluation_type IN ('project_leader', 'manager')
GROUP BY evaluation_month, evaluation_type 
ORDER BY evaluation_month, evaluation_type;

-- 检查是否还有重复数据
SELECT 
    '重复数据检查' as step,
    CASE WHEN COUNT(*) = 0 THEN '✓ 无重复数据' 
         ELSE CONCAT('✗ 仍有', COUNT(*), '组重复数据') END as result
FROM (
    SELECT 
        evaluator_id, evaluatee_id, project_id, evaluation_type,
        COUNT(*) as cnt
    FROM project_evaluation 
    WHERE evaluation_month = '2025-07'
    AND evaluation_type IN ('project_leader', 'manager')
    GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type
    HAVING COUNT(*) > 1
) duplicates;

-- 确认2025-06的project_leader和manager数据已全部修改
SELECT 
    '2025-06数据检查' as step,
    evaluation_type,
    CASE WHEN COUNT(*) = 0 THEN '✓ 已全部修改为2025-07' 
         ELSE CONCAT('✗ 仍有', COUNT(*), '条未修改') END as result
FROM project_evaluation 
WHERE evaluation_month = '2025-06' 
AND evaluation_type IN ('project_leader', 'manager')
GROUP BY evaluation_type;

-- ===== 第七步：最终统计 =====
SELECT 
    '执行完成' as status,
    NOW() as completion_time,
    '2025-06的project_leader和manager评分已合并到2025-07' as result;

-- ===== 回滚说明 =====
/*
如果需要回滚，可以执行：
DELETE FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
AND evaluation_type IN ('project_leader', 'manager');

INSERT INTO project_evaluation 
SELECT * FROM project_evaluation_backup_update_20250715;
*/
