<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.HighScoreRecordMapper">
    
    <resultMap type="HighScoreRecord" id="HighScoreRecordResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="evaluationYear"    column="evaluation_year"    />
        <result property="evaluationMonth"    column="evaluation_month"    />
        <result property="score"    column="score"    />
        <result property="evaluatorId"    column="evaluator_id"    />
        <result property="evaluatorName"    column="evaluator_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectHighScoreRecordVo">
        select id, dept_id, user_id, user_name, nick_name, evaluation_year, evaluation_month, score, evaluator_id, evaluator_name, create_time, update_time from high_score_record
    </sql>

    <select id="selectHighScoreRecordList" parameterType="HighScoreRecord" resultMap="HighScoreRecordResult">
        <include refid="selectHighScoreRecordVo"/>
        <where>  
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="evaluationYear != null  and evaluationYear != ''"> and evaluation_year = #{evaluationYear}</if>
            <if test="evaluationMonth != null  and evaluationMonth != ''"> and evaluation_month = #{evaluationMonth}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="evaluatorId != null "> and evaluator_id = #{evaluatorId}</if>
            <if test="evaluatorName != null  and evaluatorName != ''"> and evaluator_name like concat('%', #{evaluatorName}, '%')</if>
        </where>
    </select>
    
    <select id="selectHighScoreRecordById" parameterType="Long" resultMap="HighScoreRecordResult">
        <include refid="selectHighScoreRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectByUserAndYear" resultMap="HighScoreRecordResult">
        <include refid="selectHighScoreRecordVo"/>
        where user_id = #{userId} and evaluation_year = #{evaluationYear}
    </select>
        
    <insert id="insertHighScoreRecord" parameterType="HighScoreRecord" useGeneratedKeys="true" keyProperty="id">
        insert into high_score_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="evaluationYear != null and evaluationYear != ''">evaluation_year,</if>
            <if test="evaluationMonth != null and evaluationMonth != ''">evaluation_month,</if>
            <if test="score != null">score,</if>
            <if test="evaluatorId != null">evaluator_id,</if>
            <if test="evaluatorName != null">evaluator_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="evaluationYear != null and evaluationYear != ''">#{evaluationYear},</if>
            <if test="evaluationMonth != null and evaluationMonth != ''">#{evaluationMonth},</if>
            <if test="score != null">#{score},</if>
            <if test="evaluatorId != null">#{evaluatorId},</if>
            <if test="evaluatorName != null">#{evaluatorName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHighScoreRecord" parameterType="HighScoreRecord">
        update high_score_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="evaluationYear != null and evaluationYear != ''">evaluation_year = #{evaluationYear},</if>
            <if test="evaluationMonth != null and evaluationMonth != ''">evaluation_month = #{evaluationMonth},</if>
            <if test="score != null">score = #{score},</if>
            <if test="evaluatorId != null">evaluator_id = #{evaluatorId},</if>
            <if test="evaluatorName != null">evaluator_name = #{evaluatorName},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHighScoreRecordById" parameterType="Long">
        delete from high_score_record where id = #{id}
    </delete>

    <delete id="deleteHighScoreRecordByIds" parameterType="String">
        delete from high_score_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="countByDeptAndYear" resultType="int">
        select count(*) from high_score_record
        where dept_id = #{deptId} and evaluation_year = #{evaluationYear}
    </select>

</mapper>
