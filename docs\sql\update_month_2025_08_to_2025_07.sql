-- ===== 将month为2025-08的数据改为2025-07 =====
-- 简单的月份修改操作

-- ===== 第一步：查看当前数据分布 =====
SELECT 
    '修改前数据分布' as step,
    month, 
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_participation 
WHERE month IN ('2025-06', '2025-07', '2025-08') 
GROUP BY month 
ORDER BY month;

-- ===== 第二步：创建备份（如果还没有的话） =====
-- 检查备份表是否存在
SELECT 
    '备份表检查' as step,
    CASE WHEN COUNT(*) > 0 THEN '备份表已存在' ELSE '需要创建备份表' END as status
FROM information_schema.tables 
WHERE table_schema = 'projectallocation' 
AND table_name = 'project_participation_backup_20250715';

-- 如果需要创建备份表，取消下面的注释
/*
CREATE TABLE project_participation_backup_20250715 AS 
SELECT * FROM project_participation WHERE month IN ('2025-06', '2025-07', '2025-08');
*/

-- ===== 第三步：检查是否会产生冲突 =====
-- 检查2025-08改为2025-07后是否会与现有2025-07数据冲突
SELECT 
    '冲突检查' as step,
    pp1.user_name,
    pp1.project_id,
    pp1.project_name,
    pp1.participation_rate as rate_2025_07,
    pp2.participation_rate as rate_2025_08,
    pp1.created_at as created_2025_07,
    pp2.created_at as created_2025_08
FROM project_participation pp1
INNER JOIN project_participation pp2 ON pp1.user_name = pp2.user_name AND pp1.project_id = pp2.project_id
WHERE pp1.month = '2025-07' AND pp2.month = '2025-08'
ORDER BY pp1.user_name, pp1.project_id;

-- 统计冲突数量
SELECT 
    '冲突统计' as step,
    COUNT(*) as conflict_count
FROM project_participation pp1
INNER JOIN project_participation pp2 ON pp1.user_name = pp2.user_name AND pp1.project_id = pp2.project_id
WHERE pp1.month = '2025-07' AND pp2.month = '2025-08';

-- ===== 第四步：预览要修改的数据 =====
SELECT 
    '即将修改的数据预览' as step,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_name) as affected_users,
    COUNT(DISTINCT project_id) as affected_projects
FROM project_participation 
WHERE month = '2025-08';

-- 查看前10条要修改的记录
SELECT 
    '修改数据样例' as step,
    id, user_name, project_id, project_name, participation_rate, month, created_at
FROM project_participation 
WHERE month = '2025-08'
ORDER BY created_at
LIMIT 10;

-- ===== 第五步：执行修改 =====
-- 将month从2025-08改为2025-07
UPDATE project_participation 
SET month = '2025-07'
WHERE month = '2025-08';

-- ===== 第六步：验证修改结果 =====
-- 检查修改后的数据分布
SELECT 
    '修改后数据分布' as step,
    month, 
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_participation 
WHERE month IN ('2025-06', '2025-07', '2025-08') 
GROUP BY month 
ORDER BY month;

-- 确认2025-08数据已全部修改
SELECT 
    '修改确认' as step,
    CASE WHEN COUNT(*) = 0 THEN '✓ 所有2025-08数据已改为2025-07' 
         ELSE CONCAT('✗ 仍有', COUNT(*), '条2025-08数据未修改') END as result
FROM project_participation 
WHERE month = '2025-08';

-- 检查是否产生了重复数据
SELECT 
    '重复数据检查' as step,
    user_name,
    project_id,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(DISTINCT participation_rate) as rates,
    GROUP_CONCAT(DISTINCT created_at) as create_times
FROM project_participation 
WHERE month = '2025-07'
GROUP BY user_name, project_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- ===== 第七步：最终统计 =====
SELECT 
    '最终统计' as step,
    '修改完成' as status,
    NOW() as completion_time,
    '所有2025-08数据已改为2025-07' as result;

-- ===== 回滚说明 =====
/*
如果需要回滚2025-08的修改，可以执行：
UPDATE project_participation 
SET month = '2025-08'
WHERE month = '2025-07' 
AND created_at >= '2025-07-09';  -- 根据实际的2025-08数据创建时间调整

或者使用备份表完全回滚：
DELETE FROM project_participation WHERE month IN ('2025-06', '2025-07', '2025-08');
INSERT INTO project_participation SELECT * FROM project_participation_backup_20250715;
*/
