import request from '@/utils/request'

// 查询评价系统配置列表
export function listEvaluationConfig(query) {
  return request({
    url: '/system/evaluation_config/list',
    method: 'get',
    params: query
  })
}

// 查询评价系统配置详细
export function getEvaluationConfig(id) {
  return request({
    url: '/system/evaluation_config/' + id,
    method: 'get'
  })
}

// 根据配置类型和月份获取配置
export function getEvaluationConfigByTypeAndMonth(configType, month) {
  return request({
    url: `/system/evaluation_config/getByTypeAndMonth/${configType}/${month}`,
    method: 'get'
  })
}

// 检查操作是否允许
export function checkOperationAllowed(configType, month) {
  return request({
    url: `/system/evaluation_config/checkOperationAllowed/${configType}/${month}`,
    method: 'get'
  })
}

// 新增评价系统配置
export function addEvaluationConfig(data) {
  return request({
    url: '/system/evaluation_config',
    method: 'post',
    data: data
  })
}

// 修改评价系统配置
export function updateEvaluationConfig(data) {
  return request({
    url: '/system/evaluation_config',
    method: 'put',
    data: data
  })
}

// 删除评价系统配置
export function delEvaluationConfig(id) {
  return request({
    url: '/system/evaluation_config/' + id,
    method: 'delete'
  })
} 