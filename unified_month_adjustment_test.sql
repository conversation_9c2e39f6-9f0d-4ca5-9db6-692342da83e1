-- 统一月份调整方案测试脚本
-- 验证后端统一处理月份+1的解决方案

-- ===== 测试环境说明 =====
SELECT '===== 统一月份调整方案测试 =====' as test_section;

SELECT 
    '方案说明' as item,
    '后端统一处理：精力分配和工作量归集都在保存时月份+1' as description
UNION ALL
SELECT 
    '前端逻辑',
    '前端传递原始月份，后端统一调整'
UNION ALL
SELECT 
    '查询逻辑',
    '前端查询时使用+1后的月份'
UNION ALL
SELECT 
    '预期效果',
    '精力分配和工作量归集月份完全一致';

-- ===== 测试前清理 =====
SELECT '===== 测试前数据状态 =====' as test_section;

-- 查看最近的数据，检查是否还有月份不一致的情况
SELECT 
    '最近数据一致性检查' as check_type,
    pp.user_name,
    pp.project_id,
    pp.month as effort_month,
    pw.work_month as workload_month,
    CASE 
        WHEN pp.month = pw.work_month THEN '一致 ✅'
        ELSE CONCAT('不一致 ❌ (差值: ', 
                   TIMESTAMPDIFF(MONTH, 
                                STR_TO_DATE(CONCAT(pw.work_month, '-01'), '%Y-%m-%d'),
                                STR_TO_DATE(CONCAT(pp.month, '-01'), '%Y-%m-%d')), 
                   '个月)')
    END as consistency_status,
    pp.created_at
FROM project_participation pp
LEFT JOIN project_workload pw ON pp.user_name = pw.user_name 
    AND pp.project_id = pw.source_effort_project_id
    AND ABS(TIMESTAMPDIFF(MINUTE, pp.created_at, pw.created_at)) <= 5  -- 5分钟内的数据认为是同一次提交
WHERE pp.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
ORDER BY pp.created_at DESC
LIMIT 10;

-- ===== 月份计算验证 =====
SELECT '===== 月份计算验证 =====' as test_section;

-- 验证月份+1的计算逻辑
SELECT 
    '月份+1计算测试' as test_name,
    input_month,
    DATE_FORMAT(DATE_ADD(STR_TO_DATE(CONCAT(input_month, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m') as output_month,
    expected_output
FROM (
    SELECT '2024-11' as input_month, '2024-12' as expected_output
    UNION ALL SELECT '2024-12', '2025-01'
    UNION ALL SELECT '2025-01', '2025-02'
    UNION ALL SELECT '2025-06', '2025-07'
) test_cases;

-- ===== 业务场景验证 =====
SELECT '===== 业务场景验证 =====' as test_section;

-- 场景1：6月30号前填报
SELECT 
    '场景1：6月填报' as scenario,
    '用户在6月30号前填报' as action,
    '前端传递2025-06' as frontend_data,
    '后端调整为2025-07' as backend_processing,
    '数据库存储2025-07' as database_storage,
    '7月10号评分查询2025-07' as evaluation_query,
    '能查到完整数据' as result;

-- 场景2：跨年度填报
SELECT 
    '场景2：跨年填报' as scenario,
    '用户在12月30号前填报' as action,
    '前端传递2024-12' as frontend_data,
    '后端调整为2025-01' as backend_processing,
    '数据库存储2025-01' as database_storage,
    '1月10号评分查询2025-01' as evaluation_query,
    '能查到完整数据' as result;

-- ===== 数据一致性检查 =====
SELECT '===== 数据一致性检查 =====' as test_section;

-- 统计月份一致性
SELECT 
    '月份一致性统计' as stat_type,
    SUM(CASE WHEN pp.month = pw.work_month THEN 1 ELSE 0 END) as consistent_count,
    SUM(CASE WHEN pp.month != pw.work_month THEN 1 ELSE 0 END) as inconsistent_count,
    COUNT(*) as total_count,
    ROUND(SUM(CASE WHEN pp.month = pw.work_month THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as consistency_rate
FROM project_participation pp
JOIN project_workload pw ON pp.user_name = pw.user_name 
    AND pp.project_id = pw.source_effort_project_id
WHERE pp.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY);

-- ===== 查询逻辑验证 =====
SELECT '===== 查询逻辑验证 =====' as test_section;

-- 模拟前端查询逻辑
SET @current_month = '2025-06';  -- 假设当前是6月
SET @query_month = DATE_FORMAT(DATE_ADD(STR_TO_DATE(CONCAT(@current_month, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m');

SELECT 
    '前端查询逻辑模拟' as test_type,
    @current_month as display_month,
    @query_month as actual_query_month,
    '查询+1后的月份' as logic_description;

-- 验证能查到的数据
SELECT 
    '查询结果验证' as test_type,
    COUNT(DISTINCT pp.user_name) as effort_users,
    COUNT(DISTINCT pw.user_name) as workload_users,
    CASE 
        WHEN COUNT(DISTINCT pp.user_name) = COUNT(DISTINCT pw.user_name) THEN '用户数一致 ✅'
        ELSE '用户数不一致 ❌'
    END as user_consistency
FROM project_participation pp
FULL OUTER JOIN project_workload pw ON pp.month = pw.work_month
WHERE pp.month = @query_month OR pw.work_month = @query_month;

-- ===== 导入上月数据验证 =====
SELECT '===== 导入上月数据验证 =====' as test_section;

-- 模拟导入上月数据的查询
SET @current_display_month = '2025-07';  -- 当前显示7月
SET @last_display_month = '2025-06';     -- 上月显示6月
SET @import_query_month = DATE_FORMAT(DATE_ADD(STR_TO_DATE(CONCAT(@last_display_month, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m');

SELECT 
    '导入上月数据模拟' as test_type,
    @current_display_month as current_display,
    @last_display_month as last_display,
    @import_query_month as import_query_month,
    '查询7月数据（6月填报的）' as logic_description;

-- 验证可导入的数据
SELECT 
    '可导入数据统计' as test_type,
    COUNT(DISTINCT pp.user_name) as effort_users,
    COUNT(DISTINCT pw.user_name) as workload_users,
    COUNT(DISTINCT pp.project_id) as effort_projects,
    COUNT(DISTINCT pw.source_effort_project_id) as workload_projects
FROM project_participation pp
FULL OUTER JOIN project_workload pw ON pp.month = pw.work_month 
    AND pp.user_name = pw.user_name
    AND pp.project_id = pw.source_effort_project_id
WHERE pp.month = @import_query_month OR pw.work_month = @import_query_month;

-- ===== 评分查询验证 =====
SELECT '===== 评分查询验证 =====' as test_section;

-- 模拟评分时的查询
SET @evaluation_month = '2025-07';  -- 7月进行评分

SELECT 
    '评分查询模拟' as test_type,
    @evaluation_month as evaluation_month,
    '查询7月的精力分配和工作量归集数据' as query_description,
    '这些数据是6月30号前填报的' as data_source;

-- 统计评分可用的数据
SELECT 
    '评分可用数据统计' as test_type,
    COUNT(DISTINCT pp.user_name) as users_with_effort,
    COUNT(DISTINCT pw.user_name) as users_with_workload,
    COUNT(DISTINCT pp.project_id) as projects_with_effort,
    COUNT(DISTINCT pw.source_effort_project_id) as projects_with_workload,
    SUM(pp.participation_rate) as total_effort_rate,
    AVG(pw.involvement) as avg_workload_involvement
FROM project_participation pp
FULL OUTER JOIN project_workload pw ON pp.month = pw.work_month 
    AND pp.user_name = pw.user_name
    AND pp.project_id = pw.source_effort_project_id
WHERE pp.month = @evaluation_month OR pw.work_month = @evaluation_month;

-- ===== 数据完整性检查 =====
SELECT '===== 数据完整性检查 =====' as test_section;

-- 检查孤立的精力分配数据
SELECT 
    '孤立精力分配检查' as check_type,
    COUNT(*) as orphan_effort_count,
    '没有对应工作量归集的精力分配记录' as description
FROM project_participation pp
LEFT JOIN project_workload pw ON pp.user_name = pw.user_name 
    AND pp.month = pw.work_month
    AND pp.project_id = pw.source_effort_project_id
WHERE pw.id IS NULL
AND pp.created_at >= DATE_SUB(NOW(), INTERVAL 3 DAY);

-- 检查孤立的工作量归集数据
SELECT 
    '孤立工作量归集检查' as check_type,
    COUNT(*) as orphan_workload_count,
    '没有对应精力分配的工作量归集记录' as description
FROM project_workload pw
LEFT JOIN project_participation pp ON pw.user_name = pp.user_name 
    AND pw.work_month = pp.month
    AND pw.source_effort_project_id = pp.project_id
WHERE pp.id IS NULL
AND pw.created_at >= DATE_SUB(NOW(), INTERVAL 3 DAY);

-- ===== 性能影响评估 =====
SELECT '===== 性能影响评估 =====' as test_section;

-- 查询响应时间测试（模拟）
SELECT 
    '性能评估' as test_type,
    '月份调整对查询性能无影响' as performance_impact,
    '索引仍然有效' as index_effectiveness,
    '查询逻辑简单清晰' as query_complexity;

-- ===== 测试建议 =====
SELECT '===== 测试建议 =====' as test_section;

SELECT 
    '测试步骤' as step_type,
    '1. 重启应用使后端修改生效' as step_1,
    '2. 清除浏览器缓存' as step_2,
    '3. 进行完整的精力分配填报测试' as step_3,
    '4. 验证数据库中月份一致性' as step_4,
    '5. 测试评分界面数据显示' as step_5,
    '6. 测试导入上月数据功能' as step_6;

SELECT 
    '验证要点' as point_type,
    '精力分配和工作量归集月份必须完全一致' as point_1,
    '前端查询能正确显示数据' as point_2,
    '评分时能获取完整数据' as point_3,
    '导入功能正常工作' as point_4,
    '用户体验流畅无异常' as point_5;

-- ===== 测试结果总结 =====
SELECT '===== 测试结果总结 =====' as test_section;

SELECT 
    '统一方案优势' as summary_type,
    '后端统一控制，逻辑清晰' as advantage_1,
    '前端代码简化，维护容易' as advantage_2,
    '数据一致性得到保证' as advantage_3,
    '彻底解决填报周期问题' as advantage_4;

-- 显示测试完成时间
SELECT 
    '测试完成' as status,
    NOW() as completion_time,
    '统一月份调整方案验证完成' as result,
    '等待重启应用后进行实际验证' as next_step;
