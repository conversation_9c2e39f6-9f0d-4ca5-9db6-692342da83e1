package com.kc.system.service;

import java.util.List;
import com.kc.system.domain.QuotaGroup;
import com.kc.system.domain.QuotaGroupDept;
import com.kc.system.domain.QuotaGroupQuota;

/**
 * 配额组Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public interface IQuotaGroupService 
{
    /**
     * 查询配额组
     * 
     * @param id 配额组主键
     * @return 配额组
     */
    public QuotaGroup selectQuotaGroupById(Long id);

    /**
     * 查询配额组列表
     * 
     * @param quotaGroup 配额组
     * @return 配额组集合
     */
    public List<QuotaGroup> selectQuotaGroupList(QuotaGroup quotaGroup);

    /**
     * 新增配额组
     * 
     * @param quotaGroup 配额组
     * @return 结果
     */
    public int insertQuotaGroup(QuotaGroup quotaGroup);

    /**
     * 修改配额组
     * 
     * @param quotaGroup 配额组
     * @return 结果
     */
    public int updateQuotaGroup(QuotaGroup quotaGroup);

    /**
     * 批量删除配额组
     * 
     * @param ids 需要删除的配额组主键集合
     * @return 结果
     */
    public int deleteQuotaGroupByIds(Long[] ids);

    /**
     * 删除配额组信息
     * 
     * @param id 配额组主键
     * @return 结果
     */
    public int deleteQuotaGroupById(Long id);

    /**
     * 根据部门ID查询配额组
     * 
     * @param deptId 部门ID
     * @return 配额组
     */
    public QuotaGroup getQuotaGroupByDeptId(Long deptId);

    /**
     * 检查部门是否属于配额组
     * 
     * @param deptId 部门ID
     * @return 是否属于配额组
     */
    public boolean isDeptInQuotaGroup(Long deptId);

    /**
     * 获取配额组的配额信息
     * 
     * @param deptId 部门ID
     * @param year 年度
     * @return 配额组配额信息
     */
    public QuotaGroupQuota getQuotaGroupQuotaByDept(Long deptId, String year);

    /**
     * 检查配额组配额是否可用
     * 
     * @param deptId 部门ID
     * @param year 年度
     * @param requestCount 请求数量
     * @return 是否可用
     */
    public boolean checkQuotaGroupQuotaAvailable(Long deptId, String year, int requestCount);

    /**
     * 使用配额组配额
     * 
     * @param deptId 部门ID
     * @param year 年度
     * @param count 使用数量
     * @return 是否成功
     */
    public boolean useQuotaGroupQuota(Long deptId, String year, int count);

    /**
     * 释放配额组配额
     * 
     * @param deptId 部门ID
     * @param year 年度
     * @param count 释放数量
     * @return 是否成功
     */
    public boolean releaseQuotaGroupQuota(Long deptId, String year, int count);

    /**
     * 初始化配额组年度配额
     * 
     * @param groupId 配额组ID
     * @param year 年度
     * @return 配额信息
     */
    public QuotaGroupQuota initQuotaGroupQuota(Long groupId, String year);

    /**
     * 查询配额组部门关系列表
     * 
     * @param quotaGroupDept 配额组部门关系
     * @return 配额组部门关系集合
     */
    public List<QuotaGroupDept> selectQuotaGroupDeptList(QuotaGroupDept quotaGroupDept);

    /**
     * 查询配额组配额列表
     * 
     * @param quotaGroupQuota 配额组配额
     * @return 配额组配额集合
     */
    public List<QuotaGroupQuota> selectQuotaGroupQuotaList(QuotaGroupQuota quotaGroupQuota);

    /**
     * 新增配额组配额
     * 
     * @param quotaGroupQuota 配额组配额
     * @return 结果
     */
    public int insertQuotaGroupQuota(QuotaGroupQuota quotaGroupQuota);

    /**
     * 修改配额组配额
     *
     * @param quotaGroupQuota 配额组配额
     * @return 结果
     */
    public int updateQuotaGroupQuota(QuotaGroupQuota quotaGroupQuota);

    /**
     * 获取配额组在指定年度的实际使用配额数量
     *
     * @param groupId 配额组ID
     * @param year 年度
     * @return 实际使用数量
     */
    public int getActualUsedQuotaCount(Long groupId, String year);
}
