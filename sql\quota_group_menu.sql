-- 配额组管理菜单和权限配置SQL

-- 1. 插入配额组管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('配额组管理', 2000, 7, 'quotaGroup', 'system/quotaGroup/index', '', 1, 0, 'C', '0', '0', 'system:quotaGroup:list', 'peoples', 'admin', sysdate(), '', null, '配额组管理菜单');

-- 获取刚插入的菜单ID
SET @menu_id = LAST_INSERT_ID();

-- 2. 插入配额组管理的子菜单（按钮权限）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('配额组查询', @menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:query', '#', 'admin', sysdate(), '', null, ''),
('配额组新增', @menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:add', '#', 'admin', sysdate(), '', null, ''),
('配额组修改', @menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:edit', '#', 'admin', sysdate(), '', null, ''),
('配额组删除', @menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:remove', '#', 'admin', sysdate(), '', null, ''),
('配额组导出', @menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:export', '#', 'admin', sysdate(), '', null, ''),
('配额组管理', @menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:manage', '#', 'admin', sysdate(), '', null, '');

-- 获取子菜单ID
SET @query_id = LAST_INSERT_ID() - 5;
SET @add_id = LAST_INSERT_ID() - 4;
SET @edit_id = LAST_INSERT_ID() - 3;
SET @remove_id = LAST_INSERT_ID() - 2;
SET @export_id = LAST_INSERT_ID() - 1;
SET @manage_id = LAST_INSERT_ID();

-- 3. 为管理员角色分配配额组管理权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(1, @menu_id),
(1, @query_id),
(1, @add_id),
(1, @edit_id),
(1, @remove_id),
(1, @export_id),
(1, @manage_id);

-- 4. 为薪酬管理员角色分配配额组管理权限（假设角色ID为3，请根据实际情况调整）
-- 如果没有薪酬管理员角色，可以先创建
INSERT INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark)
VALUES ('薪酬管理员', 'salary_admin', 4, '1', 1, 1, '0', '0', 'admin', sysdate(), '薪酬管理员角色');

SET @salary_admin_role_id = LAST_INSERT_ID();

-- 为薪酬管理员分配配额组管理权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(@salary_admin_role_id, @menu_id),
(@salary_admin_role_id, @query_id),
(@salary_admin_role_id, @add_id),
(@salary_admin_role_id, @edit_id),
(@salary_admin_role_id, @remove_id),
(@salary_admin_role_id, @export_id),
(@salary_admin_role_id, @manage_id);

-- 5. 查看当前菜单结构（用于验证）
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    m.order_num,
    m.path,
    m.perms,
    m.menu_type,
    m.visible,
    m.status
FROM sys_menu m 
WHERE m.menu_name LIKE '%配额组%' OR m.parent_id IN (
    SELECT menu_id FROM sys_menu WHERE menu_name = '配额组管理'
)
ORDER BY m.parent_id, m.order_num;

-- 6. 查看角色权限分配情况（用于验证）
SELECT 
    r.role_name,
    m.menu_name,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_name LIKE '%配额组%'
ORDER BY r.role_name, m.menu_name;

-- 注意事项：
-- 1. 请根据实际的角色ID调整上述SQL中的role_id值
-- 2. 请根据实际的父菜单ID调整parent_id值（这里假设系统管理的parent_id为2000）
-- 3. 如果需要为其他角色分配权限，请参考上述模式添加相应的INSERT语句
-- 4. 执行前请备份数据库，确保可以回滚
-- 5. 建议在测试环境先验证SQL的正确性
