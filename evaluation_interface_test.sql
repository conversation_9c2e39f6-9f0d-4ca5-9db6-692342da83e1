-- 评分界面精力分配数据查询测试脚本
-- 验证修改后的查询逻辑是否正确

-- ===== 测试环境说明 =====
SELECT '===== 评分界面精力分配数据查询测试 =====' as test_section;

SELECT 
    '测试目标' as item,
    '验证评分界面能正确显示上个月的精力分配数据' as description
UNION ALL
SELECT 
    '问题场景',
    '12月10号评分时，查询12月精力分配数据为空，应该查询11月数据'
UNION ALL
SELECT 
    '解决方案',
    '修改getUserProjectEffortDetails等查询，让评分界面查询上个月数据';

-- ===== 测试1：getUserProjectEffortDetails查询验证 =====
SELECT '===== 测试1：getUserProjectEffortDetails查询验证 =====' as test_section;

-- 模拟原查询（修改前）
SELECT 
    '修改前查询' as query_type,
    '2024-12' as evaluation_month,
    COUNT(*) as record_count
FROM project_participation pp
INNER JOIN sys_user su ON pp.user_name = su.user_name
INNER JOIN project_info pi ON pp.project_id = pi.id
WHERE su.user_id = 1  -- 替换为实际存在的用户ID
AND pp.month = '2024-12'
AND pp.participation_rate > 0;

-- 模拟新查询（修改后）
SELECT 
    '修改后查询' as query_type,
    '2024-12' as evaluation_month,
    DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT('2024-12', '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m') as actual_query_month,
    COUNT(*) as record_count
FROM project_participation pp
INNER JOIN sys_user su ON pp.user_name = su.user_name
INNER JOIN project_info pi ON pp.project_id = pi.id
WHERE su.user_id = 1  -- 替换为实际存在的用户ID
AND pp.month = DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT('2024-12', '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m')
AND pp.participation_rate > 0;

-- ===== 测试2：checkUserHasProjectEffort查询验证 =====
SELECT '===== 测试2：checkUserHasProjectEffort查询验证 =====' as test_section;

-- 修改前查询
SELECT 
    '修改前检查' as check_type,
    COUNT(1) > 0 as has_effort,
    COUNT(1) as effort_count
FROM project_participation pp
INNER JOIN sys_user su ON pp.user_name = su.user_name
WHERE su.user_id = 1  -- 替换为实际存在的用户ID
AND pp.month = '2024-12'
AND pp.participation_rate > 0;

-- 修改后查询
SELECT 
    '修改后检查' as check_type,
    COUNT(1) > 0 as has_effort,
    COUNT(1) as effort_count,
    DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT('2024-12', '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m') as actual_query_month
FROM project_participation pp
INNER JOIN sys_user su ON pp.user_name = su.user_name
WHERE su.user_id = 1  -- 替换为实际存在的用户ID
AND pp.month = DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT('2024-12', '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m')
AND pp.participation_rate > 0;

-- ===== 测试3：月份计算验证 =====
SELECT '===== 测试3：月份计算验证 =====' as test_section;

-- 测试各种月份的计算
SELECT 
    '月份计算测试' as test_name,
    '2024-01' as evaluation_month,
    DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT('2024-01', '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m') as query_month,
    '应该是2023-12' as expected
UNION ALL
SELECT 
    '月份计算测试',
    '2024-06',
    DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT('2024-06', '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m'),
    '应该是2024-05'
UNION ALL
SELECT 
    '月份计算测试',
    '2024-12',
    DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT('2024-12', '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m'),
    '应该是2024-11';

-- ===== 测试4：实际数据分布验证 =====
SELECT '===== 测试4：实际数据分布验证 =====' as test_section;

-- 查看精力分配数据的月份分布
SELECT 
    '精力分配数据分布' as data_type,
    month,
    COUNT(*) as record_count,
    COUNT(DISTINCT user_name) as user_count
FROM project_participation 
WHERE month IN ('2024-10', '2024-11', '2024-12', '2025-01')
GROUP BY month
ORDER BY month;

-- ===== 测试5：评分界面场景模拟 =====
SELECT '===== 测试5：评分界面场景模拟 =====' as test_section;

-- 模拟项目负责人评分界面查询用户精力分配
-- 场景：2024年12月10号，项目负责人要对用户进行评分
SET @evaluation_month = '2024-12';
SET @user_id = 1; -- 替换为实际存在的用户ID

-- 修改后的查询逻辑
SELECT 
    '项目负责人评分界面' as interface_type,
    @evaluation_month as evaluation_month,
    pp.project_id,
    pi.project_name,
    pp.participation_rate,
    pp.month as effort_month,
    '这是上个月的精力分配数据' as note
FROM project_participation pp
INNER JOIN sys_user su ON pp.user_name = su.user_name
INNER JOIN project_info pi ON pp.project_id = pi.id
WHERE su.user_id = @user_id
AND pp.month = DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(@evaluation_month, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m')
AND pp.participation_rate > 0
ORDER BY pp.project_id
LIMIT 5;

-- ===== 测试6：机构负责人评分界面场景 =====
SELECT '===== 测试6：机构负责人评分界面场景 =====' as test_section;

-- 模拟机构负责人查看部门成员的精力分配情况
SELECT 
    '机构负责人评分界面' as interface_type,
    su.user_id,
    su.nick_name,
    COUNT(pp.project_id) as project_count,
    SUM(pp.participation_rate) as total_effort,
    GROUP_CONCAT(DISTINCT pi.project_name SEPARATOR ', ') as project_names
FROM project_participation pp
INNER JOIN sys_user su ON pp.user_name = su.user_name
INNER JOIN project_info pi ON pp.project_id = pi.id
WHERE pp.month = DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT('2024-12', '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m')
AND pp.participation_rate > 0
AND su.dept_id = 100  -- 替换为实际的部门ID
GROUP BY su.user_id, su.nick_name
ORDER BY su.nick_name
LIMIT 10;

-- ===== 测试7：API调用验证 =====
SELECT '===== 测试7：API调用验证建议 =====' as test_section;

SELECT 
    'API测试建议' as test_type,
    '/system/evaluation/user/project/{userId}?evaluationMonth=2024-12' as api_endpoint,
    '应该返回基于2024-11精力分配的项目列表' as expected_result
UNION ALL
SELECT 
    'API测试建议',
    '/system/participation/userEffort?userName=xxx&month=2024-12',
    '如果用于评分界面，应该返回2024-11的数据'
UNION ALL
SELECT 
    'API测试建议',
    'getUserProjectEffortDetails方法',
    '传入evaluationMonth=2024-12，应该查询2024-11的数据';

-- ===== 测试8：边界情况测试 =====
SELECT '===== 测试8：边界情况测试 =====' as test_section;

-- 测试跨年度情况
SELECT 
    '跨年度测试' as test_name,
    '2024-01' as evaluation_month,
    DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT('2024-01', '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m') as query_month,
    '跨年度：2024-01评分查询2023-12数据' as scenario;

-- 测试数据是否存在
SELECT 
    '数据存在性检查' as check_type,
    '2023-12' as target_month,
    COUNT(*) as record_count,
    CASE WHEN COUNT(*) > 0 THEN '有数据' ELSE '无数据' END as data_status
FROM project_participation 
WHERE month = '2023-12';

-- ===== 测试结果总结 =====
SELECT '===== 测试结果总结 =====' as test_section;

SELECT 
    '修改效果' as summary_type,
    '评分界面查询精力分配数据时自动查询上个月' as effect_1,
    '解决了填报周期不匹配的问题' as effect_2,
    '项目负责人和机构负责人能看到已完成的精力分配' as effect_3;

SELECT 
    '验证要点' as summary_type,
    '确认getUserProjectEffortDetails查询上月数据' as point_1,
    '确认checkUserHasProjectEffort检查上月数据' as point_2,
    '确认Java代码中的查询也使用上月数据' as point_3;

-- 显示测试完成时间
SELECT 
    '测试完成' as status,
    NOW() as completion_time,
    '评分界面精力分配查询修改验证完成' as result;
