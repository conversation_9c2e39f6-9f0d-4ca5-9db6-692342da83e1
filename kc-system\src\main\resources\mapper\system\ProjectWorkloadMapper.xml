<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.ProjectWorkloadMapper">
    
    <resultMap type="ProjectWorkload" id="ProjectWorkloadResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="userName"    column="user_name"    />
        <result property="workMonth"    column="work_month"    />
        <result property="involvement"    column="involvement"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="sourceEffortProjectId"    column="source_effort_project_id"    />
        <result property="projectName" column="projectName" />
        <result property="nickName" column="nickName" />
        <result property="role" column="role" />
    </resultMap>

    <sql id="selectProjectWorkloadVo">
        select id, project_id, user_name, work_month, involvement, created_at, updated_at, source_effort_project_id from project_workload
    </sql>

    <select id="selectProjectWorkloadList" parameterType="ProjectWorkload" resultMap="ProjectWorkloadResult">
        SELECT DISTINCT
            pw.id,
            pw.project_id,
            pw.user_name,
            pw.work_month,
            pw.involvement,
            pw.created_at,
            pw.updated_at,
            pw.source_effort_project_id,
            p.project_name as projectName,
            u.nick_name as nickName,
            pm.role as role
        FROM
            project_workload pw
            INNER JOIN project_info p ON pw.project_id = p.id
            INNER JOIN sys_user u ON pw.user_name = u.user_name
            LEFT JOIN project_members pm ON pw.source_effort_project_id = pm.project_id AND pw.user_name = pm.user_name
        <where>
            <if test="projectId != null"> AND pw.project_id = #{projectId}</if>
            <if test="userName != null and userName != ''"> AND pw.user_name = #{userName}</if>
            <if test="workMonth != null and workMonth != ''"> AND pw.work_month = #{workMonth}</if>
        </where>
        ORDER BY
            u.nick_name,
            p.project_name,
            pw.work_month DESC
    </select>
    
    <select id="selectProjectWorkloadById" parameterType="Long" resultMap="ProjectWorkloadResult">
        <include refid="selectProjectWorkloadVo"/>
        where id = #{id}
    </select>

    <!-- 专门用于获取用户实际工时记录的查询 -->
    <select id="selectUserActualWorkload" parameterType="ProjectWorkload" resultMap="ProjectWorkloadResult">
        <include refid="selectProjectWorkloadVo"/>
        <where>
            <if test="userName != null and userName != ''"> AND user_name = #{userName}</if>
            <if test="workMonth != null and workMonth != ''"> AND work_month = #{workMonth}</if>
        </where>
        ORDER BY id
    </select>

    <!-- 根据唯一约束字段查询已存在的工时记录 -->
    <select id="selectExistingWorkload" resultMap="ProjectWorkloadResult">
        <include refid="selectProjectWorkloadVo"/>
        WHERE user_name = #{userName}
        AND work_month = #{workMonth}
        AND source_effort_project_id = #{sourceEffortProjectId}
        LIMIT 1
    </select>

    <insert id="insertProjectWorkload" parameterType="ProjectWorkload" useGeneratedKeys="true" keyProperty="id">
        insert into project_workload
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="workMonth != null and workMonth != ''">work_month,</if>
            <if test="involvement != null">involvement,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="sourceEffortProjectId != null">source_effort_project_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="workMonth != null and workMonth != ''">#{workMonth},</if>
            <if test="involvement != null">#{involvement},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="sourceEffortProjectId != null">#{sourceEffortProjectId},</if>
         </trim>
    </insert>

    <update id="updateProjectWorkload" parameterType="ProjectWorkload">
        update project_workload
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="workMonth != null and workMonth != ''">work_month = #{workMonth},</if>
            <if test="involvement != null">involvement = #{involvement},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="sourceEffortProjectId != null">source_effort_project_id = #{sourceEffortProjectId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectWorkloadById" parameterType="Long">
        delete from project_workload where id = #{id}
    </delete>

    <delete id="deleteProjectWorkloadByIds" parameterType="String">
        delete from project_workload where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteProjectWorkloadByProjectIdAndUserName">
        delete from project_workload 
        where project_id = #{projectId} 
        and user_name = #{userName}
    </delete>

    <delete id="deleteProjectWorkloadByProjectId" parameterType="Long">
        delete from project_workload where project_id = #{projectId}
    </delete>

    <select id="selectProjectStats" resultType="java.util.Map">
        SELECT 
            #{projectId} as project_id,
            #{workMonth} as workMonth,
            (
                SELECT 
                    ROUND(COALESCE(SUM(pw2.involvement), 0), 2)
                FROM project_members pm2
                LEFT JOIN project_workload pw2 ON pm2.project_id = pw2.project_id 
                    AND pm2.user_name = pw2.user_name
                    AND pw2.work_month = #{workMonth}
                WHERE pm2.project_id = #{projectId}
                AND pm2.role != '配合人员'
            ) as workloadValue,
            (
                SELECT COUNT(DISTINCT pm3.user_name)
                FROM project_members pm3
                WHERE pm3.project_id = #{projectId}
                AND pm3.role != '配合人员'
            ) as memberCount,
            GROUP_CONCAT(
                DISTINCT CONCAT(
                    COALESCE(pw.user_name, pm.user_name),
                    ':',
                    COALESCE(u.nick_name, ''),
                    ':',
                    ROUND(COALESCE(pw.involvement, 0) * 100, 2)
                )
                ORDER BY COALESCE(pw.involvement, 0) DESC
                SEPARATOR '&#10;'
            ) as workloadDetails,
            (
                SELECT GROUP_CONCAT(
                    CONCAT(pm2.user_name, ':', u2.nick_name, ':', pm2.role)
                    SEPARATOR '&#10;'
                )
                FROM project_members pm2
                LEFT JOIN sys_user u2 ON pm2.user_name = u2.user_name
                WHERE pm2.project_id = #{projectId}
            ) as memberRoles,
            NULL as totalSalary
        FROM project_members pm
        LEFT JOIN project_workload pw ON pm.project_id = pw.project_id 
            AND pm.user_name = pw.user_name
            AND pw.work_month = #{workMonth}
        LEFT JOIN sys_user u ON pm.user_name = u.user_name
        WHERE pm.project_id = #{projectId}
        GROUP BY pm.project_id
    </select>

    <select id="selectProjectMonths" resultType="String">
        SELECT DISTINCT work_month 
        FROM project_workload 
        WHERE project_id = #{projectId}
        ORDER BY work_month DESC
    </select>

    <select id="selectAllWorkloadList" resultType="java.util.Map">
        SELECT 
            u.nick_name as nickName,
            p.project_name as projectName,
            pm.role as role,
            COALESCE(pw.involvement, 0) as involvement,
            COALESCE(pw.work_month, #{workMonth}) as workMonth,
            p.id as projectId,
            u.user_name as userName
        FROM 
            project_members pm
            LEFT JOIN project_workload pw ON pm.project_id = pw.project_id 
                AND pm.user_name = pw.user_name
                AND (#{workMonth} IS NULL OR pw.work_month = #{workMonth})
            INNER JOIN project_info p ON pm.project_id = p.id
            INNER JOIN sys_user u ON pm.user_name = u.user_name
        WHERE
            (#{workMonth} IS NULL OR pw.work_month = #{workMonth})
        ORDER BY 
            u.nick_name,
            p.project_name,
            pw.work_month DESC
    </select>

    <!-- 专门用于统计页面的查询，只返回有实际工时记录的数据 -->
    <select id="selectActualWorkloadList" parameterType="ProjectWorkload" resultMap="ProjectWorkloadResult">
        SELECT
            pw.id,
            pw.project_id,
            pw.user_name,
            pw.work_month,
            pw.involvement,
            pw.created_at,
            pw.updated_at,
            pw.source_effort_project_id,
            p.project_name as projectName,
            u.nick_name as nickName,
            pm.role as role
        FROM
            project_workload pw
            INNER JOIN project_info p ON pw.project_id = p.id
            INNER JOIN sys_user u ON pw.user_name = u.user_name
            LEFT JOIN project_members pm ON pw.project_id = pm.project_id AND pw.user_name = pm.user_name
        <where>
            <if test="projectId != null"> AND pw.project_id = #{projectId}</if>
            <if test="userName != null and userName != ''"> AND pw.user_name = #{userName}</if>
            <if test="workMonth != null and workMonth != ''"> AND pw.work_month = #{workMonth}</if>
        </where>
        ORDER BY
            u.nick_name,
            p.project_name,
            pw.work_month DESC
    </select>

    <select id="selectUnfilledWorkloadCount" resultType="java.util.Map">
        SELECT
            p.project_name AS projectName,
            p.id AS projectId,
            COUNT(DISTINCT pm.user_name) AS unfilledCount,
            GROUP_CONCAT(DISTINCT u.nick_name) AS unfilledUsers
        FROM
            project_members pm
        INNER JOIN
            project_info p ON pm.project_id = p.id
        INNER JOIN
            sys_user u ON pm.user_name = u.user_name
        LEFT JOIN
            project_workload pw ON pm.project_id = pw.project_id
            AND pm.user_name = pw.user_name
            AND pw.work_month = #{workMonth}
        WHERE
            pw.id IS NULL  -- 只选择未填报工时的人员
            AND pm.role != '配合人员'  -- 排除配合人员
        GROUP BY
            p.project_name, p.id
        ORDER BY
            p.project_name
    </select>
</mapper>