<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.ProjectParticipationMapper">
    
    <resultMap type="ProjectParticipation" id="ProjectParticipationResult">
        <result property="id"    column="id"    />
        <result property="userName"    column="user_name"    />
        <result property="projectId"    column="project_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="participationRate"    column="participation_rate"    />
        <result property="month"    column="month"    />
        <result property="deptId"    column="dept_id"    />
        <result property="assignerId"    column="assigner_id"    />
        <result property="assignerName"    column="assigner_name"    />
        <result property="comments"    column="comments"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectProjectParticipationVo">
        select id, user_name, project_id, project_name, participation_rate, month, dept_id, assigner_id, assigner_name, comments, created_at, updated_at from project_participation
    </sql>

    <select id="selectProjectParticipationList" parameterType="ProjectParticipation" resultMap="ProjectParticipationResult">
        <include refid="selectProjectParticipationVo"/>
        <where>  
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="participationRate != null "> and participation_rate = #{participationRate}</if>
            <if test="month != null  and month != ''"> and month = #{month}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="assignerId != null "> and assigner_id = #{assignerId}</if>
            <if test="assignerName != null  and assignerName != ''"> and assigner_name like concat('%', #{assignerName}, '%')</if>
            <if test="comments != null  and comments != ''"> and comments = #{comments}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectProjectParticipationById" parameterType="Long" resultMap="ProjectParticipationResult">
        <include refid="selectProjectParticipationVo"/>
        where id = #{id}
    </select>

    <!-- 根据唯一约束字段查询已存在的精力分配记录 -->
    <select id="selectExistingParticipation" resultMap="ProjectParticipationResult">
        <include refid="selectProjectParticipationVo"/>
        WHERE user_name = #{userName}
        AND project_id = #{projectId}
        AND month = #{month}
        LIMIT 1
    </select>

    <insert id="insertProjectParticipation" parameterType="ProjectParticipation" useGeneratedKeys="true" keyProperty="id">
        insert into project_participation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="projectId != null">project_id,</if>
            <if test="projectName != null">project_name,</if>
            <if test="participationRate != null">participation_rate,</if>
            <if test="month != null and month != ''">month,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="assignerId != null">assigner_id,</if>
            <if test="assignerName != null and assignerName != ''">assigner_name,</if>
            <if test="comments != null">comments,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="participationRate != null">#{participationRate},</if>
            <if test="month != null and month != ''">#{month},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="assignerId != null">#{assignerId},</if>
            <if test="assignerName != null and assignerName != ''">#{assignerName},</if>
            <if test="comments != null">#{comments},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateProjectParticipation" parameterType="ProjectParticipation">
        update project_participation
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="participationRate != null">participation_rate = #{participationRate},</if>
            <if test="month != null and month != ''">month = #{month},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="assignerId != null">assigner_id = #{assignerId},</if>
            <if test="assignerName != null and assignerName != ''">assigner_name = #{assignerName},</if>
            <if test="comments != null">comments = #{comments},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectParticipationById" parameterType="Long">
        delete from project_participation where id = #{id}
    </delete>

    <delete id="deleteProjectParticipationByIds" parameterType="String">
        delete from project_participation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteProjectParticipationByProjectId" parameterType="Long">
        delete from project_participation where project_id = #{projectId}
    </delete>
    
    <select id="countProjectParticipationByProjectId" parameterType="Long" resultType="int">
        select count(1) from project_participation where project_id = #{projectId}
    </select>

    <!-- 查询项目参与度分配列表（关联用户和部门信息） -->
    <select id="selectProjectParticipationWithUserInfo" parameterType="ProjectParticipation" resultType="java.util.Map">
        select
            pp.id,
            pp.user_name as userName,
            pp.project_id as projectId,
            pp.project_name as projectName,
            pp.participation_rate as participationRate,
            pp.month,
            pp.dept_id as deptId,
            pp.assigner_id as assignerId,
            pp.assigner_name as assignerName,
            pp.comments,
            pp.created_at as createdAt,
            pp.updated_at as updatedAt,
            u.user_id as userId,
            u.nick_name as nickName,
            COALESCE(d.dept_name, '未知部门') as deptName
        from project_participation pp
        inner join sys_user u on pp.user_name = u.user_name and u.del_flag = '0'
        left join sys_dept d on u.dept_id = d.dept_id and d.del_flag = '0'
        <where>
            <if test="userName != null  and userName != ''"> and pp.user_name like concat('%', #{userName}, '%')</if>
            <if test="projectId != null "> and pp.project_id = #{projectId}</if>
            <if test="projectName != null  and projectName != ''"> and pp.project_name like concat('%', #{projectName}, '%')</if>
            <if test="participationRate != null "> and pp.participation_rate = #{participationRate}</if>
            <if test="month != null  and month != ''"> and pp.month = #{month}</if>
            <if test="deptId != null "> and pp.dept_id = #{deptId}</if>
            <if test="assignerId != null "> and pp.assigner_id = #{assignerId}</if>
            <if test="assignerName != null  and assignerName != ''"> and pp.assigner_name like concat('%', #{assignerName}, '%')</if>
            <!-- 排除特聘专家 -->
            and (u.remark is null or u.remark != '特聘专家')
        </where>
        order by pp.user_name, pp.project_id
    </select>
</mapper>