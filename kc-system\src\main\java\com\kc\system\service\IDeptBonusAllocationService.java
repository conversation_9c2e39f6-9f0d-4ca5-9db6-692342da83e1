package com.kc.system.service;

import java.util.List;
import com.kc.system.domain.DeptBonusAllocation;
import com.kc.system.domain.dto.DeptBonusAllocationDTO;

/**
 * 部门奖金分配Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IDeptBonusAllocationService 
{
    /**
     * 查询部门奖金分配
     * 
     * @param id 部门奖金分配主键
     * @return 部门奖金分配
     */
    public DeptBonusAllocation selectDeptBonusAllocationById(Long id);

    /**
     * 查询部门奖金分配列表
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 部门奖金分配集合
     */
    public List<DeptBonusAllocation> selectDeptBonusAllocationList(DeptBonusAllocation deptBonusAllocation);

    /**
     * 根据部门ID和月份查询部门奖金分配
     * 
     * @param deptId 部门ID
     * @param allocationMonth 分配月份
     * @return 部门奖金分配
     */
    public DeptBonusAllocation selectByDeptIdAndMonth(Long deptId, String allocationMonth);

    /**
     * 根据月份查询所有部门奖金分配
     * 
     * @param allocationMonth 分配月份
     * @return 部门奖金分配集合
     */
    public List<DeptBonusAllocation> selectByMonth(String allocationMonth);

    /**
     * 新增部门奖金分配
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 结果
     */
    public int insertDeptBonusAllocation(DeptBonusAllocation deptBonusAllocation);

    /**
     * 批量分配部门奖金
     * 
     * @param deptBonusAllocationDTO 部门奖金分配DTO
     * @return 结果
     */
    public int batchAllocateDeptBonus(DeptBonusAllocationDTO deptBonusAllocationDTO);

    /**
     * 修改部门奖金分配
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 结果
     */
    public int updateDeptBonusAllocation(DeptBonusAllocation deptBonusAllocation);

    /**
     * 更新部门奖金分配状态
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 结果
     */
    public int updateAllocationStatus(DeptBonusAllocation deptBonusAllocation);

    /**
     * 批量删除部门奖金分配
     * 
     * @param ids 需要删除的部门奖金分配主键集合
     * @return 结果
     */
    public int deleteDeptBonusAllocationByIds(Long[] ids);

    /**
     * 删除部门奖金分配信息
     * 
     * @param id 部门奖金分配主键
     * @return 结果
     */
    public int deleteDeptBonusAllocationById(Long id);

    /**
     * 根据月份删除部门奖金分配
     * 
     * @param allocationMonth 分配月份
     * @return 结果
     */
    public int deleteByMonth(String allocationMonth);

    /**
     * 检查指定月份是否已经分配过奖金
     * 
     * @param allocationMonth 分配月份
     * @return 是否已分配
     */
    public boolean isMonthAllocated(String allocationMonth);

    /**
     * 获取部门在指定月份的剩余奖金
     * 
     * @param deptId 部门ID
     * @param allocationMonth 分配月份
     * @return 剩余奖金
     */
    public java.math.BigDecimal getDeptRemainingBonus(Long deptId, String allocationMonth);
}
