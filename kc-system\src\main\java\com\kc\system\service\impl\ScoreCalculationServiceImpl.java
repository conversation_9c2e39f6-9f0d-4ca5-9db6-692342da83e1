package com.kc.system.service.impl;

import com.kc.common.core.domain.entity.SysDept;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.system.dto.ScoreCalculationContext;
import com.kc.system.dto.ScoreCalculationResult;
import com.kc.system.factory.ScoreCalculationStrategyFactory;
import com.kc.system.service.IScoreCalculationService;
import com.kc.system.strategy.ScoreCalculationStrategy;
import com.kc.system.domain.ProjectEvaluation;
import com.kc.system.service.IProjectEvaluationService;

import com.kc.system.service.IEvaluationResultService;
import com.kc.system.service.ISysDeptService;
import com.kc.system.service.ISysUserService;
import com.kc.system.service.IProjectParticipationService;
import com.kc.system.mapper.EvaluationResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 评分计算服务实现
 * 
 * <AUTHOR>
 */
@Service
public class ScoreCalculationServiceImpl implements IScoreCalculationService {
    
    private static final Logger log = LoggerFactory.getLogger(ScoreCalculationServiceImpl.class);
    
    @Autowired
    private ScoreCalculationStrategyFactory strategyFactory;
    
    @Autowired
    private EvaluationResultMapper evaluationResultMapper;
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private ISysDeptService deptService;
    
    @Autowired
    private IProjectParticipationService projectParticipationService;

    @Autowired
    private IProjectEvaluationService projectEvaluationService;
    
    @Override
    public ScoreCalculationResult calculateUserScore(Long userId, String evaluationMonth) {
        try {
            // 构建计算上下文
            ScoreCalculationContext context = buildCalculationContext(userId, evaluationMonth);
            
            // 计算评分
            return calculateScore(context);
        } catch (Exception e) {
            log.error("计算用户评分失败，用户ID: {}, 评价月份: {}", userId, evaluationMonth, e);
            ScoreCalculationResult result = new ScoreCalculationResult();
            result.setUserId(userId);
            result.setEvaluationMonth(evaluationMonth);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setCalculationTime(LocalDateTime.now());
            return result;
        }
    }
    
    @Override
    public List<ScoreCalculationResult> batchCalculateUserScores(List<Long> userIds, String evaluationMonth) {
        return userIds.stream()
                .map(userId -> calculateUserScore(userId, evaluationMonth))
                .collect(Collectors.toList());
    }
    
    @Override
    public ScoreCalculationResult calculateScore(ScoreCalculationContext context) {
        try {
            // 获取合适的计算策略
            ScoreCalculationStrategy strategy = strategyFactory.getStrategy(context);
            
            // 计算评分
            BigDecimal finalScore = strategy.calculateScore(context);
            
            // 构建详细评分信息
            ScoreCalculationResult.ScoreDetails scoreDetails = buildScoreDetails(context, strategy);

            ScoreCalculationResult result = new ScoreCalculationResult();
            result.setUserId(context.getEvaluatee().getUserId());
            result.setUserName(context.getEvaluatee().getNickName());
            result.setFinalScore(finalScore);
            result.setStrategyType(strategy.getStrategyType());
            result.setCalculationDetails(strategy.getCalculationDetails(context));
            result.setEvaluationMonth(context.getEvaluationMonth());
            result.setCalculationTime(LocalDateTime.now());
            result.setSuccess(true);
            result.setScoreDetails(scoreDetails);
            return result;
                    
        } catch (Exception e) {
            log.error("评分计算失败", e);
            ScoreCalculationResult result = new ScoreCalculationResult();
            result.setUserId(context.getEvaluatee().getUserId());
            result.setUserName(context.getEvaluatee().getNickName());
            result.setEvaluationMonth(context.getEvaluationMonth());
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setCalculationTime(LocalDateTime.now());
            return result;
        }
    }
    
    @Override
    public ScoreCalculationContext buildCalculationContext(Long userId, String evaluationMonth) {
        // 获取用户信息
        SysUser user = userService.selectUserById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在: " + userId);
        }
        
        // 获取用户部门信息
        SysDept userDept = deptService.selectDeptById(user.getDeptId());
        
        // 获取父部门信息
        SysDept parentDept = null;
        boolean isSubDeptMember = false;
        String subDeptLeader = null;
        String parentDeptLeader = null;
        
        if (userDept != null && userDept.getParentId() != null && userDept.getParentId() != 0) {
            parentDept = deptService.selectDeptById(userDept.getParentId());
            isSubDeptMember = true;
            subDeptLeader = userDept.getLeader();
            parentDeptLeader = parentDept != null ? parentDept.getLeader() : null;
        }
        
        // 获取评价记录
        ProjectEvaluation query = new ProjectEvaluation();
        query.setEvaluateeId(userId);
        query.setEvaluationMonth(evaluationMonth);
        List<ProjectEvaluation> evaluations = projectEvaluationService.selectProjectEvaluationList(query);
        
        // 获取项目参与情况
        List<ScoreCalculationContext.ProjectParticipation> projectParticipations = 
                getProjectParticipations(user.getUserName(), evaluationMonth);
        
        // 构建部门层级关系
        Map<Long, SysDept> deptHierarchy = new HashMap<>();
        if (userDept != null) {
            deptHierarchy.put(userDept.getDeptId(), userDept);
        }
        if (parentDept != null) {
            deptHierarchy.put(parentDept.getDeptId(), parentDept);
        }
        
        return new ScoreCalculationContext(user, userDept, parentDept, evaluationMonth,
                evaluations, projectParticipations, deptHierarchy, isSubDeptMember,
                subDeptLeader, parentDeptLeader, new HashMap<>());
    }
    
    @Override
    public List<String> getAvailableStrategies() {
        return strategyFactory.getAllStrategies().stream()
                .map(ScoreCalculationStrategy::getStrategyType)
                .collect(Collectors.toList());
    }
    
    @Override
    public ScoreCalculationResult previewCalculation(ScoreCalculationContext context) {
        // 预览计算，不保存结果
        return calculateScore(context);
    }
    
    /**
     * 获取项目参与情况
     */
    private List<ScoreCalculationContext.ProjectParticipation> getProjectParticipations(String userName, String evaluationMonth) {
        // 暂时返回空列表，项目参与信息将通过其他方式获取
        // TODO: 实现项目参与数据查询
        return new ArrayList<>();
    }
    
    /**
     * 构建详细评分信息
     */
    private ScoreCalculationResult.ScoreDetails buildScoreDetails(ScoreCalculationContext context, ScoreCalculationStrategy strategy) {
        List<ProjectEvaluation> evaluations = context.getEvaluations();

        // 提取各类评分
        BigDecimal managerScore = getScoreByType(evaluations, "manager");
        BigDecimal projectScore = getScoreByType(evaluations, "project_leader");
        BigDecimal subDeptManagerScore = getScoreByType(evaluations, "sub_dept_manager");
        BigDecimal parentDeptManagerScore = getScoreByType(evaluations, "parent_manager");
        
        // 构建权重信息
        Map<String, BigDecimal> scoreWeights = new HashMap<>();
        String strategyType = strategy.getStrategyType();
        
        if ("STANDARD_EMPLOYEE".equals(strategyType)) {
            scoreWeights.put("manager", new BigDecimal("0.6"));
            scoreWeights.put("project_leader", new BigDecimal("0.4"));
        } else if ("SUB_DEPT_MEMBER".equals(strategyType)) {
            scoreWeights.put("sub_dept_manager", new BigDecimal("0.5"));
            scoreWeights.put("parent_manager", new BigDecimal("0.5"));
        } else if ("NO_PROJECT_EMPLOYEE".equals(strategyType)) {
            scoreWeights.put("manager", new BigDecimal("1.0"));
        }
        
        ScoreCalculationResult.ScoreDetails scoreDetails = new ScoreCalculationResult.ScoreDetails();
        scoreDetails.setManagerScore(managerScore);
        scoreDetails.setProjectScore(projectScore);
        scoreDetails.setSubDeptManagerScore(subDeptManagerScore);
        scoreDetails.setParentDeptManagerScore(parentDeptManagerScore);
        scoreDetails.setScoreWeights(scoreWeights);
        scoreDetails.setCalculationProcess(strategy.getCalculationDetails(context));
        return scoreDetails;
    }
    
    /**
     * 根据评价类型获取评分
     */
    private BigDecimal getScoreByType(List<ProjectEvaluation> evaluations, String evaluationType) {
        return evaluations.stream()
                .filter(eval -> evaluationType.equals(eval.getEvaluationType()))
                .map(ProjectEvaluation::getScore)
                .findFirst()
                .orElse(null);
    }
}
