package com.kc.system.dto;

import com.kc.common.core.domain.entity.SysDept;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.system.domain.ProjectEvaluation;
import java.util.List;
import java.util.Map;

/**
 * 评分计算上下文
 * 包含评分计算所需的所有信息
 *
 * <AUTHOR>
 */
public class ScoreCalculationContext {
    
    /** 被评价用户 */
    private SysUser evaluatee;
    
    /** 被评价用户所在部门 */
    private SysDept userDept;
    
    /** 父部门信息 */
    private SysDept parentDept;
    
    /** 评价月份 */
    private String evaluationMonth;
    
    /** 所有相关评价记录 */
    private List<ProjectEvaluation> evaluations;
    
    /** 用户项目参与情况 */
    private List<ProjectParticipation> projectParticipations;
    
    /** 部门层级关系 */
    private Map<Long, SysDept> deptHierarchy;
    
    /** 是否为子部门成员 */
    private Boolean isSubDeptMember;
    
    /** 子部门负责人 */
    private String subDeptLeader;
    
    /** 父部门负责人 */
    private String parentDeptLeader;
    
    /** 额外配置参数 */
    private Map<String, Object> configParams;
    
    // 构造函数
    public ScoreCalculationContext() {}

    public ScoreCalculationContext(SysUser evaluatee, SysDept userDept, SysDept parentDept,
                                 String evaluationMonth, List<ProjectEvaluation> evaluations,
                                 List<ProjectParticipation> projectParticipations,
                                 Map<Long, SysDept> deptHierarchy, Boolean isSubDeptMember,
                                 String subDeptLeader, String parentDeptLeader,
                                 Map<String, Object> configParams) {
        this.evaluatee = evaluatee;
        this.userDept = userDept;
        this.parentDept = parentDept;
        this.evaluationMonth = evaluationMonth;
        this.evaluations = evaluations;
        this.projectParticipations = projectParticipations;
        this.deptHierarchy = deptHierarchy;
        this.isSubDeptMember = isSubDeptMember;
        this.subDeptLeader = subDeptLeader;
        this.parentDeptLeader = parentDeptLeader;
        this.configParams = configParams;
    }

    // Getter和Setter方法
    public SysUser getEvaluatee() {
        return evaluatee;
    }

    public void setEvaluatee(SysUser evaluatee) {
        this.evaluatee = evaluatee;
    }

    public SysDept getUserDept() {
        return userDept;
    }

    public void setUserDept(SysDept userDept) {
        this.userDept = userDept;
    }

    public SysDept getParentDept() {
        return parentDept;
    }

    public void setParentDept(SysDept parentDept) {
        this.parentDept = parentDept;
    }

    public String getEvaluationMonth() {
        return evaluationMonth;
    }

    public void setEvaluationMonth(String evaluationMonth) {
        this.evaluationMonth = evaluationMonth;
    }

    public List<ProjectEvaluation> getEvaluations() {
        return evaluations;
    }

    public void setEvaluations(List<ProjectEvaluation> evaluations) {
        this.evaluations = evaluations;
    }

    public List<ProjectParticipation> getProjectParticipations() {
        return projectParticipations;
    }

    public void setProjectParticipations(List<ProjectParticipation> projectParticipations) {
        this.projectParticipations = projectParticipations;
    }

    public Map<Long, SysDept> getDeptHierarchy() {
        return deptHierarchy;
    }

    public void setDeptHierarchy(Map<Long, SysDept> deptHierarchy) {
        this.deptHierarchy = deptHierarchy;
    }

    public Boolean getIsSubDeptMember() {
        return isSubDeptMember;
    }

    public void setIsSubDeptMember(Boolean isSubDeptMember) {
        this.isSubDeptMember = isSubDeptMember;
    }

    public String getSubDeptLeader() {
        return subDeptLeader;
    }

    public void setSubDeptLeader(String subDeptLeader) {
        this.subDeptLeader = subDeptLeader;
    }

    public String getParentDeptLeader() {
        return parentDeptLeader;
    }

    public void setParentDeptLeader(String parentDeptLeader) {
        this.parentDeptLeader = parentDeptLeader;
    }

    public Map<String, Object> getConfigParams() {
        return configParams;
    }

    public void setConfigParams(Map<String, Object> configParams) {
        this.configParams = configParams;
    }

    /**
     * 项目参与信息内部类
     */
    public static class ProjectParticipation {
        private Long projectId;
        private String projectName;
        private String role;
        private Boolean hasProjectEvaluation;

        public ProjectParticipation() {}

        public ProjectParticipation(Long projectId, String projectName, String role, Boolean hasProjectEvaluation) {
            this.projectId = projectId;
            this.projectName = projectName;
            this.role = role;
            this.hasProjectEvaluation = hasProjectEvaluation;
        }

        public Long getProjectId() {
            return projectId;
        }

        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }

        public String getProjectName() {
            return projectName;
        }

        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public Boolean getHasProjectEvaluation() {
            return hasProjectEvaluation;
        }

        public void setHasProjectEvaluation(Boolean hasProjectEvaluation) {
            this.hasProjectEvaluation = hasProjectEvaluation;
        }
    }
}
