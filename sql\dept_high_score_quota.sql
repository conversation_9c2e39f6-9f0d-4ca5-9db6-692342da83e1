-- 部门高分配额表
CREATE TABLE dept_high_score_quota (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    dept_id BIGINT NOT NULL COMMENT '部门ID',
    evaluation_year VARCHAR(4) NOT NULL COMMENT '评价年度（如：2024）',
    total_employees INT NOT NULL DEFAULT 0 COMMENT '部门总人数',
    high_score_quota INT NOT NULL DEFAULT 0 COMMENT '高分配额（30%）',
    used_quota INT NOT NULL DEFAULT 0 COMMENT '已使用配额',
    remaining_quota INT NOT NULL DEFAULT 0 COMMENT '剩余配额',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    UNIQUE KEY uk_dept_year (dept_id, evaluation_year) COMMENT '部门年度唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门高分配额表';

-- 高分记录表
CREATE TABLE high_score_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    dept_id BIGINT NOT NULL COMMENT '部门ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    user_name VARCHAR(30) NOT NULL COMMENT '用户名',
    nick_name VARCHAR(30) DEFAULT '' COMMENT '用户昵称',
    evaluation_year VARCHAR(4) NOT NULL COMMENT '评价年度',
    evaluation_month VARCHAR(7) NOT NULL COMMENT '评价月份（如：2024-12）',
    score DECIMAL(5,2) NOT NULL COMMENT '评分（95-100分）',
    evaluator_id BIGINT NOT NULL COMMENT '评价人ID',
    evaluator_name VARCHAR(30) NOT NULL COMMENT '评价人姓名',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_dept_year (dept_id, evaluation_year) COMMENT '部门年度索引',
    INDEX idx_user_year (user_id, evaluation_year) COMMENT '用户年度索引',
    UNIQUE KEY uk_user_year (user_id, evaluation_year) COMMENT '用户年度唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='高分记录表';
