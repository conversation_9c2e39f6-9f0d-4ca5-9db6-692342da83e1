package com.kc.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 项目参与度分配对象 project_participation
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
public class ProjectParticipation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 员工用户名 */
    @Excel(name = "员工用户名")
    private String userName;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 项目名称(冗余) */
    @Excel(name = "项目名称(冗余)")
    private String projectName;

    /** 项目参与度百分比(0-1) */
    @Excel(name = "项目参与度百分比(0-1)")
    private BigDecimal participationRate;

    /** 记录月份(yyyy-MM) */
    @Excel(name = "记录月份(yyyy-MM)")
    private String month;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 分配人ID */
    @Excel(name = "分配人ID")
    private Long assignerId;

    /** 分配人用户名 */
    @Excel(name = "分配人用户名")
    private String assignerName;

    /** 备注 */
    @Excel(name = "备注")
    private String comments;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }
    public void setParticipationRate(BigDecimal participationRate) 
    {
        this.participationRate = participationRate;
    }

    public BigDecimal getParticipationRate() 
    {
        return participationRate;
    }
    public void setMonth(String month) 
    {
        this.month = month;
    }

    public String getMonth() 
    {
        return month;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setAssignerId(Long assignerId) 
    {
        this.assignerId = assignerId;
    }

    public Long getAssignerId() 
    {
        return assignerId;
    }
    public void setAssignerName(String assignerName) 
    {
        this.assignerName = assignerName;
    }

    public String getAssignerName() 
    {
        return assignerName;
    }
    public void setComments(String comments) 
    {
        this.comments = comments;
    }

    public String getComments() 
    {
        return comments;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userName", getUserName())
            .append("projectId", getProjectId())
            .append("projectName", getProjectName())
            .append("participationRate", getParticipationRate())
            .append("month", getMonth())
            .append("deptId", getDeptId())
            .append("assignerId", getAssignerId())
            .append("assignerName", getAssignerName())
            .append("comments", getComments())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
