import request from '@/utils/request'

// 查询参数列表
export function listConfig(query) {
  return request({
    url: '/system/config/list',
    method: 'get',
    params: query
  })
}

// 查询参数详细
export function getConfig(configId) {
  return request({
    url: '/system/config/' + configId,
    method: 'get'
  })
}

// 根据参数键名查询参数值
export function getConfigKey(configKey) {
  return request({
    url: '/system/config/configKey/' + configKey,
    method: 'get'
  })
}

// 新增参数配置
export function addConfig(data) {
  return request({
    url: '/system/config',
    method: 'post',
    data: data
  })
}

// 修改参数配置
export function updateConfig(data) {
  return request({
    url: '/system/config',
    method: 'put',
    data: data
  })
}

// 删除参数配置
export function delConfig(configId) {
  return request({
    url: '/system/config/' + configId,
    method: 'delete'
  })
}

// 刷新参数缓存
export function refreshCache() {
  return request({
    url: '/system/config/refreshCache',
    method: 'delete'
  })
}

// 根据参数键名修改参数键值
export function updateConfigByKey(configKey, configValue) {
  return request({
    url: '/system/config/updateByKey/' + configKey,
    method: 'put',
    data: configValue
  })
}

// 直接从数据库获取参数值
export function getConfigKeyFromDb(configKey) {
  return request({
    url: '/system/config/configKeyFromDb/' + configKey,
    method: 'get',
    params: {
      _t: new Date().getTime() // 添加时间戳防止缓存
    }
  })
}

// 修复评分配置
export function fixEvaluationConfig() {
  return request({
    url: '/system/config/fixEvaluationConfig',
    method: 'post'
  })
}
