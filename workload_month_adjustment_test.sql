-- 工作量归集月份调整测试脚本
-- 验证后端工作量归集月份+1的修改效果

-- ===== 测试环境说明 =====
SELECT '===== 工作量归集月份调整测试 =====' as test_section;

SELECT 
    '修改说明' as item,
    '后端ProjectWorkloadController添加月份+1逻辑' as description
UNION ALL
SELECT 
    '预期效果',
    '工作量归集和精力分配都存储为+1后的月份，保持一致'
UNION ALL
SELECT 
    '测试目标',
    '验证提交后两种数据的月份是否一致';

-- ===== 测试前数据状态 =====
SELECT '===== 测试前数据状态 =====' as test_section;

-- 查看最近的精力分配数据
SELECT 
    '精力分配最新数据' as data_type,
    user_name,
    project_id,
    month,
    participation_rate,
    created_at
FROM project_participation 
ORDER BY created_at DESC 
LIMIT 5;

-- 查看最近的工作量归集数据
SELECT 
    '工作量归集最新数据' as data_type,
    user_name,
    project_id,
    work_month,
    involvement,
    created_at
FROM project_workload 
ORDER BY created_at DESC 
LIMIT 5;

-- ===== 数据一致性检查 =====
SELECT '===== 数据一致性检查 =====' as test_section;

-- 检查同一用户同一时间提交的数据月份是否一致
SELECT 
    '数据一致性检查' as check_type,
    pp.user_name,
    pp.month as effort_month,
    pw.work_month as workload_month,
    CASE 
        WHEN pp.month = pw.work_month THEN '一致 ✅'
        ELSE '不一致 ❌'
    END as consistency_status,
    pp.created_at as effort_created,
    pw.created_at as workload_created
FROM project_participation pp
LEFT JOIN project_workload pw ON pp.user_name = pw.user_name 
    AND DATE(pp.created_at) = DATE(pw.created_at)
    AND pp.project_id = pw.source_effort_project_id
WHERE pp.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY pp.created_at DESC
LIMIT 10;

-- ===== 月份分布统计 =====
SELECT '===== 月份分布统计 =====' as test_section;

-- 精力分配月份分布
SELECT 
    '精力分配月份分布' as data_type,
    month,
    COUNT(*) as record_count,
    COUNT(DISTINCT user_name) as user_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM project_participation 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY month
ORDER BY month DESC;

-- 工作量归集月份分布
SELECT 
    '工作量归集月份分布' as data_type,
    work_month,
    COUNT(*) as record_count,
    COUNT(DISTINCT user_name) as user_count,
    MIN(created_at) as earliest_record,
    MAX(created_at) as latest_record
FROM project_workload 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY work_month
ORDER BY work_month DESC;

-- ===== 修改前后对比 =====
SELECT '===== 修改前后对比 =====' as test_section;

-- 查找修改前的数据（工作量归集月份没有+1）
SELECT 
    '修改前数据特征' as comparison_type,
    COUNT(*) as mismatch_count,
    '工作量归集月份比精力分配月份小1' as description
FROM project_participation pp
JOIN project_workload pw ON pp.user_name = pw.user_name 
    AND pp.project_id = pw.source_effort_project_id
WHERE DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(pp.month, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m') = pw.work_month
AND pp.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 查找修改后的数据（工作量归集月份与精力分配月份一致）
SELECT 
    '修改后数据特征' as comparison_type,
    COUNT(*) as match_count,
    '工作量归集月份与精力分配月份一致' as description
FROM project_participation pp
JOIN project_workload pw ON pp.user_name = pw.user_name 
    AND pp.project_id = pw.source_effort_project_id
WHERE pp.month = pw.work_month
AND pp.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- ===== 评分查询验证 =====
SELECT '===== 评分查询验证 =====' as test_section;

-- 模拟评分时的查询：查询当前月份的数据
SET @evaluation_month = DATE_FORMAT(NOW(), '%Y-%m');

SELECT 
    '评分查询模拟' as query_type,
    @evaluation_month as evaluation_month,
    '查询当月精力分配数据' as effort_query,
    COUNT(DISTINCT pp.user_name) as effort_users,
    '查询当月工作量归集数据' as workload_query,
    COUNT(DISTINCT pw.user_name) as workload_users
FROM project_participation pp
FULL OUTER JOIN project_workload pw ON pp.month = pw.work_month
WHERE pp.month = @evaluation_month OR pw.work_month = @evaluation_month;

-- ===== 导入上月数据验证 =====
SELECT '===== 导入上月数据验证 =====' as test_section;

-- 模拟导入上月数据的查询
SET @current_month = DATE_FORMAT(NOW(), '%Y-%m');
SET @last_month = DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m');

SELECT 
    '导入上月数据模拟' as test_type,
    @current_month as current_month,
    @last_month as last_month,
    '精力分配可导入数据' as effort_data,
    COUNT(DISTINCT pp.user_name) as effort_users,
    '工作量归集可导入数据' as workload_data,
    COUNT(DISTINCT pw.user_name) as workload_users
FROM project_participation pp
FULL OUTER JOIN project_workload pw ON pp.month = pw.work_month
WHERE pp.month = @current_month OR pw.work_month = @current_month;

-- ===== 业务场景验证 =====
SELECT '===== 业务场景验证 =====' as test_section;

-- 场景1：用户填报后立即查看数据
SELECT 
    '场景1：填报后查看' as scenario,
    '用户在6月30号填报' as action,
    '数据存储为7月' as storage,
    '7月10号评分时查询7月数据' as evaluation,
    '能查到数据' as result;

-- 场景2：导入上月数据
SELECT 
    '场景2：导入上月数据' as scenario,
    '7月导入6月数据' as action,
    '查询7月存储的数据' as query,
    '能找到6月30号填报的数据' as result;

-- ===== 数据完整性检查 =====
SELECT '===== 数据完整性检查 =====' as test_section;

-- 检查是否有孤立的精力分配数据（没有对应的工作量归集）
SELECT 
    '孤立精力分配数据' as check_type,
    COUNT(*) as orphan_count,
    '没有对应工作量归集的精力分配记录' as description
FROM project_participation pp
LEFT JOIN project_workload pw ON pp.user_name = pw.user_name 
    AND pp.month = pw.work_month
    AND pp.project_id = pw.source_effort_project_id
WHERE pw.id IS NULL
AND pp.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 检查是否有孤立的工作量归集数据（没有对应的精力分配）
SELECT 
    '孤立工作量归集数据' as check_type,
    COUNT(*) as orphan_count,
    '没有对应精力分配的工作量归集记录' as description
FROM project_workload pw
LEFT JOIN project_participation pp ON pw.user_name = pp.user_name 
    AND pw.work_month = pp.month
    AND pw.source_effort_project_id = pp.project_id
WHERE pp.id IS NULL
AND pw.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY);

-- ===== 测试建议 =====
SELECT '===== 测试建议 =====' as test_section;

SELECT 
    '测试步骤' as step_type,
    '1. 重启应用使后端修改生效' as step_1,
    '2. 进行一次完整的精力分配填报' as step_2,
    '3. 检查数据库中两种数据的月份是否一致' as step_3,
    '4. 测试评分界面是否能正常显示数据' as step_4,
    '5. 测试导入上月数据功能是否正常' as step_5;

SELECT 
    '验证要点' as point_type,
    '精力分配和工作量归集月份必须一致' as point_1,
    '评分时能查询到完整数据' as point_2,
    '导入上月数据功能正常' as point_3,
    '用户填报后能立即看到数据' as point_4;

-- ===== 测试结果总结 =====
SELECT '===== 测试结果总结 =====' as test_section;

SELECT 
    '修改效果' as summary_type,
    '后端工作量归集保存时自动+1月份' as effect_1,
    '与精力分配保持完全一致' as effect_2,
    '解决填报周期不匹配问题' as effect_3,
    '用户体验更加流畅' as effect_4;

-- 显示测试完成时间
SELECT 
    '测试完成' as status,
    NOW() as completion_time,
    '工作量归集月份调整验证完成' as result,
    '等待应用重启后进行实际测试' as next_step;
