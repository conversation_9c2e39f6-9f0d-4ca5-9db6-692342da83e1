-- 部门高分配额权限配置SQL（简化版）
-- 由于高分配额功能集成在部门评价页面中，只需要添加API权限

-- 1. 查找机构成员评分菜单ID
SET @org_member_evaluation_menu_id = (
    SELECT menu_id
    FROM sys_menu
    WHERE menu_name = '机构成员评分'
    AND menu_type = 'C'
    LIMIT 1
);

-- 如果找不到机构成员评分菜单，尝试其他可能的名称
SET @org_member_evaluation_menu_id = IFNULL(@org_member_evaluation_menu_id, (
    SELECT menu_id
    FROM sys_menu
    WHERE (menu_name LIKE '%机构%评分%' OR menu_name LIKE '%成员评分%' OR menu_name LIKE '%部门评价%')
    AND menu_type = 'C'
    LIMIT 1
));

-- 如果还是找不到，则使用系统管理菜单作为父菜单（假设ID为2000）
SET @org_member_evaluation_menu_id = IFNULL(@org_member_evaluation_menu_id, 2000);

-- 2. 添加高分配额相关的功能权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES
('高分配额查询', @org_member_evaluation_menu_id, 10, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:query', '#', 'admin', sysdate(), '', null, '查询部门高分配额信息'),
('高分配额检查', @org_member_evaluation_menu_id, 11, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:check', '#', 'admin', sysdate(), '', null, '检查高分配额是否可用'),
('高分配额管理', @org_member_evaluation_menu_id, 12, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:manage', '#', 'admin', sysdate(), '', null, '管理高分配额（使用和释放）');

-- 3. 获取刚插入的权限菜单ID
SET @quota_query_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:query' ORDER BY menu_id DESC LIMIT 1);
SET @quota_check_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:check' ORDER BY menu_id DESC LIMIT 1);
SET @quota_manage_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:manage' ORDER BY menu_id DESC LIMIT 1);

-- 4. 为管理员角色分配所有权限（角色ID=1）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(1, @quota_query_id),
(1, @quota_check_id),
(1, @quota_manage_id);

-- 5. 为部门负责人角色分配权限（假设角色ID=2，请根据实际情况调整）
-- 部门负责人需要查询、检查和管理配额的权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(2, @quota_query_id),
(2, @quota_check_id),
(2, @quota_manage_id);

-- 6. 如果有其他需要权限的角色，可以继续添加
-- 例如：HR角色（假设角色ID=3）
-- INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
-- (3, @quota_query_id),
-- (3, @quota_check_id);

-- 7. 验证权限配置
SELECT 
    r.role_id,
    r.role_name,
    m.menu_name,
    m.perms,
    m.menu_type
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.perms LIKE '%deptQuota%'
ORDER BY r.role_id, m.order_num;

-- 8. 查看所有高分配额相关权限
SELECT 
    menu_id,
    menu_name,
    parent_id,
    perms,
    menu_type,
    visible,
    status
FROM sys_menu 
WHERE perms LIKE '%deptQuota%'
ORDER BY parent_id, order_num;

-- 注意事项：
-- 1. 请根据实际的角色ID调整role_id值
-- 2. 请根据实际的部门评价菜单情况调整parent_id
-- 3. 如果部门评价菜单不存在，请先创建部门评价菜单
-- 4. 执行前请备份数据库
-- 5. 建议先在测试环境验证

-- 常用角色ID参考（请根据实际项目调整）：
-- 1 - 管理员
-- 2 - 部门负责人  
-- 3 - HR
-- 4 - 普通用户

-- 如果需要查看当前系统的角色列表：
-- SELECT role_id, role_name, role_key, status FROM sys_role WHERE status = '0' ORDER BY role_sort;
