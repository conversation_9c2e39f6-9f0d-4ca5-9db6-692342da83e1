<template>
  <div class="app-container">
    <!-- 顶部搜索区域 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
        <el-form-item label="评价月份" prop="evaluationMonth">
          <el-date-picker
            v-model="queryParams.evaluationMonth"
            type="month"
            value-format="yyyy-MM"
            placeholder="请选择评价月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 警告信息 -->
    <el-alert
      v-if="projectOptions.length === 0"
      title="您暂无负责的项目，无法进行项目负责人评价"
      type="info"
      :closable="false"
      show-icon
      style="margin: 15px 0;">
    </el-alert>

    <!-- 内容区域 -->
    <div v-else class="content-container">
      <!-- 标签页容器 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="evaluation-tabs">
        <!-- 月度评价标签页 -->
        <el-tab-pane label="月度评价" name="monthly">
          <div class="tab-content">
            <!-- 月度评价的项目和成员管理 -->
            <div class="project-member-container">
              <!-- 左侧项目列表 -->
              <el-card class="project-card">
                <div slot="header" class="card-header">
                  <div class="card-title-section">
                    <h4 class="monthly-title">负责项目列表</h4>
                    <span class="project-count">共 {{ projectOptions.length }} 个项目</span>
                  </div>
                </div>
                
                <div class="project-section">
                  <div class="project-list">
                    <div 
                      v-for="(project, index) in projectOptions" 
                      :key="'project-' + project.id + '-' + index"
                      :class="['project-item', {active: currentProjectId === project.id}]"
                      @click="selectMonthlyProject(project.id)">
                      <i class="el-icon-folder"></i>
                      <span class="project-name">{{ project.projectName }}</span>
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- 右侧成员详情 -->
              <el-card class="member-card">
                <div slot="header" class="card-header">
                  <div class="member-header-info">
                    <h4 class="monthly-title">{{ getCurrentProjectName() }} - 成员评价</h4>
                    <span class="header-month">评价月份: {{ queryParams.evaluationMonth || '未选择' }}</span>
                  </div>
                </div>

                <div v-loading="loading" class="member-content">
                  <div v-if="!currentProjectId" class="placeholder-info">
                    请在左侧选择一个项目
                  </div>
                  <div v-else-if="!memberList[currentProjectId] || memberList[currentProjectId].length === 0" class="placeholder-info">
                    该项目暂无成员或成员数据加载中...
                  </div>
                  <div v-else>
                    <!-- 配额信息显示 -->
                    <el-alert
                      :title="getQuotaInfoText()"
                      :type="getQuotaAlertType()"
                      :closable="false"
                      show-icon
                      style="margin-bottom: 15px;">
                    </el-alert>

                    <!-- 建议分值区间提示 -->
                    <el-alert
                      title="建议分值区间：非常满意：95分以上；比较满意：85-94分；满意：75-84分；不满意：74分及以下"
                      type="success"
                      :closable="false"
                      show-icon
                      style="margin-bottom: 15px;">
                    </el-alert>

                    <el-table
                      :data="memberList[currentProjectId]" 
                      border 
                      style="width: 100%"
                      row-key="_uid"
                      :cell-class-name="getCellClassName"
                      :row-class-name="getRowClassName">
                      <el-table-column label="序号" type="index" width="50" align="center">
                        <template slot-scope="scope">
                          {{ scope.$index + 1 }}
                        </template>
                      </el-table-column>
                      <el-table-column label="成员姓名" prop="nickName" min-width="120" align="center">
                        <template slot-scope="scope">
                          <span v-if="scope.row.nickName && scope.row.nickName !== scope.row.userName">
                            {{ scope.row.nickName }}
                          </span>
                          <span v-else>
                            {{ scope.row.userName }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="员工编号" prop="userName" min-width="120" align="center">
                        <template slot-scope="scope">
                          {{ scope.row.userName }}
                        </template>
                      </el-table-column>
                      <el-table-column label="所属部门" prop="deptName" min-width="150" align="center">
                        <template slot-scope="scope">
                          <span v-if="scope.row.deptName && scope.row.deptName !== '未知部门'">
                            {{ scope.row.deptName }}
                          </span>
                          <span v-else style="color: #999;">
                            未知部门
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="评分" min-width="220" align="center">
                        <template slot-scope="scope">
                          <el-input-number
                            v-model="scope.row.newScore"
                            :min="0"
                            :max="100"
                            @change="handleScoreChange(scope.row)"
                            size="small"
                            controls-position="right">
                          </el-input-number>
                          <span class="score-unit">分</span>
                          <span class="score-limit">(0-100)</span>
                        </template>
                      </el-table-column>
                    </el-table>
                    
                    <!-- 提交按钮 -->
                    <div class="table-footer-wrapper">
                      <div class="table-footer">
                        <el-button
                          type="primary"
                          :disabled="!canSubmit"
                          @click="batchSaveEvaluations"
                          size="medium">
                          <i class="el-icon-check"></i> 提交月度评价
                        </el-button>
                        <span v-if="!canSubmit" class="submit-warning">
                          {{ getSubmitWarningText() }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 季度评价标签页 -->
        <el-tab-pane name="quarterly">
          <span slot="label">
            <i class="el-icon-date"></i> 季度评价
          </span>
          <div class="tab-content">
            <!-- 季度评价的项目和成员管理 -->
            <div class="project-member-container">
              <!-- 左侧项目列表 -->
              <el-card class="project-card">
                <div slot="header" class="card-header">
                  <div class="card-title-section">
                    <h4 class="quarterly-title">负责项目列表</h4>
                    <span class="project-count">共 {{ rightProjectOptions.length }} 个项目</span>
                  </div>
                </div>
                
                <div class="project-section">
                  <div class="project-list">
                    <div
                      v-for="(project, index) in rightProjectOptions"
                      :key="'quarterly-project-' + project.id + '-' + index"
                      :class="['project-item', {active: rightCurrentProjectId === project.id}]"
                      @click="selectQuarterlyProject(project.id)">
                      <i class="el-icon-folder"></i>
                      <span class="project-name">{{ project.projectName }}</span>
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- 右侧成员详情 -->
              <el-card class="member-card">
                <div slot="header" class="card-header">
                  <div class="member-header-info">
                    <h4 class="quarterly-title">{{ getCurrentProjectName() }} - 成员评价</h4>
                  </div>
                </div>

                <div v-loading="rightLoading" class="member-content">
                  <div v-if="!rightCurrentProjectId" class="placeholder-info">
                    请在左侧选择一个项目
                  </div>
                  <div v-else-if="!rightMemberList[rightCurrentProjectId] || rightMemberList[rightCurrentProjectId].length === 0" class="placeholder-info">
                    该项目暂无成员或成员数据加载中...
                  </div>
                  <div v-else>
                    <!-- 配额信息显示 -->
                    <el-alert
                      :title="getRightQuotaInfoText()"
                      :type="getRightQuotaAlertType()"
                      :closable="false"
                      show-icon
                      style="margin-bottom: 15px;">
                    </el-alert>

                    <!-- 建议分值区间提示 -->
                    <el-alert
                      title="建议分值区间：非常满意：95分以上；比较满意：85-94分；满意：75-84分；不满意：74分及以下"
                      type="success"
                      :closable="false"
                      show-icon
                      style="margin-bottom: 15px;">
                    </el-alert>

                    <el-table
                      :data="rightMemberList[rightCurrentProjectId]"
                      border 
                      style="width: 100%"
                      row-key="_uid"
                      :cell-class-name="getCellClassName"
                      :row-class-name="getRowClassName">
                      <el-table-column label="序号" type="index" width="50" align="center">
                        <template slot-scope="scope">
                          {{ scope.$index + 1 }}
                        </template>
                      </el-table-column>
                      <el-table-column label="成员姓名" prop="nickName" min-width="120" align="center">
                        <template slot-scope="scope">
                          <span v-if="scope.row.nickName && scope.row.nickName !== scope.row.userName">
                            {{ scope.row.nickName }}
                          </span>
                          <span v-else>
                            {{ scope.row.userName }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="员工编号" prop="userName" min-width="120" align="center">
                        <template slot-scope="scope">
                          {{ scope.row.userName }}
                        </template>
                      </el-table-column>
                      <el-table-column label="所属部门" prop="deptName" min-width="150" align="center">
                        <template slot-scope="scope">
                          <span v-if="scope.row.deptName && scope.row.deptName !== '未知部门'">
                            {{ scope.row.deptName }}
                          </span>
                          <span v-else style="color: #999;">
                            未知部门
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="评分" min-width="220" align="center">
                        <template slot-scope="scope">
                          <el-input-number
                            v-model="scope.row.newScore"
                            :min="0"
                            :max="100"
                            @change="handleScoreChange(scope.row)"
                            size="small"
                            controls-position="right">
                          </el-input-number>
                          <span class="score-unit">分</span>
                          <span class="score-limit">(0-100)</span>
                        </template>
                      </el-table-column>
                    </el-table>
                    
                    <!-- 提交按钮 -->
                    <div class="table-footer-wrapper">
                      <div class="table-footer">
                        <el-button
                          type="primary"
                          :disabled="!rightCanSubmit"
                          @click="batchSaveQuarterlyEvaluations"
                          size="medium">
                          <i class="el-icon-check"></i> 提交季度评价
                        </el-button>
                        <span v-if="!rightCanSubmit" class="submit-warning">
                          {{ getRightSubmitWarningText() }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 评价对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="项目名称">
          <span>{{ form.projectName }}</span>
        </el-form-item>
        <el-form-item label="被评价人">
          <span>{{ form.userName }}</span>
        </el-form-item>
        <el-form-item label="评价月份">
          <span>{{ form.evaluationMonth }}</span>
        </el-form-item>
        <el-form-item label="评分" prop="score">
          <el-input-number
            v-model="form.score"
            :min="0"
            :max="100"
            controls-position="right">
          </el-input-number>
          <span class="score-unit">分</span>
          <span class="score-limit">(0-100)</span>
        </el-form-item>
        <el-form-item label="评价意见" prop="comments">
          <el-input
            type="textarea"
            v-model="form.comments"
            placeholder="请输入评价意见"
            maxlength="500"
            show-word-limit
            :rows="4">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUserLeadProjects } from "@/api/system/info";
import { addEvaluation, listEvaluation, updateEvaluation } from "@/api/system/evaluation";
import { listAllUser } from "@/api/system/user";
import { listAllParticipationWithUserInfo } from "@/api/system/participation";
import { listDept } from "@/api/system/dept";


export default {
  name: "LeaderEvaluation",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 项目选项
      projectOptions: [],
      // 当前选中的项目ID
      currentProjectId: null,
      // 成员列表 (按项目ID分组)
      memberList: {},
      // 所有部门信息（用于判断机构负责人）
      allDepts: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期选择器配置
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() > new Date().setDate(1);
        }
      },
      // 查询参数
      queryParams: {
        evaluationMonth: this.getCurrentMonth()
      },
      // 表单参数
      form: {
        projectId: null,
        projectName: null,
        evaluatorId: null,
        evaluateeId: null,
        userName: null,
        score: 0,
        evaluationMonth: null,
        evaluationType: "project_leader",
        comments: null
      },
      // 表单校验
      rules: {
        score: [
          { required: true, message: "评分不能为空", trigger: "change" }
        ]
      },
      // 是否有未保存的评分
      hasUnsavedChanges: false,
      // 所有用户信息 (用于获取nickName)
      userMap: {},
      // 可用部门用户
      availableDeptUsers: [],
      
      // 标签页控制
      activeTab: 'monthly', // 默认显示月度评价
      
      // 右侧卡片独立数据变量（保留向后兼容）
      rightProjectOptions: [],
      rightCurrentProjectId: null,
      rightMemberList: {},
      rightLoading: false
    }
  },
  computed: {
    // 当前标签页的项目列表
    currentProjectOptions() {
      return this.activeTab === 'monthly' ? this.projectOptions : this.rightProjectOptions;
    },

    // 当前标签页的成员列表
    currentMemberList() {
      if (this.activeTab === 'monthly') {
        return this.currentProjectId ? this.memberList[this.currentProjectId] || [] : [];
      } else {
        return this.rightCurrentProjectId ? this.rightMemberList[this.rightCurrentProjectId] || [] : [];
      }
    },

    // 当前标签页的加载状态
    currentLoading() {
      return this.activeTab === 'monthly' ? this.loading : this.rightLoading;
    },

    // 当前标签页的提交状态
    currentCanSubmit() {
      if (this.activeTab === 'monthly') {
        return this.canSubmit;
      } else {
        return this.rightCanSubmit;
      }
    },

    // 获取当前活动的项目ID
    getActiveProjectId() {
      return this.activeTab === 'monthly' ? this.currentProjectId : this.rightCurrentProjectId;
    },

    // 检查是否可以提交（月度）
    canSubmit() {
      if (!this.currentProjectId || !this.memberList[this.currentProjectId]) {
        return false;
      }

      const currentMembers = this.memberList[this.currentProjectId];

      // 检查是否有零分评价
      const zeroScoreMembers = currentMembers.filter(member => member.newScore === 0);
      if (zeroScoreMembers.length > 0) {
        return false;
      }

      // 检查95分以上配额限制
      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      if (highScoreMembers.length > maxHighScoreCount) {
        return false;
      }

      return true;
    },

    // 右侧卡片的提交检查（季度）
    rightCanSubmit() {
      if (!this.rightCurrentProjectId || !this.rightMemberList[this.rightCurrentProjectId]) {
        return false;
      }

      const currentMembers = this.rightMemberList[this.rightCurrentProjectId];

      // 检查是否有零分评价
      const zeroScoreMembers = currentMembers.filter(member => member.newScore === 0);
      if (zeroScoreMembers.length > 0) {
        return false;
      }

      // 检查95分以上配额限制
      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      if (highScoreMembers.length > maxHighScoreCount) {
        return false;
      }

      return true;
    }
  },
  created() {
    // 先加载部门信息，再加载项目数据
    this.loadAllDepts().then(() => {
      // 直接加载项目数据，用户信息由后端API关联返回
      this.getUserLeadProjects();
      // 为右侧卡片加载项目数据
      this.getRightUserLeadProjects();
    });
  },
  methods: {
    // 获取当前月份，格式为yyyy-MM
    getCurrentMonth() {
      const date = new Date();
      const year = date.getFullYear();
      let month = date.getMonth() + 1;
      month = month < 10 ? '0' + month : month;
      return `${year}-${month}`;
    },
    // 获取所有用户信息（不受数据权限限制）
    loadDeptUsers() {
      // 如果已经加载过且数据不为空，则不再重新加载
      if (this.availableDeptUsers && this.availableDeptUsers.length > 0) {
        return Promise.resolve(this.availableDeptUsers);
      }

      // 使用listAllUser加载所有用户信息，不受数据权限限制
      return listAllUser({
        includeDeleted: false,
        includeDept: true
      }).then(response => {
        const users = response.rows || [];

        // 建立userName到用户信息的映射
        this.userMap = {};
        users.forEach(user => {
          this.userMap[user.userName] = user;
        });

        // 过滤出有部门信息的用户
        this.availableDeptUsers = users.filter(user => user.dept && user.dept.deptName);

        console.log(`加载了 ${users.length} 个用户信息，其中 ${this.availableDeptUsers.length} 个有部门信息`);

        return this.availableDeptUsers;
      }).catch(error => {
        console.error('加载用户信息失败:', error);
        return [];
      });
    },
    // 加载所有用户信息，用于获取nickName
    loadAllUsers() {
      return this.loadDeptUsers();
    },
    // 加载所有部门信息，用于判断机构负责人
    loadAllDepts() {
      return new Promise((resolve, reject) => {
        listDept({}).then(response => {
          this.allDepts = response.data || [];
          resolve();
        }).catch(error => {
          this.allDepts = [];
          resolve(); // 即使失败也继续执行
        });
      });
    },
    // 判断用户是否为机构负责人
    isDeptLeader(userName) {
      return this.allDepts.some(dept => dept.leader === userName);
    },

    // 标签页切换处理
    handleTabClick(tab) {
      this.activeTab = tab.name;
      
      // 切换标签页时加载对应数据
      if (tab.name === 'quarterly') {
        // 每次切换到季度评价时都重新加载数据
        this.getRightUserLeadProjects().then(() => {
          // 如果之前有选中的项目，重新加载该项目的成员数据
          if (this.rightCurrentProjectId && this.rightProjectOptions.some(p => p.id === this.rightCurrentProjectId)) {
            this.loadQuarterlyProjectMembers(this.rightCurrentProjectId);
          }
        });
      }
    },

    // 通用的项目选择处理（用于其他地方调用）
    selectProject(projectId) {
      // 根据当前标签页调用对应的专用方法
      if (this.activeTab === 'monthly') {
        this.selectMonthlyProject(projectId);
      } else {
        this.selectQuarterlyProject(projectId);
      }
    },

    // 月度评价专用的项目选择方法
    selectMonthlyProject(projectId) {
      console.log('selectMonthlyProject called:', projectId, '当前月度项目:', this.currentProjectId);

      if (this.currentProjectId === projectId) {
        console.log('月度评价 - 项目已选中，无需切换');
        return;
      }

      console.log('月度评价 - 切换到项目:', projectId);
      this.currentProjectId = projectId;

      // 初始化成员列表
      if (!this.memberList[projectId]) {
        this.$set(this.memberList, projectId, []);
      }

      // 加载月度评价项目成员数据
      this.loadProjectMembers(projectId);
    },

    // 季度评价专用的项目选择方法
    selectQuarterlyProject(projectId) {
      console.log('selectQuarterlyProject called:', projectId, '当前季度项目:', this.rightCurrentProjectId);

      if (this.rightCurrentProjectId === projectId) {
        console.log('季度评价 - 项目已选中，无需切换');
        return;
      }

      console.log('季度评价 - 切换到项目:', projectId);
      this.rightCurrentProjectId = projectId;

      // 初始化成员列表
      if (!this.rightMemberList[projectId]) {
        this.$set(this.rightMemberList, projectId, []);
      }

      // 加载季度评价项目成员数据
      this.loadQuarterlyProjectMembers(projectId);
    },

    // 统一的分数变更处理（用于模板中）
    handleScoreChange(row) {
      if (this.activeTab === 'monthly') {
        this.handleMonthlyScoreChange(row);
      } else {
        this.handleQuarterlyScoreChange(row);
      }
    },

    // 月度评价分数变更
    handleMonthlyScoreChange(row) {
      if (row.newScore < 0) {
        row.newScore = 0;
      } else if (row.newScore > 100) {
        row.newScore = 100;
      }

      if (row.newScore >= 95) {
        const currentMembers = this.memberList[this.currentProjectId] || [];
        const totalMembers = currentMembers.length;
        const highScoreCount = currentMembers.filter(member => member.newScore >= 95).length;
        const maxHighScoreCount = Math.round(totalMembers * 0.3);

        if (highScoreCount > maxHighScoreCount) {
          this.$message({
            message: `95分以上人员不能超过项目成员总数的30%（当前项目共${totalMembers}人，最多允许${maxHighScoreCount}人获得95分以上）`,
            type: 'warning',
            duration: 4000
          });

          if (row.evaluated) {
            row.newScore = row.currentScore;
          } else {
            row.newScore = 0;
          }
          return;
        }
      }

      if (row.evaluated) {
        row.changed = row.currentScore !== row.newScore;
      } else {
        row.changed = true;
      }
    },

    // 季度评价分数变更
    handleQuarterlyScoreChange(row) {
      if (row.newScore < 0) {
        row.newScore = 0;
      } else if (row.newScore > 100) {
        row.newScore = 100;
      }

      if (row.newScore >= 95) {
        const currentMembers = this.rightMemberList[this.currentProjectId] || [];
        const totalMembers = currentMembers.length;
        const highScoreCount = currentMembers.filter(member => member.newScore >= 95).length;
        const maxHighScoreCount = Math.round(totalMembers * 0.3);

        if (highScoreCount > maxHighScoreCount) {
          this.$message({
            message: `95分以上人员不能超过项目成员总数的30%（当前项目共${totalMembers}人，最多允许${maxHighScoreCount}人获得95分以上）`,
            type: 'warning',
            duration: 4000
          });

          if (row.evaluated) {
            row.newScore = row.currentScore;
          } else {
            row.newScore = 0;
          }
          return;
        }
      }

      if (row.evaluated) {
        row.changed = row.currentScore !== row.newScore;
      } else {
        row.changed = true;
      }
    },

    // 统一的批量保存评分（用于模板中）
    batchSaveEvaluations() {
      if (this.activeTab === 'monthly') {
        this.batchSaveMonthlyEvaluations();
      } else {
        this.batchSaveQuarterlyEvaluations();
      }
    },

    // 获取当前标签页的配额信息
    getCurrentQuotaInfoText() {
      if (this.activeTab === 'monthly') {
        return this.getQuotaInfoText();
      } else {
        return this.getRightQuotaInfoText();
      }
    },

    // 获取当前标签页的配额类型
    getCurrentQuotaAlertType() {
      if (this.activeTab === 'monthly') {
        return this.getQuotaAlertType();
      } else {
        return this.getRightQuotaAlertType();
      }
    },

    // 获取当前标签页的提交警告文本
    getCurrentSubmitWarningText() {
      if (this.activeTab === 'monthly') {
        return this.getSubmitWarningText();
      } else {
        return this.getRightSubmitWarningText();
      }
    },
    // 更新成员的nickName和部门信息
    updateMembersNickName(projectId) {
      if (!this.memberList[projectId]) return;
      
      const members = this.memberList[projectId];
      members.forEach(member => {
        const user = this.userMap[member.userName];
        if (user) {
          member.nickName = user.nickName;
          // 更新部门信息
          if (user.dept && user.dept.deptName) {
            member.deptName = user.dept.deptName;
          }
        }
      });
      
      // 更新成员列表
      this.$set(this.memberList, projectId, [...members]);
    },
    // 获取用户负责的项目
    getUserLeadProjects() {
      this.loading = true;
      return new Promise((resolve, reject) => {
        listUserLeadProjects().then(response => {
          // 对项目数据进行去重处理，基于id
          const projectMap = new Map();
          (response.rows || []).forEach(project => {
            // 只保留唯一的项目ID
            if (project && project.id && !projectMap.has(project.id)) {
              projectMap.set(project.id, project);
            }
          });
          
          // 将Map转换回数组并排序，确保每次顺序一致
          let projectArray = Array.from(projectMap.values());
          
          // 按项目ID或项目名称排序
          projectArray.sort((a, b) => {
            // 优先按项目ID排序（如果ID是数字）
            if (!isNaN(a.id) && !isNaN(b.id)) {
              return a.id - b.id;
            }
            // 如果ID不是数字或相同，则按项目名称排序
            return (a.projectName || '').localeCompare(b.projectName || '');
          });
          
          this.projectOptions = projectArray;
          
          this.loading = false;
          if (this.projectOptions.length > 0 && !this.currentProjectId) {
            // 默认选中第一个项目，但仅当没有选择项目时
            this.selectProject(this.projectOptions[0].id);
          }
          resolve(this.projectOptions);
        }).catch(error => {
          this.loading = false;
          reject(error);
        });
      });
    },
    // 选择项目
    selectProject(projectId) {
      if (this.currentProjectId === projectId) return;
      
      // 检查是否有未保存的更改
      if (this.hasUnsavedChanges) {
        this.$confirm('有未保存的评分，切换项目将丢失这些更改，是否继续?', '提示', {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.currentProjectId = projectId;
          this.hasUnsavedChanges = false;
          // 预先初始化一个空的成员列表，避免undefined
          if (!this.memberList[projectId]) {
            this.$set(this.memberList, projectId, []);
          }
          this.loadProjectMembers(projectId);
        }).catch(() => {});
      } else {
        this.currentProjectId = projectId;
        // 预先初始化一个空的成员列表，避免undefined
        if (!this.memberList[projectId]) {
          this.$set(this.memberList, projectId, []);
        }
        this.loadProjectMembers(projectId);
      }
    },
    // 加载项目成员
    loadProjectMembers(projectId) {
      if (!projectId) return;

      this.loading = true;

      // 保存当前请求的项目ID，用于后续验证
      const requestProjectId = projectId;

      // 获取当前选择的月份
      const month = this.queryParams.evaluationMonth;
      if (!month) {
        this.$modal.msgError("请先选择评价月份");
        this.loading = false;
        return;
      }

      // 使用新的API直接获取包含用户信息的项目成员数据
      listAllParticipationWithUserInfo({
        projectId: projectId,
        month: month
      }).then(response => {
        // 检查当前处理的项目ID是否仍然是用户选中的项目
        if (requestProjectId !== this.currentProjectId) {
          this.loading = false;
          return;
        }

        // 从后端获取已经关联好用户信息的数据
        let allParticipations = response.data || [];

        // 按照userName进行分组去重，只保留每个用户的一条记录
        const participationMap = new Map();

        allParticipations.forEach(part => {
          if (!participationMap.has(part.userName)) {
            participationMap.set(part.userName, part);
          }
        });

        // 获取当前项目信息，用于确定项目负责人
        const currentProject = this.projectOptions.find(p => p.id === projectId);
        let projectLeaderUserName = null;

        // 从项目成员列表中找到负责人的员工编号
        if (currentProject && currentProject.memberList) {
          const leader = currentProject.memberList.find(member =>
            member.role === '负责人' || (typeof member.nickName === 'string' && member.nickName.includes('-负责人'))
          );
          if (leader) {
            projectLeaderUserName = leader.userName;
          }
        }

        // 将参与度记录转换为成员记录，数据已经包含用户信息
        let members = Array.from(participationMap.values()).map((part, index) => {
          // 检查数据完整性
          if (!part.userName) {
            console.warn('发现用户名为空的参与度记录:', part);
          }
          if (!part.userId) {
            console.warn('发现用户ID为空的参与度记录:', part);
          }
          if (!part.deptName || part.deptName === '未知部门') {
            console.warn('发现部门信息缺失的用户:', part.userName, '部门名称:', part.deptName);
          }

          return {
            userName: part.userName,
            userId: part.userId,
            nickName: part.nickName || part.userName, // 后端已经关联了用户信息
            deptName: part.deptName || '未知部门', // 后端已经关联了部门信息
            deptId: part.deptId,
            remark: part.remark || '', // 添加remark字段用于判断机构负责人
            _uid: `${projectId}-${part.userName || 'unknown'}-${index}`,
            newScore: 0, // 默认评分为0
            projectId: projectId,
            projectName: this.getCurrentProjectName()
          };
        });

        // 过滤掉项目负责人、机构负责人和特聘专家
        members = members.filter(member => {
          // 过滤掉当前项目的负责人
          if (projectLeaderUserName && member.userName === projectLeaderUserName) {
            return false;
          }

          // 过滤掉机构负责人（通过部门leader字段判断）
          if (this.isDeptLeader(member.userName)) {
            return false;
          }

          // 过滤掉特聘专家（remark='特聘专家'）
          if (member.remark === '特聘专家') {
            return false;
          }

          return true;
        });

        // 按项目ID存储项目成员
        this.$set(this.memberList, projectId, [...members]);

        // 如果有成员，加载评价信息
        if (members.length > 0 && this.queryParams.evaluationMonth) {
          this.getEvaluations(projectId, members);
        } else {
          this.loading = false;
        }
      }).catch(error => {
        console.error('加载项目成员失败:', error);
        this.loading = false;
      });
    },
    // 获取评价记录
    getEvaluations(projectId, members) {
      const month = this.queryParams.evaluationMonth;
      
      // 保存当前请求的项目ID，用于后续验证
      const requestProjectId = projectId;
      
      this.loading = true;
      listEvaluation({
        projectId: projectId,
        evaluationMonth: month,
        evaluationType: "project_leader"
      }).then(response => {
        // 检查当前处理的项目ID是否仍然是用户选中的项目
        // 如果用户已经切换到其他项目，则不处理这个响应
        if (requestProjectId !== this.currentProjectId) {
          this.loading = false;
          return;
        }
        
        // 获取并去重评价记录
        let evaluations = response.rows || [];
        
        // 去除重复的评价记录（基于evaluateeId）
        const uniqueEvaluations = [];
        const evaluationMap = new Map();
        
        evaluations.forEach(evaluation => {
          const key = `${evaluation.evaluateeId}`;
          if (!evaluationMap.has(key)) {
            evaluationMap.set(key, evaluation);
            uniqueEvaluations.push(evaluation);
          } else {
            // 如果发现重复，保留ID较大的记录（通常是较新的）
            const existingEval = evaluationMap.get(key);
            if (evaluation.id > existingEval.id) {
              evaluationMap.set(key, evaluation);
              // 替换数组中的记录
              const index = uniqueEvaluations.indexOf(existingEval);
              if (index !== -1) {
                uniqueEvaluations[index] = evaluation;
              }
            }
          }
        });
        
        evaluations = uniqueEvaluations;
        
        // 找到当前项目
        const project = this.projectOptions.find(p => p.id === projectId);
        const projectName = project ? project.projectName : "";
        
        // 创建员工编号到评价记录的映射，方便查找
        const evaluationMap2 = {};
        evaluations.forEach(evaluation => {
          evaluationMap2[evaluation.evaluateeId] = evaluation;
        });
        
        // 把评价信息合并到成员列表
        members.forEach(member => {
          // 获取用户的完整信息，包括userId
          const userInfo = this.userMap[member.userName];
          if (userInfo) {
            member.userId = userInfo.userId;
          }
          
          // 查找当前月份该成员的评价记录
          const evaluation = evaluationMap2[member.userId];
          
          if (evaluation) {
            member.evaluated = true;
            member.currentScore = evaluation.score;
            member.newScore = evaluation.score; // 添加新分数字段，用于跟踪更改
            member.evaluationId = evaluation.id;
            member.comments = evaluation.comments || "";
          } else {
            member.evaluated = false;
            member.currentScore = null;
            member.newScore = 0; // 默认分数为0分
            member.comments = "";
          }
          
          member.changed = false; // 标记是否有更改
          member.projectId = projectId;
          member.projectName = projectName;
          member.evaluationMonth = month; // 添加评价月份
          // 确保保留_uid字段
        });
        
        // 更新成员列表
        this.$set(this.memberList, projectId, [...members]); // 使用展开运算符创建新数组，确保Vue检测到变化
        this.hasUnsavedChanges = false;
        this.loading = false;
      }).catch(error => {
        // 即使请求失败，也确保设置默认分数为0
        if (this.memberList[projectId]) {
          this.memberList[projectId].forEach(member => {
            if (!member.evaluated) {
              member.newScore = 0;
            }
          });
          
          this.$set(this.memberList, projectId, [...this.memberList[projectId]]);
        }
        this.loading = false;
      });
    },
    // 处理分数变更
    handleScoreChange(row) {
      // 确保分数在0-100范围内
      if (row.newScore < 0) {
        row.newScore = 0;
      } else if (row.newScore > 100) {
        row.newScore = 100;
      }

      // 检查95分以上配额限制
      if (row.newScore >= 95) {
        const currentMembers = this.memberList[this.currentProjectId] || [];
        const totalMembers = currentMembers.length;

        // 计算当前95分以上的人数（包括正在修改的这个人）
        const highScoreCount = currentMembers.filter(member => member.newScore >= 95).length;

        // 计算30%配额（四舍五入）
        const maxHighScoreCount = Math.round(totalMembers * 0.3);

        // 如果超过配额，阻止修改并提示
        if (highScoreCount > maxHighScoreCount) {
          this.$message({
            message: `95分以上人员不能超过项目成员总数的30%（当前项目共${totalMembers}人，最多允许${maxHighScoreCount}人获得95分以上）`,
            type: 'warning',
            duration: 4000
          });

          // 恢复到之前的分数
          if (row.evaluated) {
            row.newScore = row.currentScore;
          } else {
            row.newScore = 0;
          }
          return;
        }
      }

      // 标记为已更改
      if (row.evaluated) {
        row.changed = row.currentScore !== row.newScore;
      } else {
        row.changed = true;
      }

      // 更新是否有未保存的变更状态
      this.checkUnsavedChanges();
    },
    // 检查是否有未保存的更改
    checkUnsavedChanges() {
      if (!this.currentProjectId || !this.memberList[this.currentProjectId]) {
        this.hasUnsavedChanges = false;
        return;
      }
      
      this.hasUnsavedChanges = this.memberList[this.currentProjectId].some(member => member.changed);
    },
    // 批量保存月度评分
    batchSaveMonthlyEvaluations() {
      // 首先检查是否可以提交
      if (!this.canSubmit) {
        const warningText = this.getSubmitWarningText();
        this.$modal.msgError(`无法提交：${warningText}`);
        return;
      }

      if (!this.currentProjectId || !this.memberList[this.currentProjectId]) return;

      // 获取所有成员，不再需要过滤_isAddButton
      const currentMembers = this.memberList[this.currentProjectId];
      
      if (currentMembers.length === 0) {
        this.$message.info('没有可提交的评分');
        return;
      }
      
      // 检查重复人员
      const userNameMap = {};
      let hasDuplicate = false;
      let duplicateUserName = '';
      
      for (const member of currentMembers) {
        if (!member.userName) {
          this.$modal.msgError("存在未填写员工编号的记录，请完善信息后再提交");
          return;
        }
        
        if (userNameMap[member.userName]) {
          hasDuplicate = true;
          duplicateUserName = member.userName;
          break;
        }
        
        userNameMap[member.userName] = true;
      }
      
      if (hasDuplicate) {
        this.$modal.msgError(`存在重复的人员【${duplicateUserName}】，请检查后再提交`);
        return;
      }
      
      // 检查零分评价
      const zeroScoreMembers = currentMembers.filter(member => member.newScore === 0);
      if (zeroScoreMembers.length > 0) {
        const zeroScoreName = zeroScoreMembers[0].nickName || zeroScoreMembers[0].userName;
        this.$modal.msgError(`人员【${zeroScoreName}】的评分为0分，请评分后再提交`);
        return;
      }

      // 检查95分以上配额限制
      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      if (highScoreMembers.length > maxHighScoreCount) {
        this.$modal.msgError(`95分以上人员不能超过项目成员总数的30%（当前项目共${totalMembers}人，最多允许${maxHighScoreCount}人获得95分以上，当前有${highScoreMembers.length}人）`);
        return;
      }
      
      // 确保有评价人ID
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$modal.msgError("获取当前用户信息失败，请重新登录后再试");
        return;
      }
      
      // 确保选择了评价月份
      const evaluationMonth = this.queryParams.evaluationMonth;
      if (!evaluationMonth) {
        this.$modal.msgError("请先选择评价月份");
        return;
      }
      
      this.loading = true;
      
      // 查询本月已有的评价记录
      listEvaluation({
        projectId: this.currentProjectId,
        evaluationMonth: evaluationMonth,
        evaluationType: "project_leader"
      }).then(response => {
        const existingEvaluations = response.rows || [];
        
        // 创建员工编号到评价记录的映射
        const evaluationMap = {};
        existingEvaluations.forEach(evaluation => {
          evaluationMap[evaluation.evaluateeId] = evaluation;
        });
        
        // 创建保存请求
        const savePromises = currentMembers.map(member => {
          // 检查员工编号是否为空
          if (!member.userName) {
            return Promise.reject(new Error(`有成员的员工编号为空，请完善信息后再提交`));
          }
          
          // 查找该用户本月是否已有评价
          const existingEval = evaluationMap[member.userId];
          
          const data = {
            projectId: member.projectId,
            evaluatorId: evaluatorId,
            evaluateeId: member.userId,
            score: member.newScore,
            evaluationMonth: evaluationMonth,
            evaluationType: "project_leader",
            comments: member.comments || ""
          };
          
          if (existingEval) {
            // 如果本月已有评价，则更新
            data.id = existingEval.id;
            return updateEvaluation(data);
          } else {
            // 否则新增
            return addEvaluation(data);
          }
        });
        
        // 执行所有保存请求
        Promise.all(savePromises)
          .then(() => {
            this.$modal.msgSuccess("提交成功");
            
            // 完整刷新数据
            this.refreshData(this.currentProjectId);
          })
          .catch((error) => {
            this.$modal.msgError(error.message || "保存失败，请重试");
            this.loading = false;
          });
      }).catch(error => {
        this.$modal.msgError("获取评价记录失败，请重试");
        this.loading = false;
      });
    },
    // 获取当前项目名称
    getCurrentProjectName() {
      if (this.activeTab === 'monthly') {
        if (!this.currentProjectId) return "请选择项目";
        const project = this.projectOptions.find(p => p.id === this.currentProjectId);
        return project ? project.projectName : "未知项目";
      } else {
        if (!this.rightCurrentProjectId) return "请选择项目";
        const project = this.rightProjectOptions.find(p => p.id === this.rightCurrentProjectId);
        return project ? project.projectName : "未知项目";
      }
    },
    // 获取已评价成员数量
    getEvaluatedCount(projectId) {
      if (!this.memberList[projectId]) return 0;
      return this.memberList[projectId].filter(m => m.evaluated).length;
    },
    // 获取总成员数量
    getTotalCount(projectId) {
      if (!this.memberList[projectId]) return 0;
      return this.memberList[projectId].length;
    },
    // 获取状态标签类型
    getTagType(row) {
      if (row.evaluated) return 'success';
      return 'info';
    },
    // 获取状态文本
    getStatusText(row) {
      if (row.evaluated) return '已评价';
      return '未评价';
    },
    // 设置行的类名
    getRowClassName({row, rowIndex}) {
      if (row.evaluated) return 'evaluated-row';
      return '';
    },
    // 刷新项目列表
    refreshProjectList() {
      const currentId = this.currentProjectId;
      this.getUserLeadProjects();
      // 保持当前选中的项目
      if (currentId) {
        this.selectProject(currentId);
      }
    },
    // 搜索按钮操作
    handleQuery() {
      if (this.currentProjectId) {
        // 直接调用loadProjectMembers重新加载当前月份的成员数据
        this.loadProjectMembers(this.currentProjectId);
      }
    },
    // 重置按钮操作
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.evaluationMonth = this.getCurrentMonth();
      this.handleQuery();
    },
    // 打开评价对话框
    handleEvaluate(row) {
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$modal.msgError("获取当前用户信息失败，请重新登录后再试");
        return;
      }
      
      this.form = {
        projectId: row.projectId,
        projectName: row.projectName,
        evaluatorId: evaluatorId,
        evaluateeId: row.userId,
        userName: row.nickName || row.userName, // 显示昵称
        score: 0,
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluationType: "project_leader",
        comments: ""
      };
      this.title = "项目负责人评价";
      this.open = true;
    },
    // 查看评价详情
    handleViewEvaluation(row) {
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$modal.msgError("获取当前用户信息失败，请重新登录后再试");
        return;
      }
      
      this.form = {
        id: row.evaluationId,
        projectId: row.projectId,
        projectName: row.projectName,
        evaluatorId: evaluatorId,
        evaluateeId: row.userId,
        userName: row.nickName || row.userName, // 显示昵称
        score: row.currentScore,
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluationType: "project_leader",
        comments: row.comments || ""
      };
      this.title = "查看/修改评价";
      this.open = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        projectId: null,
        projectName: null,
        evaluatorId: null,
        evaluateeId: null,
        userName: null,
        score: 0,
        evaluationMonth: null,
        evaluationType: "project_leader",
        comments: null
      };
      this.resetForm("form");
    },
    // 提交评价
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保分数在0-100范围内
          if (this.form.score <= 0) {
            this.$modal.msgError("评分必须大于0");
            return;
          }
          if (this.form.score > 100) {
            this.form.score = 100;
          }

          // 检查95分以上配额限制
          if (this.form.score >= 95) {
            const currentMembers = this.memberList[this.currentProjectId] || [];
            const totalMembers = currentMembers.length;

            // 计算当前95分以上的人数（排除正在编辑的这个人）
            let highScoreCount = currentMembers.filter(member =>
              member.newScore >= 95 && member.userId !== this.form.evaluateeId
            ).length;

            // 如果当前评分是95分以上，则加1
            if (this.form.score >= 95) {
              highScoreCount += 1;
            }

            // 计算30%配额（四舍五入）
            const maxHighScoreCount = Math.round(totalMembers * 0.3);

            // 如果超过配额，阻止提交
            if (highScoreCount > maxHighScoreCount) {
              this.$modal.msgError(`95分以上人员不能超过项目成员总数的30%（当前项目共${totalMembers}人，最多允许${maxHighScoreCount}人获得95分以上）`);
              return;
            }
          }
          
          this.loading = true;
          
          // 保存当前项目ID
          const currentProjectId = this.form.projectId;
          
          if (this.form.id) {
            updateEvaluation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              
              // 完整刷新数据
              this.refreshData(currentProjectId);
            }).catch(error => {
              this.$modal.msgError("修改失败，请重试");
              this.loading = false;
            });
          } else {
            addEvaluation(this.form).then(response => {
              this.$modal.msgSuccess("评价成功");
              this.open = false;
              
              // 完整刷新数据
              this.refreshData(currentProjectId);
            }).catch(error => {
              this.$modal.msgError("评价失败，请重试");
              this.loading = false;
            });
          }
        }
      });
    },
    
    // 完整刷新数据
    refreshData(projectId) {
      this.loading = true;
      
      // 先重新获取项目列表
      this.getUserLeadProjects().then(() => {
        // 如果指定的项目仍然存在，则重新选择它
        if (projectId && this.projectOptions.some(p => p.id === projectId)) {
          this.currentProjectId = projectId;
          // 重新加载该项目的成员和评价数据
          this.loadProjectMembers(projectId);
        } else if (this.projectOptions.length > 0) {
          // 如果原项目不存在，则选择第一个项目
          this.selectProject(this.projectOptions[0].id);
        } else {
          this.loading = false;
        }
      }).catch(error => {
        this.loading = false;
      });
    },
    // 设置单元格的类名
    getCellClassName({row, column, rowIndex, columnIndex}) {
      return '';
    },

    // 获取配额信息文本
    getQuotaInfoText() {
      if (!this.currentProjectId || !this.memberList[this.currentProjectId]) {
        return '配额信息加载中...';
      }

      const currentMembers = this.memberList[this.currentProjectId];
      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      return `高分配额限制：95分以上人员不能超过项目成员总数的30%（当前项目共${totalMembers}人，最多允许${maxHighScoreCount}人获得95分以上，已使用${highScoreMembers.length}人）`;
    },

    // 获取配额提示类型
    getQuotaAlertType() {
      if (!this.currentProjectId || !this.memberList[this.currentProjectId]) {
        return 'info';
      }

      const currentMembers = this.memberList[this.currentProjectId];
      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      if (highScoreMembers.length >= maxHighScoreCount) {
        return 'warning';
      } else if (highScoreMembers.length >= maxHighScoreCount * 0.8) {
        return 'info';
      } else {
        return 'success';
      }
    },

    // 获取提交警告文本
    getSubmitWarningText() {
      if (!this.currentProjectId || !this.memberList[this.currentProjectId]) {
        return '';
      }

      const currentMembers = this.memberList[this.currentProjectId];

      // 检查是否有零分评价
      const zeroScoreMembers = currentMembers.filter(member => member.newScore === 0);
      if (zeroScoreMembers.length > 0) {
        const memberNames = zeroScoreMembers.map(m => m.nickName || m.userName).join('、');
        return `存在未评分人员：${memberNames}`;
      }

      // 检查95分以上配额限制
      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      if (highScoreMembers.length > maxHighScoreCount) {
        return `95分以上人员超过配额限制（${highScoreMembers.length}/${maxHighScoreCount}）`;
      }

      return '';
    },

    // 获取右侧卡片的项目名称
    getRightCurrentProjectName() {
      if (!this.rightCurrentProjectId) return "请选择项目";
      const project = this.rightProjectOptions.find(p => p.id === this.rightCurrentProjectId);
      return project ? project.projectName : "未知项目";
    },

    // 获取右侧卡片的项目列表
    getRightUserLeadProjects() {
      this.rightLoading = true;
      return new Promise((resolve, reject) => {
        listUserLeadProjects().then(response => {
          const projectMap = new Map();
          (response.rows || []).forEach(project => {
            if (project && project.id && !projectMap.has(project.id)) {
              projectMap.set(project.id, project);
            }
          });
          
          let projectArray = Array.from(projectMap.values());
          
          projectArray.sort((a, b) => {
            if (!isNaN(a.id) && !isNaN(b.id)) {
              return a.id - b.id;
            }
            return (a.projectName || '').localeCompare(b.projectName || '');
          });
          
          this.rightProjectOptions = projectArray;
          
          this.rightLoading = false;
          if (this.rightProjectOptions.length > 0 && !this.rightCurrentProjectId) {
            this.selectRightProject(this.rightProjectOptions[0].id);
          }
          resolve(this.rightProjectOptions);
        }).catch(error => {
          this.rightLoading = false;
          reject(error);
        });
      });
    },

    // 选择右侧卡片项目
    selectRightProject(projectId) {
      if (this.rightCurrentProjectId === projectId) return;
      
      this.rightCurrentProjectId = projectId;
      if (!this.rightMemberList[projectId]) {
        this.$set(this.rightMemberList, projectId, []);
      }
      this.loadRightProjectMembers(projectId);
    },

    // 加载右侧卡片项目成员
    loadRightProjectMembers(projectId) {
      if (!projectId) return;

      this.rightLoading = true;
      const requestProjectId = projectId;

      // 使用硬编码的时间 "2022-08"
      const month = "2022-08";

      listAllParticipationWithUserInfo({
        projectId: projectId,
        month: month
      }).then(response => {
        if (requestProjectId !== this.rightCurrentProjectId) {
          this.rightLoading = false;
          return;
        }

        let allParticipations = response.data || [];
        const participationMap = new Map();

        allParticipations.forEach(part => {
          if (!participationMap.has(part.userName)) {
            participationMap.set(part.userName, part);
          }
        });

        const currentProject = this.rightProjectOptions.find(p => p.id === projectId);
        let projectLeaderUserName = null;

        if (currentProject && currentProject.memberList) {
          const leader = currentProject.memberList.find(member =>
            member.role === '负责人' || (typeof member.nickName === 'string' && member.nickName.includes('-负责人'))
          );
          if (leader) {
            projectLeaderUserName = leader.userName;
          }
        }

        let members = Array.from(participationMap.values()).map((part, index) => {
          return {
            userName: part.userName,
            userId: part.userId,
            nickName: part.nickName || part.userName,
            deptName: part.deptName || '未知部门',
            deptId: part.deptId,
            remark: part.remark || '',
            _uid: `right-${projectId}-${part.userName || 'unknown'}-${index}`,
            newScore: 0,
            projectId: projectId,
            projectName: this.getRightCurrentProjectName()
          };
        });

        // 过滤掉项目负责人、机构负责人和特聘专家
        members = members.filter(member => {
          if (projectLeaderUserName && member.userName === projectLeaderUserName) {
            return false;
          }
          if (this.isDeptLeader(member.userName)) {
            return false;
          }
          if (member.remark === '特聘专家') {
            return false;
          }
          return true;
        });

        this.$set(this.rightMemberList, projectId, [...members]);

        if (members.length > 0) {
          this.getRightEvaluations(projectId, members);
        } else {
          this.rightLoading = false;
        }
      }).catch(error => {
        console.error('加载右侧卡片项目成员失败:', error);
        this.rightLoading = false;
      });
    },

    // 获取右侧卡片评价记录
    getRightEvaluations(projectId, members) {
      const month = "2022-08";
      const requestProjectId = projectId;
      
      this.rightLoading = true;
      listEvaluation({
        projectId: projectId,
        evaluationMonth: month,
        evaluationType: "quarterly_manager"
      }).then(response => {
        if (requestProjectId !== this.rightCurrentProjectId) {
          this.rightLoading = false;
          return;
        }
        
        let evaluations = response.rows || [];
        const uniqueEvaluations = [];
        const evaluationMap = new Map();
        
        evaluations.forEach(evaluation => {
          const key = `${evaluation.evaluateeId}`;
          if (!evaluationMap.has(key)) {
            evaluationMap.set(key, evaluation);
            uniqueEvaluations.push(evaluation);
          } else {
            const existingEval = evaluationMap.get(key);
            if (evaluation.id > existingEval.id) {
              evaluationMap.set(key, evaluation);
              const index = uniqueEvaluations.indexOf(existingEval);
              if (index !== -1) {
                uniqueEvaluations[index] = evaluation;
              }
            }
          }
        });
        
        evaluations = uniqueEvaluations;
        
        const project = this.rightProjectOptions.find(p => p.id === projectId);
        const projectName = project ? project.projectName : "";
        
        const evaluationMap2 = {};
        evaluations.forEach(evaluation => {
          evaluationMap2[evaluation.evaluateeId] = evaluation;
        });
        
        members.forEach(member => {
          const userInfo = this.userMap[member.userName];
          if (userInfo) {
            member.userId = userInfo.userId;
          }
          
          const evaluation = evaluationMap2[member.userId];
          
          if (evaluation) {
            member.evaluated = true;
            member.currentScore = evaluation.score;
            member.newScore = evaluation.score;
            member.evaluationId = evaluation.id;
            member.comments = evaluation.comments || "";
          } else {
            member.evaluated = false;
            member.currentScore = null;
            member.newScore = 0;
            member.comments = "";
          }
          
          member.changed = false;
          member.projectId = projectId;
          member.projectName = projectName;
          member.evaluationMonth = month;
        });
        
        this.$set(this.rightMemberList, projectId, [...members]);
        this.rightLoading = false;
      }).catch(error => {
        if (this.rightMemberList[projectId]) {
          this.rightMemberList[projectId].forEach(member => {
            if (!member.evaluated) {
              member.newScore = 0;
            }
          });
          
          this.$set(this.rightMemberList, projectId, [...this.rightMemberList[projectId]]);
        }
        this.rightLoading = false;
      });
    },

    // 加载季度评价项目成员（使用硬编码2022-08时间）
    loadQuarterlyProjectMembers(projectId) {
      if (!projectId) return;

      console.log('loadQuarterlyProjectMembers called:', projectId);
      this.rightLoading = true;

      // 保存当前请求的项目ID，用于后续验证
      const requestProjectId = projectId;

      // 使用硬编码的时间 "2022-08" 进行季度评价
      const month = "2022-08";

      // 使用新的API直接获取包含用户信息的项目成员数据
      listAllParticipationWithUserInfo({
        projectId: projectId,
        month: month
      }).then(response => {
        // 检查当前处理的项目ID是否仍然是用户选中的项目
        if (requestProjectId !== this.rightCurrentProjectId) {
          this.rightLoading = false;
          return;
        }

        // 从后端获取已经关联好用户信息的数据
        let allParticipations = response.data || [];

        // 按照userName进行分组去重，只保留每个用户的一条记录
        const participationMap = new Map();

        allParticipations.forEach(part => {
          if (!participationMap.has(part.userName)) {
            participationMap.set(part.userName, part);
          }
        });

        // 获取当前项目信息，用于确定项目负责人
        const currentProject = this.rightProjectOptions.find(p => p.id === projectId);
        let projectLeaderUserName = null;

        // 从项目成员列表中找到负责人的员工编号
        if (currentProject && currentProject.memberList) {
          const leader = currentProject.memberList.find(member =>
            member.role === '负责人' || (typeof member.nickName === 'string' && member.nickName.includes('-负责人'))
          );
          if (leader) {
            projectLeaderUserName = leader.userName;
          }
        }

        // 将参与度记录转换为成员记录，数据已经包含用户信息
        let members = Array.from(participationMap.values()).map((part, index) => {
          // 检查数据完整性
          if (!part.userName) {
            console.warn('发现用户名为空的参与度记录:', part);
          }
          if (!part.userId) {
            console.warn('发现用户ID为空的参与度记录:', part);
          }
          if (!part.deptName || part.deptName === '未知部门') {
            console.warn('发现部门信息缺失的用户:', part.userName, '部门名称:', part.deptName);
          }

          return {
            userName: part.userName,
            userId: part.userId,
            nickName: part.nickName || part.userName, // 后端已经关联了用户信息
            deptName: part.deptName || '未知部门', // 后端已经关联了部门信息
            deptId: part.deptId,
            remark: part.remark || '', // 添加remark字段用于判断机构负责人
            _uid: `quarterly-${projectId}-${part.userName || 'unknown'}-${index}`,
            newScore: 0, // 默认评分为0
            projectId: projectId,
            projectName: this.getCurrentProjectName()
          };
        });

        // 过滤掉项目负责人、机构负责人和特聘专家
        members = members.filter(member => {
          // 过滤掉当前项目的负责人
          if (projectLeaderUserName && member.userName === projectLeaderUserName) {
            return false;
          }

          // 过滤掉机构负责人（通过部门leader字段判断）
          if (this.isDeptLeader(member.userName)) {
            return false;
          }

          // 过滤掉特聘专家（remark='特聘专家'）
          if (member.remark === '特聘专家') {
            return false;
          }

          return true;
        });

        // 按项目ID存储项目成员到右侧卡片数据
        this.$set(this.rightMemberList, projectId, [...members]);

        // 如果有成员，加载评价信息
        if (members.length > 0) {
          this.getQuarterlyEvaluations(projectId, members);
        } else {
          this.rightLoading = false;
        }
      }).catch(error => {
        console.error('加载季度项目成员失败:', error);
        this.rightLoading = false;
      });
    },

    // 获取季度评价记录（使用硬编码2022-08时间）
    getQuarterlyEvaluations(projectId, members) {
      const month = "2022-08"; // 硬编码使用2022-08
      
      // 保存当前请求的项目ID，用于后续验证
      const requestProjectId = projectId;
      
      this.rightLoading = true;
      listEvaluation({
        projectId: projectId,
        evaluationMonth: month,
        evaluationType: "quarterly_manager"
      }).then(response => {
        // 检查当前处理的项目ID是否仍然是用户选中的项目
        // 如果用户已经切换到其他项目，则不处理这个响应
        if (requestProjectId !== this.rightCurrentProjectId) {
          this.rightLoading = false;
          return;
        }
        
        // 获取并去重评价记录
        let evaluations = response.rows || [];
        
        // 去除重复的评价记录（基于evaluateeId）
        const uniqueEvaluations = [];
        const evaluationMap = new Map();
        
        evaluations.forEach(evaluation => {
          const key = `${evaluation.evaluateeId}`;
          if (!evaluationMap.has(key)) {
            evaluationMap.set(key, evaluation);
            uniqueEvaluations.push(evaluation);
          } else {
            // 如果发现重复，保留ID较大的记录（通常是较新的）
            const existingEval = evaluationMap.get(key);
            if (evaluation.id > existingEval.id) {
              evaluationMap.set(key, evaluation);
              // 替换数组中的记录
              const index = uniqueEvaluations.indexOf(existingEval);
              if (index !== -1) {
                uniqueEvaluations[index] = evaluation;
              }
            }
          }
        });
        
        evaluations = uniqueEvaluations;
        
        // 找到当前项目
        const project = this.rightProjectOptions.find(p => p.id === projectId);
        const projectName = project ? project.projectName : "";
        
        // 创建员工编号到评价记录的映射，方便查找
        const evaluationMap2 = {};
        evaluations.forEach(evaluation => {
          evaluationMap2[evaluation.evaluateeId] = evaluation;
        });
        
        // 把评价信息合并到成员列表
        members.forEach(member => {
          // 获取用户的完整信息，包括userId
          const userInfo = this.userMap[member.userName];
          if (userInfo) {
            member.userId = userInfo.userId;
          }
          
          // 查找2022-08月份该成员的评价记录
          const evaluation = evaluationMap2[member.userId];
          
          if (evaluation) {
            member.evaluated = true;
            member.currentScore = evaluation.score;
            member.newScore = evaluation.score; // 添加新分数字段，用于跟踪更改
            member.evaluationId = evaluation.id;
            member.comments = evaluation.comments || "";
          } else {
            member.evaluated = false;
            member.currentScore = null;
            member.newScore = 0; // 默认分数为0分
            member.comments = "";
          }
          
          member.changed = false; // 标记是否有更改
          member.projectId = projectId;
          member.projectName = projectName;
          member.evaluationMonth = month; // 添加评价月份（2022-08）
          // 确保保留_uid字段
        });
        
        // 更新右侧卡片成员列表
        this.$set(this.rightMemberList, projectId, [...members]); // 使用展开运算符创建新数组，确保Vue检测到变化
        this.rightLoading = false;
      }).catch(error => {
        // 即使请求失败，也确保设置默认分数为0
        if (this.rightMemberList[projectId]) {
          this.rightMemberList[projectId].forEach(member => {
            if (!member.evaluated) {
              member.newScore = 0;
            }
          });
          
          this.$set(this.rightMemberList, projectId, [...this.rightMemberList[projectId]]);
        }
        this.rightLoading = false;
      });
    },

    // 右侧卡片分数变更处理
    handleRightScoreChange(row) {
      if (row.newScore < 0) {
        row.newScore = 0;
      } else if (row.newScore > 100) {
        row.newScore = 100;
      }

      if (row.newScore >= 95) {
        const currentMembers = this.rightMemberList[this.rightCurrentProjectId] || [];
        const totalMembers = currentMembers.length;
        const highScoreCount = currentMembers.filter(member => member.newScore >= 95).length;
        const maxHighScoreCount = Math.round(totalMembers * 0.3);

        if (highScoreCount > maxHighScoreCount) {
          this.$message({
            message: `95分以上人员不能超过项目成员总数的30%（当前项目共${totalMembers}人，最多允许${maxHighScoreCount}人获得95分以上）`,
            type: 'warning',
            duration: 4000
          });

          if (row.evaluated) {
            row.newScore = row.currentScore;
          } else {
            row.newScore = 0;
          }
          return;
        }
      }

      if (row.evaluated) {
        row.changed = row.currentScore !== row.newScore;
      } else {
        row.changed = true;
      }
    },

    // 批量保存季度评分
    batchSaveQuarterlyEvaluations() {
      if (!this.rightCanSubmit) {
        const warningText = this.getRightSubmitWarningText();
        this.$modal.msgError(`无法提交：${warningText}`);
        return;
      }

      if (!this.rightCurrentProjectId || !this.rightMemberList[this.rightCurrentProjectId]) return;

      const currentMembers = this.rightMemberList[this.rightCurrentProjectId];
      
      if (currentMembers.length === 0) {
        this.$message.info('没有可提交的评分');
        return;
      }
      
      const userNameMap = {};
      let hasDuplicate = false;
      let duplicateUserName = '';
      
      for (const member of currentMembers) {
        if (!member.userName) {
          this.$modal.msgError("存在未填写员工编号的记录，请完善信息后再提交");
          return;
        }
        
        if (userNameMap[member.userName]) {
          hasDuplicate = true;
          duplicateUserName = member.userName;
          break;
        }
        
        userNameMap[member.userName] = true;
      }
      
      if (hasDuplicate) {
        this.$modal.msgError(`存在重复的人员【${duplicateUserName}】，请检查后再提交`);
        return;
      }
      
      const zeroScoreMembers = currentMembers.filter(member => member.newScore === 0);
      if (zeroScoreMembers.length > 0) {
        const zeroScoreName = zeroScoreMembers[0].nickName || zeroScoreMembers[0].userName;
        this.$modal.msgError(`人员【${zeroScoreName}】的评分为0分，请评分后再提交`);
        return;
      }

      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      if (highScoreMembers.length > maxHighScoreCount) {
        this.$modal.msgError(`95分以上人员不能超过项目成员总数的30%（当前项目共${totalMembers}人，最多允许${maxHighScoreCount}人获得95分以上，当前有${highScoreMembers.length}人）`);
        return;
      }
      
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$modal.msgError("获取当前用户信息失败，请重新登录后再试");
        return;
      }
      
      const evaluationMonth = "2022-08";
      
      this.rightLoading = true;
      
      listEvaluation({
        projectId: this.rightCurrentProjectId,
        evaluationMonth: evaluationMonth,
        evaluationType: "quarterly_manager"
      }).then(response => {
        const existingEvaluations = response.rows || [];
        
        const evaluationMap = {};
        existingEvaluations.forEach(evaluation => {
          evaluationMap[evaluation.evaluateeId] = evaluation;
        });
        
        const savePromises = currentMembers.map(member => {
          if (!member.userName) {
            return Promise.reject(new Error(`有成员的员工编号为空，请完善信息后再提交`));
          }
          
          const existingEval = evaluationMap[member.userId];
          
          const data = {
            projectId: member.projectId,
            evaluatorId: evaluatorId,
            evaluateeId: member.userId,
            score: member.newScore,
            evaluationMonth: evaluationMonth,
            evaluationType: "quarterly_manager",
            comments: member.comments || ""
          };
          
          if (existingEval) {
            data.id = existingEval.id;
            return updateEvaluation(data);
          } else {
            return addEvaluation(data);
          }
        });
        
        Promise.all(savePromises)
          .then(() => {
            this.$modal.msgSuccess("提交成功（第二季度）");
            this.refreshRightData(this.rightCurrentProjectId);
          })
          .catch((error) => {
            this.$modal.msgError(error.message || "保存失败，请重试");
            this.rightLoading = false;
          });
      }).catch(error => {
        this.$modal.msgError("获取评价记录失败，请重试");
        this.rightLoading = false;
      });
    },

    // 刷新右侧卡片数据
    refreshRightData(projectId) {
      this.rightLoading = true;
      
      this.getRightUserLeadProjects().then(() => {
        if (projectId && this.rightProjectOptions.some(p => p.id === projectId)) {
          this.rightCurrentProjectId = projectId;
          this.loadRightProjectMembers(projectId);
        } else if (this.rightProjectOptions.length > 0) {
          this.selectRightProject(this.rightProjectOptions[0].id);
        } else {
          this.rightLoading = false;
        }
      }).catch(error => {
        this.rightLoading = false;
      });
    },

    // 获取右侧卡片配额信息文本
    getRightQuotaInfoText() {
      if (!this.rightCurrentProjectId || !this.rightMemberList[this.rightCurrentProjectId]) {
        return '配额信息加载中...';
      }

      const currentMembers = this.rightMemberList[this.rightCurrentProjectId];
      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      return `高分配额限制：95分以上人员不能超过项目成员总数的30%（当前项目共${totalMembers}人，最多允许${maxHighScoreCount}人获得95分以上，已使用${highScoreMembers.length}人）`;
    },

    // 获取右侧卡片配额提示类型
    getRightQuotaAlertType() {
      if (!this.rightCurrentProjectId || !this.rightMemberList[this.rightCurrentProjectId]) {
        return 'info';
      }

      const currentMembers = this.rightMemberList[this.rightCurrentProjectId];
      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      if (highScoreMembers.length >= maxHighScoreCount) {
        return 'warning';
      } else if (highScoreMembers.length >= maxHighScoreCount * 0.8) {
        return 'info';
      } else {
        return 'success';
      }
    },

    // 获取右侧卡片提交警告文本
    getRightSubmitWarningText() {
      if (!this.rightCurrentProjectId || !this.rightMemberList[this.rightCurrentProjectId]) {
        return '';
      }

      const currentMembers = this.rightMemberList[this.rightCurrentProjectId];

      const zeroScoreMembers = currentMembers.filter(member => member.newScore === 0);
      if (zeroScoreMembers.length > 0) {
        const memberNames = zeroScoreMembers.map(m => m.nickName || m.userName).join('、');
        return `存在未评分人员：${memberNames}`;
      }

      const totalMembers = currentMembers.length;
      const highScoreMembers = currentMembers.filter(member => member.newScore >= 95);
      const maxHighScoreCount = Math.round(totalMembers * 0.3);

      if (highScoreMembers.length > maxHighScoreCount) {
        return `95分以上人员超过配额限制（${highScoreMembers.length}/${maxHighScoreCount}）`;
      }

      return '';
    }
  }
};
</script>

<style scoped>
.search-card {
  margin-bottom: 15px;
}

.content-container {
  display: flex;
  gap: 15px;
}

.project-card {
  width: 300px;
  flex-shrink: 0;
}

.member-card {
  flex-grow: 1;
}

.project-list {
  max-height: 600px;
  overflow-y: auto;
}

.project-item {
  padding: 12px 15px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

.project-item:hover {
  background-color: #f5f7fa;
}

.project-item.active {
  background-color: #ecf5ff;
  color: #409eff;
  font-weight: bold;
}

.project-name {
  margin-left: 10px;
  flex-grow: 1;
}

.header-month {
  margin-left: 15px;
  color: #909399;
  font-size: 14px;
}

.placeholder-info {
  text-align: center;
  padding: 50px 0;
  color: #909399;
  font-size: 14px;
}

.table-footer-wrapper {
  position: relative;
  width: 100%;
}

.table-footer {
  margin-top: 20px;
  margin-bottom: 10px;
  text-align: center;
}

.evaluated-row {
  background-color: #f0f9eb;
}

.changed-row {
  background-color: #fdf6ec;
}

.el-rate {
  display: inline-block;
}

.score-changed {
  margin-left: 10px;
  color: #e6a23c;
  font-size: 12px;
}

.score-unit {
  margin-left: 5px;
  color: #606266;
}

.score-limit {
  margin-left: 5px;
  color: #909399;
  font-size: 12px;
}

.submit-warning {
  margin-left: 10px;
  color: #f56c6c;
  font-size: 14px;
  font-weight: 500;
}

</style>

<style scoped>
.search-card {
  margin-bottom: 15px;
}

.content-container {
  width: 100%;
}

/* 标签页样式 */
.evaluation-tabs {
  width: 100%;
}

.evaluation-tabs ::v-deep .el-tabs__header {
  margin-bottom: 20px;
}

.evaluation-tabs ::v-deep .el-tabs__nav-wrap {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.12), 0 0 6px rgba(0,0,0,0.04);
  border-radius: 4px;
  padding: 0 16px;
}

.evaluation-tabs ::v-deep .el-tabs__item {
  font-size: 14px;
  font-weight: 500;
  padding: 0 20px;
  color: #606266;
}

.evaluation-tabs ::v-deep .el-tabs__item.is-active {
  color: #409EFF;
  font-weight: 600;
}

.evaluation-tabs ::v-deep .el-tabs__item:hover {
  color: #409EFF;
}

/* 标签页内容样式 */
.tab-content {
  width: 100%;
  min-height: 600px;
}

/* 项目和成员容器 */
.project-member-container {
  display: flex;
  gap: 16px;
  width: 100%;
  height: 100%;
}

/* 项目卡片样式 */
.project-card {
  width: 300px;
  flex-shrink: 0;
  height: fit-content;
}

/* 成员卡片样式 */
.member-card {
  flex: 1;
  height: fit-content;
}

/* 卡片标题区域 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.member-header-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.monthly-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #67C23A;
}

.quarterly-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #409EFF;
}

.project-count {
  font-size: 12px;
  color: #909399;
}

/* 项目列表样式 */
.project-list {
  max-height: 400px;
  overflow-y: auto;
}

.project-item {
  padding: 12px 15px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  border-radius: 4px;
  margin-bottom: 2px;
}

.project-item:hover {
  background-color: #f5f7fa;
  transform: translateX(2px);
}

.project-item.active {
  background-color: #ecf5ff;
  color: #409eff;
  font-weight: bold;
  border-left: 3px solid #409eff;
}

.project-name {
  margin-left: 10px;
  flex-grow: 1;
  font-size: 14px;
}

/* 成员内容样式 */
.member-content {
  min-height: 400px;
}

.header-month {
  margin-top: 4px;
  color: #909399;
  font-size: 12px;
}

.placeholder-info {
  text-align: center;
  padding: 80px 0;
  color: #909399;
  font-size: 14px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

/* 表格底部样式 */
.table-footer-wrapper {
  position: relative;
  width: 100%;
  margin-top: 20px;
}

.table-footer {
  text-align: center;
  padding: 20px 0;
}

.table-footer .el-button {
  padding: 12px 24px;
  font-size: 14px;
  border-radius: 6px;
}

/* 评价行样式 */
.evaluated-row {
  background-color: #f0f9eb;
}

.changed-row {
  background-color: #fdf6ec;
}

/* 评分相关样式 */
.score-unit {
  margin-left: 5px;
  color: #606266;
  font-size: 12px;
}

.score-limit {
  margin-left: 5px;
  color: #909399;
  font-size: 12px;
}

.submit-warning {
  margin-left: 12px;
  color: #f56c6c;
  font-size: 13px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .project-member-container {
    flex-direction: column;
  }
  
  .project-card {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .member-card {
    width: 100%;
  }
}

/* 滚动条样式 */
.project-list::-webkit-scrollbar {
  width: 6px;
}

.project-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.project-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.project-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.evaluation-tabs ::v-deep .el-tab-pane {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片阴影效果 */
.project-card, .member-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 8px;
  transition: box-shadow 0.3s;
}

.project-card:hover, .member-card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}
</style>