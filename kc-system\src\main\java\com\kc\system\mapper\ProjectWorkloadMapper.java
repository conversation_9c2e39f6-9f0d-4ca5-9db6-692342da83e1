package com.kc.system.mapper;

import java.util.List;
import java.util.Map;
import java.util.Set;
import com.kc.system.domain.ProjectWorkload;
import org.apache.ibatis.annotations.Param;

/**
 * 项目工时记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-15
 */
public interface ProjectWorkloadMapper 
{
    /**
     * 查询项目工时记录
     * 
     * @param id 项目工时记录主键
     * @return 项目工时记录
     */
    public ProjectWorkload selectProjectWorkloadById(Long id);

    /**
     * 查询项目工时记录列表
     *
     * @param projectWorkload 项目工时记录
     * @return 项目工时记录集合
     */
    public List<ProjectWorkload> selectProjectWorkloadList(ProjectWorkload projectWorkload);

    /**
     * 查询用户实际工时记录
     *
     * @param projectWorkload 项目工时记录
     * @return 项目工时记录集合
     */
    public List<ProjectWorkload> selectUserActualWorkload(ProjectWorkload projectWorkload);

    /**
     * 根据唯一约束字段查询已存在的工时记录
     *
     * @param userName 用户名
     * @param workMonth 工作月份
     * @param sourceEffortProjectId 源精力分配项目ID
     * @return 已存在的工时记录，如果不存在则返回null
     */
    public ProjectWorkload selectExistingWorkload(@Param("userName") String userName,
                                                  @Param("workMonth") String workMonth,
                                                  @Param("sourceEffortProjectId") Long sourceEffortProjectId);

    /**
     * 查询实际工时记录列表（只返回有实际记录的数据）
     *
     * @param projectWorkload 项目工时记录
     * @return 项目工时记录集合
     */
    public List<ProjectWorkload> selectActualWorkloadList(ProjectWorkload projectWorkload);

    /**
     * 新增项目工时记录
     * 
     * @param projectWorkload 项目工时记录
     * @return 结果
     */
    public int insertProjectWorkload(ProjectWorkload projectWorkload);

    /**
     * 修改项目工时记录
     * 
     * @param projectWorkload 项目工时记录
     * @return 结果
     */
    public int updateProjectWorkload(ProjectWorkload projectWorkload);

    /**
     * 删除项目工时记录
     * 
     * @param id 项目工时记录主键
     * @return 结果
     */
    public int deleteProjectWorkloadById(Long id);

    /**
     * 批量删除项目工时记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectWorkloadByIds(Long[] ids);

    /**
     * 查询项目统计数据
     */
    public Map<String, Object> selectProjectStats(@Param("projectId") Long projectId, @Param("workMonth") String workMonth);

    /**
     * 查询项目的所有工作月份
     */
    public Set<String> selectProjectMonths(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和用户名删除工时记录
     * 
     * @param projectId 项目ID
     * @param userName 用户名
     * @return 结果
     */
    public int deleteProjectWorkloadByProjectIdAndUserName(@Param("projectId") Long projectId, @Param("userName") String userName);

    /**
     * 根据项目ID删除工时记录
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    public int deleteProjectWorkloadByProjectId(Long projectId);

    /**
     * 查询所有人工时记录
     */
    List<Map<String, Object>> selectAllWorkloadList(@Param("workMonth") String workMonth);

    /**
     * 查询未填报工时的人数及项目名称
     */
    List<Map<String, Object>> selectUnfilledWorkloadCount(@Param("workMonth") String workMonth);
}
