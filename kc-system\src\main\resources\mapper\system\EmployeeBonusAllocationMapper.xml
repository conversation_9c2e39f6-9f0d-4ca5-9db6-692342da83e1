<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.EmployeeBonusAllocationMapper">
    
    <resultMap type="EmployeeBonusAllocation" id="EmployeeBonusAllocationResult">
        <result property="id"    column="id"    />
        <result property="deptBonusId"    column="dept_bonus_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="allocationMonth"    column="allocation_month"    />
        <result property="bonusAmount"    column="bonus_amount"    />
        <result property="allocationReason"    column="allocation_reason"    />
        <result property="allocatorId"    column="allocator_id"    />
        <result property="allocatorName"    column="allocator_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectEmployeeBonusAllocationVo">
        select id, dept_bonus_id, user_id, user_name, nick_name, dept_id, dept_name, allocation_month, bonus_amount, allocation_reason, allocator_id, allocator_name, create_by, create_time, update_by, update_time from employee_bonus_allocation
    </sql>

    <select id="selectEmployeeBonusAllocationList" parameterType="EmployeeBonusAllocation" resultMap="EmployeeBonusAllocationResult">
        <include refid="selectEmployeeBonusAllocationVo"/>
        <where>  
            <if test="deptBonusId != null "> and dept_bonus_id = #{deptBonusId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="allocationMonth != null  and allocationMonth != ''"> and allocation_month = #{allocationMonth}</if>
            <if test="allocatorId != null "> and allocator_id = #{allocatorId}</if>
        </where>
        order by allocation_month desc, bonus_amount desc
    </select>
    
    <select id="selectEmployeeBonusAllocationById" parameterType="Long" resultMap="EmployeeBonusAllocationResult">
        <include refid="selectEmployeeBonusAllocationVo"/>
        where id = #{id}
    </select>

    <select id="selectByUserIdAndMonth" resultMap="EmployeeBonusAllocationResult">
        <include refid="selectEmployeeBonusAllocationVo"/>
        where user_id = #{userId} and allocation_month = #{allocationMonth}
    </select>

    <select id="selectByDeptBonusId" resultMap="EmployeeBonusAllocationResult">
        <include refid="selectEmployeeBonusAllocationVo"/>
        where dept_bonus_id = #{deptBonusId}
        order by bonus_amount desc
    </select>

    <select id="selectByDeptIdAndMonth" resultMap="EmployeeBonusAllocationResult">
        <include refid="selectEmployeeBonusAllocationVo"/>
        where dept_id = #{deptId} and allocation_month = #{allocationMonth}
        order by bonus_amount desc
    </select>

    <select id="selectByMonth" resultMap="EmployeeBonusAllocationResult">
        <include refid="selectEmployeeBonusAllocationVo"/>
        where allocation_month = #{allocationMonth}
        order by dept_id, bonus_amount desc
    </select>

    <select id="selectByAllocatorAndMonth" resultMap="EmployeeBonusAllocationResult">
        <include refid="selectEmployeeBonusAllocationVo"/>
        where allocator_id = #{allocatorId} and allocation_month = #{allocationMonth}
        order by bonus_amount desc
    </select>

    <insert id="insertEmployeeBonusAllocation" parameterType="EmployeeBonusAllocation" useGeneratedKeys="true" keyProperty="id">
        insert into employee_bonus_allocation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptBonusId != null">dept_bonus_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null and deptName != ''">dept_name,</if>
            <if test="allocationMonth != null and allocationMonth != ''">allocation_month,</if>
            <if test="bonusAmount != null">bonus_amount,</if>
            <if test="allocationReason != null">allocation_reason,</if>
            <if test="allocatorId != null">allocator_id,</if>
            <if test="allocatorName != null">allocator_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptBonusId != null">#{deptBonusId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null and deptName != ''">#{deptName},</if>
            <if test="allocationMonth != null and allocationMonth != ''">#{allocationMonth},</if>
            <if test="bonusAmount != null">#{bonusAmount},</if>
            <if test="allocationReason != null">#{allocationReason},</if>
            <if test="allocatorId != null">#{allocatorId},</if>
            <if test="allocatorName != null">#{allocatorName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <insert id="batchInsertEmployeeBonusAllocation" parameterType="java.util.List">
        insert into employee_bonus_allocation(dept_bonus_id, user_id, user_name, nick_name, dept_id, dept_name, allocation_month, bonus_amount, allocation_reason, allocator_id, allocator_name, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptBonusId}, #{item.userId}, #{item.userName}, #{item.nickName}, #{item.deptId}, #{item.deptName}, #{item.allocationMonth}, #{item.bonusAmount}, #{item.allocationReason}, #{item.allocatorId}, #{item.allocatorName}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateEmployeeBonusAllocation" parameterType="EmployeeBonusAllocation">
        update employee_bonus_allocation
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptBonusId != null">dept_bonus_id = #{deptBonusId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="allocationMonth != null and allocationMonth != ''">allocation_month = #{allocationMonth},</if>
            <if test="bonusAmount != null">bonus_amount = #{bonusAmount},</if>
            <if test="allocationReason != null">allocation_reason = #{allocationReason},</if>
            <if test="allocatorId != null">allocator_id = #{allocatorId},</if>
            <if test="allocatorName != null">allocator_name = #{allocatorName},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeBonusAllocationById" parameterType="Long">
        delete from employee_bonus_allocation where id = #{id}
    </delete>

    <delete id="deleteEmployeeBonusAllocationByIds" parameterType="String">
        delete from employee_bonus_allocation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByDeptBonusId">
        delete from employee_bonus_allocation where dept_bonus_id = #{deptBonusId}
    </delete>

    <delete id="deleteByMonth">
        delete from employee_bonus_allocation where allocation_month = #{allocationMonth}
    </delete>

</mapper>
