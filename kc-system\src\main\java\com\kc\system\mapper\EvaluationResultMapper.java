package com.kc.system.mapper;

import java.util.List;
import java.util.Map;
import com.kc.system.domain.EvaluationResult;
import org.apache.ibatis.annotations.Param;

/**
 * 评价结果Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-01
 */
public interface EvaluationResultMapper 
{
    /**
     * 查询评价结果
     * 
     * @param id 评价结果主键
     * @return 评价结果
     */
    public EvaluationResult selectEvaluationResultById(Long id);

    /**
     * 查询评价结果列表
     * 
     * @param evaluationResult 评价结果
     * @return 评价结果集合
     */
    public List<EvaluationResult> selectEvaluationResultList(EvaluationResult evaluationResult);

    /**
     * 查询评价结果列表（带部门数据权限）
     * 
     * @param evaluationResult 评价结果
     * @param deptIds 部门ID列表
     * @return 评价结果集合
     */
    public List<EvaluationResult> selectEvaluationResultListWithDeptScope(EvaluationResult evaluationResult, List<Long> deptIds);

    /**
     * 新增评价结果
     * 
     * @param evaluationResult 评价结果
     * @return 结果
     */
    public int insertEvaluationResult(EvaluationResult evaluationResult);

    /**
     * 修改评价结果
     * 
     * @param evaluationResult 评价结果
     * @return 结果
     */
    public int updateEvaluationResult(EvaluationResult evaluationResult);

    /**
     * 删除评价结果
     * 
     * @param id 评价结果主键
     * @return 结果
     */
    public int deleteEvaluationResultById(Long id);

    /**
     * 批量删除评价结果
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEvaluationResultByIds(Long[] ids);
    
    /**
     * 调用存储过程计算评价结果
     * 
     * @param params 包含evaluationMonth(String), userId(Long), deptId(String)的参数Map
     */
    public void callCalculateEvaluationResults(Map<String, Object> params);
    
    /**
     * 根据评价月份删除评价结果
     *
     * @param params 包含evaluationMonth(String)的参数Map
     * @return 结果
     */
    public int deleteEvaluationResultByMonth(Map<String, Object> params);

    /**
     * 根据用户ID和评价月份删除评价结果
     *
     * @param params 包含userId和evaluationMonth的参数Map
     * @return 结果
     */
    public int deleteEvaluationResultByUserAndMonth(Map<String, Object> params);

    /**
     * 根据用户ID列表获取用户角色信息
     *
     * @param userIds 用户ID列表，逗号分隔
     * @return 用户角色信息列表
     */
    public List<Map<String, Object>> getUserRolesByUserIds(@Param("userIds") String userIds);

    /**
     * 根据用户ID列表获取机构负责人评分
     *
     * @param userIds 用户ID列表，逗号分隔
     * @param evaluationMonth 评价月份
     * @return 机构负责人评分列表
     */
    public List<Map<String, Object>> getManagerScoresByUserIds(@Param("userIds") String userIds, @Param("evaluationMonth") String evaluationMonth);

    /**
     * 根据用户ID列表获取项目负责人评分
     *
     * @param userIds 用户ID列表，逗号分隔
     * @param evaluationMonth 评价月份
     * @return 项目负责人评分列表
     */
    public List<Map<String, Object>> getProjectLeaderScoresByUserIds(@Param("userIds") String userIds, @Param("evaluationMonth") String evaluationMonth);
}
