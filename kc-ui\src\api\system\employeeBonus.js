import request from '@/utils/request'

// 查询员工奖金分配列表
export function listEmployeeBonus(query) {
  return request({
    url: '/system/employeeBonus/list',
    method: 'get',
    params: query
  })
}

// 根据部门ID和月份查询员工奖金分配列表
export function listEmployeeBonusByDeptAndMonth(deptId, month) {
  return request({
    url: '/system/employeeBonus/listByDeptAndMonth/' + deptId + '/' + month,
    method: 'get'
  })
}

// 根据月份查询员工奖金分配列表
export function listEmployeeBonusByMonth(month) {
  return request({
    url: '/system/employeeBonus/listByMonth/' + month,
    method: 'get'
  })
}

// 获取部门负责人可分配的员工列表
export function getAvailableEmployees(deptId, month) {
  return request({
    url: '/system/employeeBonus/getAvailableEmployees/' + deptId + '/' + month,
    method: 'get'
  })
}

// 获取当前用户的奖金分配信息
export function getMyBonus(month) {
  return request({
    url: '/system/employeeBonus/getMyBonus/' + month,
    method: 'get'
  })
}

// 查询员工奖金分配详细
export function getEmployeeBonus(id) {
  return request({
    url: '/system/employeeBonus/' + id,
    method: 'get'
  })
}

// 新增员工奖金分配
export function addEmployeeBonus(data) {
  return request({
    url: '/system/employeeBonus',
    method: 'post',
    data: data
  })
}

// 批量分配员工奖金
export function batchAllocateEmployeeBonus(data) {
  return request({
    url: '/system/employeeBonus/batchAllocate',
    method: 'post',
    data: data
  })
}

// 修改员工奖金分配
export function updateEmployeeBonus(data) {
  return request({
    url: '/system/employeeBonus',
    method: 'put',
    data: data
  })
}

// 删除员工奖金分配
export function delEmployeeBonus(id) {
  return request({
    url: '/system/employeeBonus/' + id,
    method: 'delete'
  })
}

// 根据部门奖金分配ID删除员工奖金分配
export function deleteEmployeeBonusByDeptBonusId(deptBonusId) {
  return request({
    url: '/system/employeeBonus/deleteByDeptBonusId/' + deptBonusId,
    method: 'delete'
  })
}

// 根据月份删除员工奖金分配
export function deleteEmployeeBonusByMonth(month) {
  return request({
    url: '/system/employeeBonus/deleteByMonth/' + month,
    method: 'delete'
  })
}
