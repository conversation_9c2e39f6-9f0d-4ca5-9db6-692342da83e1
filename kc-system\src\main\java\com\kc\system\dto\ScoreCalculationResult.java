package com.kc.system.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 评分计算结果
 *
 * <AUTHOR>
 */
public class ScoreCalculationResult {
    
    /** 用户ID */
    private Long userId;
    
    /** 用户姓名 */
    private String userName;
    
    /** 最终评分 */
    private BigDecimal finalScore;
    
    /** 使用的计算策略 */
    private String strategyType;
    
    /** 计算详情说明 */
    private String calculationDetails;
    
    /** 评价月份 */
    private String evaluationMonth;
    
    /** 计算时间 */
    private LocalDateTime calculationTime;
    
    /** 是否计算成功 */
    private Boolean success;
    
    /** 错误信息（如果计算失败） */
    private String errorMessage;
    
    /** 详细评分信息 */
    private ScoreDetails scoreDetails;
    
    // 构造函数
    public ScoreCalculationResult() {}

    public ScoreCalculationResult(Long userId, String userName, BigDecimal finalScore,
                                String strategyType, String calculationDetails, String evaluationMonth,
                                LocalDateTime calculationTime, Boolean success, String errorMessage,
                                ScoreDetails scoreDetails) {
        this.userId = userId;
        this.userName = userName;
        this.finalScore = finalScore;
        this.strategyType = strategyType;
        this.calculationDetails = calculationDetails;
        this.evaluationMonth = evaluationMonth;
        this.calculationTime = calculationTime;
        this.success = success;
        this.errorMessage = errorMessage;
        this.scoreDetails = scoreDetails;
    }

    // Getter和Setter方法
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public BigDecimal getFinalScore() {
        return finalScore;
    }

    public void setFinalScore(BigDecimal finalScore) {
        this.finalScore = finalScore;
    }

    public String getStrategyType() {
        return strategyType;
    }

    public void setStrategyType(String strategyType) {
        this.strategyType = strategyType;
    }

    public String getCalculationDetails() {
        return calculationDetails;
    }

    public void setCalculationDetails(String calculationDetails) {
        this.calculationDetails = calculationDetails;
    }

    public String getEvaluationMonth() {
        return evaluationMonth;
    }

    public void setEvaluationMonth(String evaluationMonth) {
        this.evaluationMonth = evaluationMonth;
    }

    public LocalDateTime getCalculationTime() {
        return calculationTime;
    }

    public void setCalculationTime(LocalDateTime calculationTime) {
        this.calculationTime = calculationTime;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public ScoreDetails getScoreDetails() {
        return scoreDetails;
    }

    public void setScoreDetails(ScoreDetails scoreDetails) {
        this.scoreDetails = scoreDetails;
    }

    /**
     * 详细评分信息内部类
     */
    public static class ScoreDetails {
        /** 机构负责人评分 */
        private BigDecimal managerScore;

        /** 项目负责人评分 */
        private BigDecimal projectScore;

        /** 子部门负责人评分 */
        private BigDecimal subDeptManagerScore;

        /** 父部门负责人评分 */
        private BigDecimal parentDeptManagerScore;

        /** 各项评分权重 */
        private Map<String, BigDecimal> scoreWeights;

        /** 计算过程详情 */
        private String calculationProcess;

        public ScoreDetails() {}

        public ScoreDetails(BigDecimal managerScore, BigDecimal projectScore,
                          BigDecimal subDeptManagerScore, BigDecimal parentDeptManagerScore,
                          Map<String, BigDecimal> scoreWeights, String calculationProcess) {
            this.managerScore = managerScore;
            this.projectScore = projectScore;
            this.subDeptManagerScore = subDeptManagerScore;
            this.parentDeptManagerScore = parentDeptManagerScore;
            this.scoreWeights = scoreWeights;
            this.calculationProcess = calculationProcess;
        }

        public BigDecimal getManagerScore() {
            return managerScore;
        }

        public void setManagerScore(BigDecimal managerScore) {
            this.managerScore = managerScore;
        }

        public BigDecimal getProjectScore() {
            return projectScore;
        }

        public void setProjectScore(BigDecimal projectScore) {
            this.projectScore = projectScore;
        }

        public BigDecimal getSubDeptManagerScore() {
            return subDeptManagerScore;
        }

        public void setSubDeptManagerScore(BigDecimal subDeptManagerScore) {
            this.subDeptManagerScore = subDeptManagerScore;
        }

        public BigDecimal getParentDeptManagerScore() {
            return parentDeptManagerScore;
        }

        public void setParentDeptManagerScore(BigDecimal parentDeptManagerScore) {
            this.parentDeptManagerScore = parentDeptManagerScore;
        }

        public Map<String, BigDecimal> getScoreWeights() {
            return scoreWeights;
        }

        public void setScoreWeights(Map<String, BigDecimal> scoreWeights) {
            this.scoreWeights = scoreWeights;
        }

        public String getCalculationProcess() {
            return calculationProcess;
        }

        public void setCalculationProcess(String calculationProcess) {
            this.calculationProcess = calculationProcess;
        }
    }
}
