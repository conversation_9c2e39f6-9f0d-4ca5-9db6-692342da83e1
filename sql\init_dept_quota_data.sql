-- 初始化部门高分配额数据SQL

-- 1. 为所有现有部门初始化当前年度的高分配额
-- 注意：这个脚本会为所有状态正常的部门创建当前年度的配额记录

SET @current_year = YEAR(NOW());

-- 创建临时存储过程来计算部门配额
DELIMITER $$

CREATE PROCEDURE InitDeptQuota()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE dept_id_var BIGINT;
    DECLARE dept_name_var VARCHAR(50);
    DECLARE leader_var VARCHAR(30);
    DECLARE total_employees INT DEFAULT 0;
    DECLARE high_score_quota INT DEFAULT 0;
    
    -- 声明游标
    DECLARE dept_cursor CURSOR FOR 
        SELECT dept_id, dept_name, leader 
        FROM sys_dept 
        WHERE status = '0' 
        AND del_flag = '0'
        AND dept_id != 100; -- 排除根部门
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 打开游标
    OPEN dept_cursor;
    
    -- 循环处理每个部门
    dept_loop: LOOP
        FETCH dept_cursor INTO dept_id_var, dept_name_var, leader_var;
        
        IF done THEN
            LEAVE dept_loop;
        END IF;
        
        -- 计算部门总人数（排除部门负责人）
        SELECT COUNT(*) INTO total_employees
        FROM sys_user u
        WHERE u.dept_id = dept_id_var 
        AND u.status = '0' 
        AND u.del_flag = '0'
        AND (leader_var IS NULL OR u.user_name != leader_var);
        
        -- 计算高分配额（30%，向下取整）
        SET high_score_quota = FLOOR(total_employees * 0.30);
        
        -- 检查是否已存在当前年度的配额记录
        IF NOT EXISTS (
            SELECT 1 FROM dept_high_score_quota 
            WHERE dept_id = dept_id_var 
            AND evaluation_year = @current_year
        ) THEN
            -- 插入配额记录
            INSERT INTO dept_high_score_quota (
                dept_id, 
                evaluation_year, 
                total_employees, 
                high_score_quota, 
                used_quota, 
                remaining_quota, 
                create_time, 
                create_by
            ) VALUES (
                dept_id_var,
                @current_year,
                total_employees,
                high_score_quota,
                0,
                high_score_quota,
                NOW(),
                'system'
            );
            
            -- 输出日志信息
            SELECT CONCAT('初始化部门[', dept_name_var, ']配额: 总人数=', total_employees, ', 配额=', high_score_quota) AS log_info;
        ELSE
            -- 更新现有记录的人数和配额
            UPDATE dept_high_score_quota 
            SET 
                total_employees = total_employees,
                high_score_quota = high_score_quota,
                remaining_quota = high_score_quota - used_quota,
                update_time = NOW(),
                update_by = 'system'
            WHERE dept_id = dept_id_var 
            AND evaluation_year = @current_year;
            
            SELECT CONCAT('更新部门[', dept_name_var, ']配额: 总人数=', total_employees, ', 配额=', high_score_quota) AS log_info;
        END IF;
        
    END LOOP;
    
    -- 关闭游标
    CLOSE dept_cursor;
    
END$$

DELIMITER ;

-- 2. 执行初始化
CALL InitDeptQuota();

-- 3. 删除临时存储过程
DROP PROCEDURE InitDeptQuota;

-- 4. 查看初始化结果
SELECT 
    d.dept_name,
    q.evaluation_year,
    q.total_employees,
    q.high_score_quota,
    q.used_quota,
    q.remaining_quota,
    q.create_time
FROM dept_high_score_quota q
JOIN sys_dept d ON q.dept_id = d.dept_id
WHERE q.evaluation_year = @current_year
ORDER BY d.dept_name;

-- 5. 统计信息
SELECT 
    COUNT(*) as total_depts,
    SUM(total_employees) as total_employees,
    SUM(high_score_quota) as total_quota,
    SUM(used_quota) as total_used,
    SUM(remaining_quota) as total_remaining,
    AVG(high_score_quota) as avg_quota_per_dept
FROM dept_high_score_quota 
WHERE evaluation_year = @current_year;

-- 6. 创建年度重置存储过程（可选）
DELIMITER $$

CREATE PROCEDURE ResetYearlyQuota(IN target_year VARCHAR(4))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE dept_id_var BIGINT;
    DECLARE total_employees INT DEFAULT 0;
    DECLARE high_score_quota INT DEFAULT 0;
    
    DECLARE dept_cursor CURSOR FOR 
        SELECT dept_id 
        FROM sys_dept 
        WHERE status = '0' 
        AND del_flag = '0'
        AND dept_id != 100;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 清空指定年度的配额记录
    DELETE FROM dept_high_score_quota WHERE evaluation_year = target_year;
    DELETE FROM high_score_record WHERE evaluation_year = target_year;
    
    -- 重新初始化
    OPEN dept_cursor;
    
    dept_loop: LOOP
        FETCH dept_cursor INTO dept_id_var;
        
        IF done THEN
            LEAVE dept_loop;
        END IF;
        
        -- 计算部门总人数
        SELECT COUNT(*) INTO total_employees
        FROM sys_user u
        JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE u.dept_id = dept_id_var 
        AND u.status = '0' 
        AND u.del_flag = '0'
        AND (d.leader IS NULL OR u.user_name != d.leader);
        
        -- 计算高分配额
        SET high_score_quota = FLOOR(total_employees * 0.30);
        
        -- 插入新的配额记录
        INSERT INTO dept_high_score_quota (
            dept_id, 
            evaluation_year, 
            total_employees, 
            high_score_quota, 
            used_quota, 
            remaining_quota, 
            create_time, 
            create_by
        ) VALUES (
            dept_id_var,
            target_year,
            total_employees,
            high_score_quota,
            0,
            high_score_quota,
            NOW(),
            'system'
        );
        
    END LOOP;
    
    CLOSE dept_cursor;
    
    SELECT CONCAT('年度[', target_year, ']配额重置完成') AS result;
    
END$$

DELIMITER ;

-- 使用示例：
-- CALL ResetYearlyQuota('2025'); -- 重置2025年的配额

-- 7. 创建配额检查视图（可选）
CREATE OR REPLACE VIEW v_dept_quota_status AS
SELECT 
    d.dept_id,
    d.dept_name,
    d.leader,
    q.evaluation_year,
    q.total_employees,
    q.high_score_quota,
    q.used_quota,
    q.remaining_quota,
    CASE 
        WHEN q.remaining_quota <= 0 THEN '配额已满'
        WHEN q.remaining_quota <= 2 THEN '配额紧张'
        ELSE '配额充足'
    END as quota_status,
    ROUND(q.used_quota / NULLIF(q.high_score_quota, 0) * 100, 2) as usage_percentage
FROM sys_dept d
LEFT JOIN dept_high_score_quota q ON d.dept_id = q.dept_id AND q.evaluation_year = YEAR(NOW())
WHERE d.status = '0' AND d.del_flag = '0' AND d.dept_id != 100
ORDER BY d.dept_name;

-- 查看配额状态
SELECT * FROM v_dept_quota_status;

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 请根据实际的部门结构调整排除条件
-- 3. 30%的比例可以根据业务需求调整
-- 4. 建议在业务低峰期执行
-- 5. 如果有大量部门，执行可能需要一些时间
