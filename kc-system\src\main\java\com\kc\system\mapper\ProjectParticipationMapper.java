package com.kc.system.mapper;

import java.util.List;
import java.util.Map;

import com.kc.system.domain.ProjectParticipation;
import org.apache.ibatis.annotations.Param;

/**
 * 项目参与度分配Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface ProjectParticipationMapper 
{
    /**
     * 查询项目参与度分配
     * 
     * @param id 项目参与度分配主键
     * @return 项目参与度分配
     */
    public ProjectParticipation selectProjectParticipationById(Long id);

    /**
     * 查询项目参与度分配列表
     *
     * @param projectParticipation 项目参与度分配
     * @return 项目参与度分配集合
     */
    public List<ProjectParticipation> selectProjectParticipationList(ProjectParticipation projectParticipation);

    /**
     * 根据唯一约束字段查询已存在的精力分配记录
     *
     * @param userName 用户名
     * @param projectId 项目ID
     * @param month 月份
     * @return 已存在的精力分配记录，如果不存在则返回null
     */
    public ProjectParticipation selectExistingParticipation(@Param("userName") String userName,
                                                           @Param("projectId") Long projectId,
                                                           @Param("month") String month);

    /**
     * 新增项目参与度分配
     * 
     * @param projectParticipation 项目参与度分配
     * @return 结果
     */
    public int insertProjectParticipation(ProjectParticipation projectParticipation);

    /**
     * 修改项目参与度分配
     * 
     * @param projectParticipation 项目参与度分配
     * @return 结果
     */
    public int updateProjectParticipation(ProjectParticipation projectParticipation);

    /**
     * 删除项目参与度分配
     * 
     * @param id 项目参与度分配主键
     * @return 结果
     */
    public int deleteProjectParticipationById(Long id);

    /**
     * 批量删除项目参与度分配
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectParticipationByIds(Long[] ids);

    /**
     * 根据项目ID删除项目参与度分配
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    public int deleteProjectParticipationByProjectId(Long projectId);

    /**
     * 根据项目ID统计项目参与度分配记录数
     *
     * @param projectId 项目ID
     * @return 记录数
     */
    public int countProjectParticipationByProjectId(Long projectId);

    /**
     * 查询项目参与度分配列表（关联用户和部门信息）
     *
     * @param projectParticipation 项目参与度分配
     * @return 项目参与度分配集合（包含用户和部门信息）
     */
    public List<Map<String, Object>> selectProjectParticipationWithUserInfo(ProjectParticipation projectParticipation);
}
