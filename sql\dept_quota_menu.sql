-- 部门高分配额菜单和权限配置SQL

-- 1. 插入部门高分配额管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('部门高分配额', 2000, 6, 'deptQuota', 'system/deptQuota/index', '', 1, 0, 'C', '0', '0', 'system:deptQuota:list', 'chart', 'admin', sysdate(), '', null, '部门高分配额管理菜单');

-- 获取刚插入的菜单ID（假设为2061，实际需要根据数据库自增值调整）
SET @menu_id = LAST_INSERT_ID();

-- 2. 插入子菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('部门高分配额查询', @menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:query', '#', 'admin', sysdate(), '', null, ''),
('部门高分配额新增', @menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:add', '#', 'admin', sysdate(), '', null, ''),
('部门高分配额修改', @menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:edit', '#', 'admin', sysdate(), '', null, ''),
('部门高分配额删除', @menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:remove', '#', 'admin', sysdate(), '', null, ''),
('部门高分配额导出', @menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:export', '#', 'admin', sysdate(), '', null, '');

-- 3. 为管理员角色分配权限（假设管理员角色ID为1）
-- 获取所有相关菜单ID
SET @quota_query_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:query' AND parent_id = @menu_id);
SET @quota_add_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:add' AND parent_id = @menu_id);
SET @quota_edit_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:edit' AND parent_id = @menu_id);
SET @quota_remove_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:remove' AND parent_id = @menu_id);
SET @quota_export_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:export' AND parent_id = @menu_id);

-- 为管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(1, @menu_id),
(1, @quota_query_id),
(1, @quota_add_id),
(1, @quota_edit_id),
(1, @quota_remove_id),
(1, @quota_export_id);

-- 4. 为部门负责人角色分配基本权限（假设部门负责人角色ID为2，根据实际情况调整）
-- 部门负责人通常只需要查询权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(2, @menu_id),
(2, @quota_query_id);

-- 5. 创建高分记录管理菜单（可选，用于查看高分记录）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('高分记录管理', 2000, 7, 'highScoreRecord', 'system/highScoreRecord/index', '', 1, 0, 'C', '0', '0', 'system:highScoreRecord:list', 'documentation', 'admin', sysdate(), '', null, '高分记录管理菜单');

-- 获取高分记录菜单ID
SET @record_menu_id = LAST_INSERT_ID();

-- 插入高分记录子菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('高分记录查询', @record_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:highScoreRecord:query', '#', 'admin', sysdate(), '', null, ''),
('高分记录新增', @record_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:highScoreRecord:add', '#', 'admin', sysdate(), '', null, ''),
('高分记录修改', @record_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:highScoreRecord:edit', '#', 'admin', sysdate(), '', null, ''),
('高分记录删除', @record_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:highScoreRecord:remove', '#', 'admin', sysdate(), '', null, ''),
('高分记录导出', @record_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:highScoreRecord:export', '#', 'admin', sysdate(), '', null, '');

-- 获取高分记录相关菜单ID
SET @record_query_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:highScoreRecord:query' AND parent_id = @record_menu_id);
SET @record_add_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:highScoreRecord:add' AND parent_id = @record_menu_id);
SET @record_edit_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:highScoreRecord:edit' AND parent_id = @record_menu_id);
SET @record_remove_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:highScoreRecord:remove' AND parent_id = @record_menu_id);
SET @record_export_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:highScoreRecord:export' AND parent_id = @record_menu_id);

-- 为管理员角色分配高分记录权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(1, @record_menu_id),
(1, @record_query_id),
(1, @record_add_id),
(1, @record_edit_id),
(1, @record_remove_id),
(1, @record_export_id);

-- 为部门负责人角色分配高分记录查询权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES 
(2, @record_menu_id),
(2, @record_query_id);

-- 6. 更新部门评价菜单权限，确保部门负责人可以使用高分配额功能
-- 假设部门评价菜单已存在，需要确保相关角色有权限
-- 这里可以根据实际的部门评价菜单ID进行调整

-- 查看当前菜单结构（用于验证）
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    m.order_num,
    m.path,
    m.perms,
    m.menu_type,
    m.visible,
    m.status
FROM sys_menu m 
WHERE m.menu_name LIKE '%配额%' OR m.menu_name LIKE '%高分%'
ORDER BY m.parent_id, m.order_num;

-- 查看角色权限分配情况（用于验证）
SELECT 
    r.role_name,
    m.menu_name,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_name LIKE '%配额%' OR m.menu_name LIKE '%高分%'
ORDER BY r.role_name, m.menu_name;

-- 注意事项：
-- 1. 请根据实际的角色ID调整上述SQL中的role_id值
-- 2. 请根据实际的父菜单ID调整parent_id值（这里假设系统管理的parent_id为2000）
-- 3. 如果需要为其他角色分配权限，请参考上述模式添加相应的INSERT语句
-- 4. 执行前请备份数据库，确保可以回滚
-- 5. 建议在测试环境先验证SQL的正确性
