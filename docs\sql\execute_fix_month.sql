-- 执行月份修复的具体SQL
-- 请在确认数据备份后执行

-- 1. 预览要修复的数据
SELECT 
    '即将修复的数据' as step,
    id, evaluator_id, evaluatee_id, project_id, evaluation_type, score, created_at
FROM project_evaluation 
WHERE evaluation_month = '2025-06' 
AND created_at >= '2025-06-11 12:00:00'
ORDER BY created_at;

-- 2. 执行修复
UPDATE project_evaluation 
SET evaluation_month = '2025-07'
WHERE evaluation_month = '2025-06' 
AND created_at >= '2025-06-11 12:00:00';

-- 3. 验证修复结果
SELECT 
    '修复后统计' as step,
    evaluation_month, 
    evaluation_type, 
    COUNT(*) as count 
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
GROUP BY evaluation_month, evaluation_type;
