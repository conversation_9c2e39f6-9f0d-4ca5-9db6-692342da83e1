package com.kc.system.mapper;

import java.util.List;
import com.kc.system.domain.DeptHighScoreQuota;
import org.apache.ibatis.annotations.Param;

/**
 * 部门高分配额Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface DeptHighScoreQuotaMapper 
{
    /**
     * 查询部门高分配额
     * 
     * @param id 部门高分配额主键
     * @return 部门高分配额
     */
    public DeptHighScoreQuota selectDeptHighScoreQuotaById(Long id);

    /**
     * 根据部门ID和年度查询配额
     * 
     * @param deptId 部门ID
     * @param evaluationYear 评价年度
     * @return 部门高分配额
     */
    public DeptHighScoreQuota selectByDeptAndYear(@Param("deptId") Long deptId, @Param("evaluationYear") String evaluationYear);

    /**
     * 查询部门高分配额列表
     * 
     * @param deptHighScoreQuota 部门高分配额
     * @return 部门高分配额集合
     */
    public List<DeptHighScoreQuota> selectDeptHighScoreQuotaList(DeptHighScoreQuota deptHighScoreQuota);

    /**
     * 新增部门高分配额
     * 
     * @param deptHighScoreQuota 部门高分配额
     * @return 结果
     */
    public int insertDeptHighScoreQuota(DeptHighScoreQuota deptHighScoreQuota);

    /**
     * 修改部门高分配额
     * 
     * @param deptHighScoreQuota 部门高分配额
     * @return 结果
     */
    public int updateDeptHighScoreQuota(DeptHighScoreQuota deptHighScoreQuota);

    /**
     * 删除部门高分配额
     * 
     * @param id 部门高分配额主键
     * @return 结果
     */
    public int deleteDeptHighScoreQuotaById(Long id);

    /**
     * 批量删除部门高分配额
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeptHighScoreQuotaByIds(Long[] ids);
}
