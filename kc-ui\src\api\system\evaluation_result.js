import request from '@/utils/request'

// 查询评价结果列表
export function listEvaluation_result(query) {
  return request({
    url: '/system/evaluation_result/list',
    method: 'get',
    params: query
  })
}

// 查询评价结果详细
export function getEvaluation_result(id) {
  return request({
    url: '/system/evaluation_result/' + id,
    method: 'get'
  })
}

// 新增评价结果
export function addEvaluation_result(data) {
  return request({
    url: '/system/evaluation_result',
    method: 'post',
    data: data
  })
}

// 修改评价结果
export function updateEvaluation_result(data) {
  return request({
    url: '/system/evaluation_result',
    method: 'put',
    data: data
  })
}

// 删除评价结果
export function delEvaluation_result(id) {
  return request({
    url: '/system/evaluation_result/' + id,
    method: 'delete'
  })
}

// 手动计算评价结果（调用存储过程）
export function calculateEvaluationResults(data) {
  return request({
    url: '/system/evaluation_result/calculate',
    method: 'post',
    params: data
  })
}

// 计算指定用户列表的评价结果
export function calculateUserEvaluationResults(data) {
  return request({
    url: '/system/evaluation_result/calculateUsers',
    method: 'post',
    data: data
  })
}

// 手动计算评价结果（使用Java实现）
export function calculateEvaluationResultsJava(data) {
  return request({
    url: '/system/evaluation_result/calculate-java',
    method: 'post',
    params: data
  })
}

// 获取评价结果计算明细
export function getEvaluationDetail(data) {
  return request({
    url: '/system/evaluation_result/detail',
    method: 'get',
    params: data
  })
}
