import request from '@/utils/request'

// 查询项目参与度分配列表
export function listParticipation(query) {
  return request({
    url: '/system/participation/list',
    method: 'get',
    params: query
  })
}

// 查询项目参与度分配列表（不分页）
export function listAllParticipation(query) {
  return request({
    url: '/system/participation/listAll',
    method: 'get',
    params: query
  })
}

// 查询项目参与度分配列表（关联用户和部门信息，不分页）
export function listAllParticipationWithUserInfo(query) {
  return request({
    url: '/system/participation/listAllWithUserInfo',
    method: 'get',
    params: query
  })
}

// 查询项目参与度分配详细
export function getParticipation(id) {
  return request({
    url: '/system/participation/' + id,
    method: 'get'
  })
}

// 新增项目参与度分配
export function addParticipation(data) {
  return request({
    url: '/system/participation',
    method: 'post',
    data: data
  })
}

// 修改项目参与度分配
export function updateParticipation(data) {
  return request({
    url: '/system/participation',
    method: 'put',
    data: data
  })
}

// 删除项目参与度分配
export function delParticipation(id) {
  return request({
    url: '/system/participation/' + id,
    method: 'delete'
  })
}

// 批量提交项目参与度分配
export function batchAddParticipation(dataList) {
  return request({
    url: '/system/participation/batchAdd',
    method: 'post',
    data: dataList
  })
}

// 批量更新项目参与度分配
export function batchUpdateParticipation(dataList) {
  return request({
    url: '/system/participation/batchUpdate',
    method: 'put',
    data: dataList
  })
}

// 获取用户当月精力分配数据
export function getUserMonthlyEffort(userName, month) {
  return request({
    url: '/system/participation/userEffort',
    method: 'get',
    params: {
      userName,
      month
    }
  })
}


