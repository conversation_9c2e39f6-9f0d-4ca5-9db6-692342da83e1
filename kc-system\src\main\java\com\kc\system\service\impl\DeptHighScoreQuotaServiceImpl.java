package com.kc.system.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.kc.common.core.domain.entity.SysDept;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.common.utils.DateUtils;
import com.kc.common.utils.SecurityUtils;
import com.kc.system.domain.HighScoreRecord;

import com.kc.system.mapper.HighScoreRecordMapper;
import com.kc.system.service.ISysDeptService;
import com.kc.system.service.ISysUserService;
import com.kc.system.service.IQuotaGroupService;
import com.kc.system.service.IQuotaManagementService;
import com.kc.system.domain.QuotaGroupQuota;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.kc.system.mapper.DeptHighScoreQuotaMapper;
import com.kc.system.domain.DeptHighScoreQuota;
import com.kc.system.service.IDeptHighScoreQuotaService;

/**
 * 部门高分配额Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Transactional
public class DeptHighScoreQuotaServiceImpl implements IDeptHighScoreQuotaService 
{
    private static final Logger log = LoggerFactory.getLogger(DeptHighScoreQuotaServiceImpl.class);
    
    @Autowired
    private DeptHighScoreQuotaMapper deptHighScoreQuotaMapper;
    
    @Autowired
    private HighScoreRecordMapper highScoreRecordMapper;
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IQuotaGroupService quotaGroupService;

    @Autowired
    private IQuotaManagementService quotaManagementService;

    private static final BigDecimal HIGH_SCORE_THRESHOLD = new BigDecimal("95.00");
    private static final BigDecimal QUOTA_PERCENTAGE = new BigDecimal("0.30");

    /**
     * 查询部门高分配额
     * 
     * @param id 部门高分配额主键
     * @return 部门高分配额
     */
    @Override
    public DeptHighScoreQuota selectDeptHighScoreQuotaById(Long id)
    {
        return deptHighScoreQuotaMapper.selectDeptHighScoreQuotaById(id);
    }

    /**
     * 查询部门高分配额列表
     * 
     * @param deptHighScoreQuota 部门高分配额
     * @return 部门高分配额
     */
    @Override
    public List<DeptHighScoreQuota> selectDeptHighScoreQuotaList(DeptHighScoreQuota deptHighScoreQuota)
    {
        return deptHighScoreQuotaMapper.selectDeptHighScoreQuotaList(deptHighScoreQuota);
    }

    /**
     * 新增部门高分配额
     * 
     * @param deptHighScoreQuota 部门高分配额
     * @return 结果
     */
    @Override
    public int insertDeptHighScoreQuota(DeptHighScoreQuota deptHighScoreQuota)
    {
        deptHighScoreQuota.setCreateTime(DateUtils.getNowDate());
        return deptHighScoreQuotaMapper.insertDeptHighScoreQuota(deptHighScoreQuota);
    }

    /**
     * 修改部门高分配额
     * 
     * @param deptHighScoreQuota 部门高分配额
     * @return 结果
     */
    @Override
    public int updateDeptHighScoreQuota(DeptHighScoreQuota deptHighScoreQuota)
    {
        deptHighScoreQuota.setUpdateTime(DateUtils.getNowDate());
        return deptHighScoreQuotaMapper.updateDeptHighScoreQuota(deptHighScoreQuota);
    }

    /**
     * 批量删除部门高分配额
     * 
     * @param ids 需要删除的部门高分配额主键
     * @return 结果
     */
    @Override
    public int deleteDeptHighScoreQuotaByIds(Long[] ids)
    {
        return deptHighScoreQuotaMapper.deleteDeptHighScoreQuotaByIds(ids);
    }

    /**
     * 删除部门高分配额信息
     * 
     * @param id 部门高分配额主键
     * @return 结果
     */
    @Override
    public int deleteDeptHighScoreQuotaById(Long id)
    {
        return deptHighScoreQuotaMapper.deleteDeptHighScoreQuotaById(id);
    }

    @Override
    public boolean checkQuotaAvailable(Long deptId, String year, int requestCount) {
        // 优先使用新的配额管理系统
        return quotaManagementService.checkQuotaAvailable(deptId, year, requestCount);
    }
    
    @Override
    public DeptHighScoreQuota getDeptQuotaInfo(Long deptId, String year) {
        // 首先检查部门是否属于配额组
        if (quotaGroupService.isDeptInQuotaGroup(deptId)) {
            // 如果属于配额组，返回基于配额组的配额信息
            QuotaGroupQuota groupQuota = quotaGroupService.getQuotaGroupQuotaByDept(deptId, year);
            if (groupQuota != null) {
                // 将配额组配额转换为部门配额格式返回
                DeptHighScoreQuota quota = new DeptHighScoreQuota();
                quota.setDeptId(deptId);
                quota.setEvaluationYear(year);
                quota.setTotalEmployees(groupQuota.getTotalEmployees());
                quota.setHighScoreQuota(groupQuota.getHighScoreQuota());
                quota.setUsedQuota(groupQuota.getUsedQuota());
                quota.setRemainingQuota(groupQuota.getRemainingQuota());
                return quota;
            }
        }

        // 使用传统的部门配额逻辑
        DeptHighScoreQuota quota = deptHighScoreQuotaMapper.selectByDeptAndYear(deptId, year);
        if (quota == null) {
            quota = initDeptQuota(deptId, year);
        }

        // 实时计算已使用配额
        int actualUsedQuota = highScoreRecordMapper.countByDeptAndYear(deptId, year);
        quota.setUsedQuota(actualUsedQuota);
        quota.setRemainingQuota(quota.getHighScoreQuota() - actualUsedQuota);

        // 更新数据库中的配额信息
        if (quota.getId() != null) {
            updateDeptHighScoreQuota(quota);
        }

        return quota;
    }

    @Override
    @Transactional
    public boolean useQuota(Long deptId, Long userId, String year, String month,
                           BigDecimal score, Long evaluatorId) {
        // 检查是否为高分
        if (score.compareTo(HIGH_SCORE_THRESHOLD) < 0) {
            return true; // 不是高分，无需使用配额
        }

        // 检查该用户本年度是否已有高分记录
        HighScoreRecord existingRecord = highScoreRecordMapper.selectByUserAndYear(userId, year);
        if (existingRecord != null) {
            // 如果已有记录，只更新评分，不消耗新配额
            existingRecord.setScore(score);
            existingRecord.setEvaluationMonth(month);
            existingRecord.setUpdateTime(new Date());
            highScoreRecordMapper.updateHighScoreRecord(existingRecord);
            log.info("用户[{}]更新高分记录，部门[{}]，年度[{}]，评分[{}]", userId, deptId, year, score);
            return true;
        }

        // 检查配额是否足够
        if (!checkQuotaAvailable(deptId, year, 1)) {
            log.warn("部门[{}]年度[{}]配额不足，无法为用户[{}]分配高分", deptId, year, userId);
            return false;
        }

        // 使用新的配额管理系统
        if (!quotaManagementService.useQuota(deptId, year, 1)) {
            log.warn("部门[{}]年度[{}]配额使用失败，用户[{}]", deptId, year, userId);
            return false;
        }

        // 记录高分
        HighScoreRecord record = new HighScoreRecord();
        record.setDeptId(deptId);
        record.setUserId(userId);
        record.setEvaluationYear(year);
        record.setEvaluationMonth(month);
        record.setScore(score);
        record.setEvaluatorId(evaluatorId);

        // 获取用户信息
        SysUser user = userService.selectUserById(userId);
        if (user != null) {
            record.setUserName(user.getUserName());
            record.setNickName(user.getNickName());
        }

        // 获取评价人信息
        SysUser evaluator = userService.selectUserById(evaluatorId);
        if (evaluator != null) {
            record.setEvaluatorName(evaluator.getNickName());
        }

        record.setCreateTime(new Date());
        int result = highScoreRecordMapper.insertHighScoreRecord(record);

        if (result > 0) {
            log.info("用户[{}]使用高分配额成功，部门[{}]，年度[{}]，评分[{}]", userId, deptId, year, score);
            return true;
        } else {
            log.error("用户[{}]高分记录插入失败，部门[{}]，年度[{}]，评分[{}]", userId, deptId, year, score);
            return false;
        }
    }

    @Override
    public boolean releaseQuota(Long deptId, Long userId, String year, String month) {
        HighScoreRecord record = highScoreRecordMapper.selectByUserAndYear(userId, year);
        if (record != null) {
            // 删除高分记录
            highScoreRecordMapper.deleteHighScoreRecordById(record.getId());

            // 释放配额 - 使用新的配额管理系统
            quotaManagementService.releaseQuota(deptId, year, 1);

            log.info("用户[{}]释放高分配额，部门[{}]，年度[{}]", userId, deptId, year);
            return true;
        }
        return false;
    }

    @Override
    public DeptHighScoreQuota initDeptQuota(Long deptId, String year) {
        // 计算部门总人数（排除部门负责人）
        SysUser queryUser = new SysUser();
        queryUser.setDeptId(deptId);
        List<SysUser> deptUsers = userService.selectUserList(queryUser);

        // 排除部门负责人
        SysDept dept = deptService.selectDeptById(deptId);
        int totalEmployees = (int) deptUsers.stream()
            .filter(user -> dept.getLeader() == null || !user.getUserName().equals(dept.getLeader()))
            .count();

        // 计算高分配额（30%）
        int highScoreQuota = (int) Math.floor(totalEmployees * QUOTA_PERCENTAGE.doubleValue());

        DeptHighScoreQuota quota = new DeptHighScoreQuota();
        quota.setDeptId(deptId);
        quota.setEvaluationYear(year);
        quota.setTotalEmployees(totalEmployees);
        quota.setHighScoreQuota(highScoreQuota);
        quota.setUsedQuota(0);
        quota.setRemainingQuota(highScoreQuota);
        quota.setCreateBy(SecurityUtils.getUsername());
        quota.setCreateTime(new Date());

        insertDeptHighScoreQuota(quota);
        log.info("初始化部门[{}]年度[{}]高分配额，总人数[{}]，配额[{}]", deptId, year, totalEmployees, highScoreQuota);
        return quota;
    }
}
