package com.kc.system.service;

import java.util.List;
import java.util.Map;
import com.kc.system.domain.ProjectInfo;

/**
 * 项目基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-14
 */
public interface IProjectInfoService 
{
    /**
     * 查询项目基础信息
     * 
     * @param id 项目基础信息主键
     * @return 项目基础信息
     */
    public ProjectInfo selectProjectInfoById(Long id);

    /**
     * 查询项目基础信息列表
     * 
     * @param projectInfo 项目基础信息
     * @return 项目基础信息集合
     */
    public List<ProjectInfo> selectProjectInfoList(ProjectInfo projectInfo);

    /**
     * 新增项目基础信息
     * 
     * @param projectInfo 项目基础信息
     * @return 结果
     */
    public int insertProjectInfo(ProjectInfo projectInfo);

    /**
     * 修改项目基础信息
     * 
     * @param projectInfo 项目基础信息
     * @return 结果
     */
    public int updateProjectInfo(ProjectInfo projectInfo);

    /**
     * 批量删除项目基础信息
     * 
     * @param ids 需要删除的项目基础信息主键集合
     * @return 结果
     */
    public int deleteProjectInfoByIds(Long[] ids);

    /**
     * 删除项目基础信息信息
     * 
     * @param id 项目基础信息主键
     * @return 结果
     */
    public int deleteProjectInfoById(Long id);

    /**
     * 查询当前用户负责的项目列表
     * 
     * @param userName 用户名
     * @return 项目列表
     */
    public List<ProjectInfo> selectUserLeadProjectList(String userName);

    /**
     * 查询用户所有项目工时
     */
    public List<Map<String, Object>> selectUserProjectWorkloads(String userName, String month);

    /**
     * 查询用户负责的项目列表
     * 
     * @param userName 用户名
     * @return 项目列表
     */
    public List<ProjectInfo> getUserLeadProjects(String userName);

    /**
     * 导入项目数据
     * 
     * @param projectList 项目数据列表
     * @param updateSupport 是否更新已存在的项目数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importProjectInfo(List<ProjectInfo> projectList, Boolean updateSupport, String operName);

    /**
     * 检查项目名称是否已存在
     *
     * @param projectName 项目名称
     * @param deptId 部门ID
     * @return 如果存在返回 true，否则返回 false
     */
    public boolean checkProjectNameExists(String projectName, Long deptId);

    /**
     * 根据部门ID查询项目列表
     * 
     * @param deptId 部门ID
     * @return 项目列表
     */
    public List<ProjectInfo> selectProjectsByDeptId(Long deptId);

    /**
     * 批量填报所有项目成员工时
     * 
     * @return 处理结果信息
     */
    public String batchAddWorkload();

    /**
     * 检查项目是否有依赖关系（精力分配和评分记录）
     *
     * @param projectId 项目ID
     * @return 依赖关系信息
     */
    public Map<String, Object> checkProjectDependencies(Long projectId);

    /**
     * 获取用户可选择的项目列表（排除承揽项目）
     *
     * @return 可选择的项目列表
     */
    public List<ProjectInfo> selectAvailableProjects();
}
