<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.EvaluationResultMapper">
    
    <resultMap type="EvaluationResult" id="EvaluationResultResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="evaluationMonth"    column="evaluation_month"    />
        <result property="finalScore"    column="final_score"    />
        <result property="managerScore"    column="manager_score"    />
        <result property="projectLeaderScore"    column="project_leader_score"    />
        <result property="userRole"    column="user_role"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectEvaluationResultVo">
        select e.id, e.user_id, e.user_name, e.nick_name, e.dept_id, d.dept_name, e.evaluation_month, e.final_score, e.manager_score, e.project_leader_score, e.user_role, e.created_at, e.updated_at 
        from evaluation_result e
        left join sys_dept d on e.dept_id = d.dept_id
    </sql>

    <select id="selectEvaluationResultList" parameterType="EvaluationResult" resultMap="EvaluationResultResult">
        select e.id, e.user_id, e.user_name, e.nick_name, e.dept_id, d.dept_name, e.evaluation_month, e.final_score, e.manager_score, 
               e.project_leader_score, e.user_role, e.created_at, e.updated_at
        from evaluation_result e
        left join sys_dept d on e.dept_id = d.dept_id
        left join sys_user u on e.user_id = u.user_id
        <where>  
            <if test="userId != null "> and e.user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and e.user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and e.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="deptId != null "> and e.dept_id = #{deptId}</if>
            <if test="deptName != null and deptName != ''"> and d.dept_name like concat('%', #{deptName}, '%')</if>
            <if test="evaluationMonth != null  and evaluationMonth != ''"> and e.evaluation_month = #{evaluationMonth}</if>
            <if test="finalScore != null "> and e.final_score = #{finalScore}</if>
            <if test="managerScore != null "> and e.manager_score = #{managerScore}</if>
            <if test="projectLeaderScore != null "> and e.project_leader_score = #{projectLeaderScore}</if>
            <if test="userRole != null  and userRole != ''"> and e.user_role = #{userRole}</if>
            <if test="createdAt != null "> and e.created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and e.updated_at = #{updatedAt}</if>
            <!-- 数据权限过滤 -->
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectEvaluationResultListWithDeptScope" resultMap="EvaluationResultResult">
        select e.id, e.user_id, e.user_name, e.nick_name, e.dept_id, d.dept_name, e.evaluation_month, e.final_score, e.manager_score, 
               e.project_leader_score, e.user_role, e.created_at, e.updated_at
        from evaluation_result e
        left join sys_dept d on e.dept_id = d.dept_id
        <where>  
            <if test="evaluationResult.userId != null "> and e.user_id = #{evaluationResult.userId}</if>
            <if test="evaluationResult.userName != null  and evaluationResult.userName != ''"> and e.user_name like concat('%', #{evaluationResult.userName}, '%')</if>
            <if test="evaluationResult.nickName != null  and evaluationResult.nickName != ''"> and e.nick_name like concat('%', #{evaluationResult.nickName}, '%')</if>
            <if test="evaluationResult.deptId != null "> and e.dept_id = #{evaluationResult.deptId}</if>
            <if test="evaluationResult.deptName != null and evaluationResult.deptName != ''"> and d.dept_name like concat('%', #{evaluationResult.deptName}, '%')</if>
            <if test="evaluationResult.evaluationMonth != null  and evaluationResult.evaluationMonth != ''"> and e.evaluation_month = #{evaluationResult.evaluationMonth}</if>
            <if test="evaluationResult.finalScore != null "> and e.final_score = #{evaluationResult.finalScore}</if>
            <if test="evaluationResult.managerScore != null "> and e.manager_score = #{evaluationResult.managerScore}</if>
            <if test="evaluationResult.projectLeaderScore != null "> and e.project_leader_score = #{evaluationResult.projectLeaderScore}</if>
            <if test="evaluationResult.userRole != null  and evaluationResult.userRole != ''"> and e.user_role = #{evaluationResult.userRole}</if>
            <if test="evaluationResult.createdAt != null "> and e.created_at = #{evaluationResult.createdAt}</if>
            <if test="evaluationResult.updatedAt != null "> and e.updated_at = #{evaluationResult.updatedAt}</if>
            
            <!-- 数据权限过滤 -->
            <if test="deptIds != null and deptIds.size() > 0">
                and e.dept_id in
                <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
    </select>
    
    <select id="selectEvaluationResultById" parameterType="Long" resultMap="EvaluationResultResult">
        select e.id, e.user_id, e.user_name, e.nick_name, e.dept_id, d.dept_name, e.evaluation_month, e.final_score, e.manager_score, 
               e.project_leader_score, e.user_role, e.created_at, e.updated_at
        from evaluation_result e
        left join sys_dept d on e.dept_id = d.dept_id
        where e.id = #{id}
    </select>

    <insert id="insertEvaluationResult" parameterType="EvaluationResult" useGeneratedKeys="true" keyProperty="id">
        insert into evaluation_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="evaluationMonth != null and evaluationMonth != ''">evaluation_month,</if>
            <if test="finalScore != null">final_score,</if>
            <if test="managerScore != null">manager_score,</if>
            <if test="projectLeaderScore != null">project_leader_score,</if>
            <if test="userRole != null and userRole != ''">user_role,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="evaluationMonth != null and evaluationMonth != ''">#{evaluationMonth},</if>
            <if test="finalScore != null">#{finalScore},</if>
            <if test="managerScore != null">#{managerScore},</if>
            <if test="projectLeaderScore != null">#{projectLeaderScore},</if>
            <if test="userRole != null and userRole != ''">#{userRole},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateEvaluationResult" parameterType="EvaluationResult">
        update evaluation_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="evaluationMonth != null and evaluationMonth != ''">evaluation_month = #{evaluationMonth},</if>
            <if test="finalScore != null">final_score = #{finalScore},</if>
            <if test="managerScore != null">manager_score = #{managerScore},</if>
            <if test="projectLeaderScore != null">project_leader_score = #{projectLeaderScore},</if>
            <if test="userRole != null and userRole != ''">user_role = #{userRole},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluationResultById" parameterType="Long">
        delete from evaluation_result where id = #{id}
    </delete>

    <delete id="deleteEvaluationResultByIds" parameterType="String">
        delete from evaluation_result where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 定义存储过程参数映射 -->
    <parameterMap id="calculateEvaluationResultsParam" type="java.util.Map">
        <parameter property="evaluationMonth" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="userId" mode="IN" jdbcType="BIGINT"/>
        <parameter property="deptId" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>

    <!-- 调用存储过程计算评价结果 -->
    <select id="callCalculateEvaluationResults" statementType="CALLABLE" parameterMap="calculateEvaluationResultsParam">
        {call calculate_evaluation_results_with_datascope(?, ?, ?)}
    </select>
    
    <!-- 根据评价月份删除评价结果 -->
    <delete id="deleteEvaluationResultByMonth" parameterType="java.util.Map">
        delete from evaluation_result
        where evaluation_month = #{evaluationMonth}
    </delete>

    <!-- 根据用户ID和评价月份删除评价结果 -->
    <delete id="deleteEvaluationResultByUserAndMonth" parameterType="java.util.Map">
        delete from evaluation_result
        where user_id = #{userId} and evaluation_month = #{evaluationMonth}
    </delete>

    <!-- 根据用户ID列表获取用户角色信息 -->
    <select id="getUserRolesByUserIds" resultType="java.util.Map">
        SELECT
            u.user_id,
            u.user_name,
            u.nick_name,
            u.dept_id,
            d.dept_name,
            CASE
                WHEN pl.user_name IS NOT NULL AND pm.user_name IS NOT NULL THEN 'both'
                WHEN pl.user_name IS NOT NULL THEN 'project_leader'
                WHEN pm.user_name IS NOT NULL THEN 'project_member'
                ELSE 'none'
            END as user_role
        FROM sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN (
            SELECT DISTINCT user_name
            FROM project_members
            WHERE role = '负责人'
        ) pl ON u.user_name = pl.user_name
        LEFT JOIN (
            SELECT DISTINCT user_name
            FROM project_members
            WHERE role != '负责人'
        ) pm ON u.user_name = pm.user_name
        WHERE FIND_IN_SET(u.user_id, #{userIds})
        AND u.del_flag = '0'
    </select>

    <!-- 根据用户ID列表获取机构负责人评分 -->
    <select id="getManagerScoresByUserIds" resultType="java.util.Map">
        SELECT
            pe.evaluatee_id as user_id,
            pe.score as manager_score,
            pe.evaluation_type
        FROM project_evaluation pe
        WHERE pe.evaluation_type IN ('manager', 'parent_manager')
        AND pe.evaluation_month = #{evaluationMonth}
        AND FIND_IN_SET(pe.evaluatee_id, #{userIds})
        ORDER BY pe.evaluatee_id,
                 CASE pe.evaluation_type
                     WHEN 'manager' THEN 1
                     WHEN 'parent_manager' THEN 2
                     ELSE 3
                 END
    </select>

    <!-- 根据用户ID列表获取项目负责人评分 -->
    <select id="getProjectLeaderScoresByUserIds" resultType="java.util.Map">
        SELECT
            pe.evaluatee_id as user_id,
            ROUND(
                SUM(pe.score * COALESCE(pp.participation_rate, 0)) /
                NULLIF(SUM(COALESCE(pp.participation_rate, 0)), 0),
                2
            ) as project_leader_score
        FROM project_evaluation pe
        LEFT JOIN sys_user u ON pe.evaluatee_id = u.user_id
        LEFT JOIN project_participation pp ON u.user_name = pp.user_name
            AND pe.project_id = pp.project_id
            AND pe.evaluation_month = pp.month
        WHERE pe.evaluation_type = 'project_leader'
        AND pe.evaluation_month = #{evaluationMonth}
        AND FIND_IN_SET(pe.evaluatee_id, #{userIds})
        GROUP BY pe.evaluatee_id
        ORDER BY pe.evaluatee_id
    </select>
</mapper>