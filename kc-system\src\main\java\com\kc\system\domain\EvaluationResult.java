package com.kc.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 评价结果对象 evaluation_result
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
public class EvaluationResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名 */
    @Excel(name = "员工编号")
    private String userName;
    
    /** 用户昵称 */
    @Excel(name = "姓名")
    private String nickName;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;
    
    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 评价月份 格式：yyyy-MM */
    @Excel(name = "评价月份 格式：yyyy-MM")
    private String evaluationMonth;

    /** 最终评分 */
    @Excel(name = "最终评分")
    private BigDecimal finalScore;

    /** 机构负责人评分 */
    @Excel(name = "机构负责人评分")
    private BigDecimal managerScore;

    /** 项目负责人平均评分 */
    @Excel(name = "项目负责人平均评分")
    private BigDecimal projectLeaderScore;

    /** 用户角色：project_leader-仅项目负责人,project_member-仅项目成员,both-既是负责人又是成员,none-不参与项目 */
    @Excel(name = "用户角色：project_leader-仅项目负责人,project_member-仅项目成员,both-既是负责人又是成员,none-不参与项目")
    private String userRole;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    
    public void setEvaluationMonth(String evaluationMonth) 
    {
        this.evaluationMonth = evaluationMonth;
    }

    public String getEvaluationMonth() 
    {
        return evaluationMonth;
    }
    public void setFinalScore(BigDecimal finalScore) 
    {
        this.finalScore = finalScore;
    }

    public BigDecimal getFinalScore() 
    {
        return finalScore;
    }
    public void setManagerScore(BigDecimal managerScore) 
    {
        this.managerScore = managerScore;
    }

    public BigDecimal getManagerScore() 
    {
        return managerScore;
    }
    public void setProjectLeaderScore(BigDecimal projectLeaderScore) 
    {
        this.projectLeaderScore = projectLeaderScore;
    }

    public BigDecimal getProjectLeaderScore() 
    {
        return projectLeaderScore;
    }
    public void setUserRole(String userRole) 
    {
        this.userRole = userRole;
    }

    public String getUserRole() 
    {
        return userRole;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("evaluationMonth", getEvaluationMonth())
            .append("finalScore", getFinalScore())
            .append("managerScore", getManagerScore())
            .append("projectLeaderScore", getProjectLeaderScore())
            .append("userRole", getUserRole())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
