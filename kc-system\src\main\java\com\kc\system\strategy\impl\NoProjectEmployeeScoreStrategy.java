package com.kc.system.strategy.impl;

import com.kc.system.domain.ProjectEvaluation;
import com.kc.system.dto.ScoreCalculationContext;
import com.kc.system.strategy.ScoreCalculationStrategy;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.util.List;

/**
 * 无项目员工评分策略
 * 适用于没有项目参与的普通员工
 * 计算规则：直接使用机构负责人评分
 * 
 * <AUTHOR>
 */
@Component
public class NoProjectEmployeeScoreStrategy implements ScoreCalculationStrategy {
    
    private static final String STRATEGY_TYPE = "NO_PROJECT_EMPLOYEE";
    
    @Override
    public BigDecimal calculateScore(ScoreCalculationContext context) {
        List<ProjectEvaluation> evaluations = context.getEvaluations();

        // 获取机构负责人评分
        BigDecimal managerScore = getManagerScore(evaluations);

        return managerScore != null ? managerScore : BigDecimal.ZERO;
    }

    @Override
    public String getStrategyType() {
        return STRATEGY_TYPE;
    }

    @Override
    public boolean isApplicable(ScoreCalculationContext context) {
        // 适用于没有项目参与且不是子部门成员的员工
        return !Boolean.TRUE.equals(context.getIsSubDeptMember())
                && !hasProjectParticipation(context);
    }

    @Override
    public String getCalculationDetails(ScoreCalculationContext context) {
        List<ProjectEvaluation> evaluations = context.getEvaluations();
        BigDecimal managerScore = getManagerScore(evaluations);

        return String.format("无项目员工评分：机构负责人评分(%.2f)",
                managerScore != null ? managerScore : 0);
    }

    private BigDecimal getManagerScore(List<ProjectEvaluation> evaluations) {
        return evaluations.stream()
                .filter(eval -> "manager".equals(eval.getEvaluationType()))
                .map(ProjectEvaluation::getScore)
                .findFirst()
                .orElse(null);
    }
    
    private boolean hasProjectParticipation(ScoreCalculationContext context) {
        return context.getProjectParticipations() != null 
                && !context.getProjectParticipations().isEmpty();
    }
}
