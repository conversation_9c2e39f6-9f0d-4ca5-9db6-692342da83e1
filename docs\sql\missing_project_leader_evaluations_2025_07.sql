-- ===== 补充缺失的项目负责人评分记录 =====
-- 评价月份：2025-07
-- 基于闫敬书、杜文韬、乔彦涵的未评分项目生成

-- ===== 闫敬书（169）的未评分项目 =====

-- 1. 高温热源耦合热化学循环制氢系统设计分析研究（项目311）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1001, 311, 156, 169, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 新型低成本制氢技术开发（项目312）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1002, 312, 181, 169, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 石城子100MW光热发电科技示范项目验收服务（项目329）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1003, 329, 181, 169, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 杜文韬（172）的未评分项目 =====

-- 1. 光热发电系统配置与运行优化研究（项目261）- 负责人：闫敬书（169）
INSERT INTO `project_evaluation` VALUES (1004, 261, 169, 172, 90.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 二氧化碳提纯和高值利用关键技术研究（项目271）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1005, 271, 156, 172, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 高温热源耦合热化学循环制氢系统设计分析研究（项目311）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1006, 311, 156, 172, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 二氧化碳捕集与原位加氢一体转化关键技术研究（项目327）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1007, 327, 156, 172, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 乔彦涵（175）的未评分项目 =====

-- 1. 光热发电系统配置与运行优化研究（项目261）- 负责人：闫敬书（169）
INSERT INTO `project_evaluation` VALUES (1008, 261, 169, 175, 85.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 绿氢制备系统高频谐波诊断与集群协同控制技术研究（项目273）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1009, 273, 156, 175, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 石城子100MW光热发电科技示范项目验收服务（项目329）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1010, 329, 181, 175, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 段琦玮（156）的未评分项目 =====

-- 1. 大唐多伦15万千瓦风光制氢一体化能量管控平台（项目316）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1011, 316, 181, 156, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 杨文博（174）的未评分项目 =====

-- 1. 二氧化碳提纯和高值利用关键技术研究（项目271）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1012, 271, 156, 174, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 无储能风电离网制氢系统关键技术研究（项目272）- 负责人：段琦玮（156）
INSERT INTO `project_evaluation` VALUES (1013, 272, 156, 174, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 绿氢制备系统高频谐波诊断与集群协同控制技术研究（项目273）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1014, 273, 156, 174, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 新型低成本制氢技术开发（项目312）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1015, 312, 181, 174, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 5. 大唐多伦15万千瓦风光制氢一体化能量管控平台（项目316）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1016, 316, 181, 174, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 6. 氢电耦合零碳园区建设模式与机制研究（项目324）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1017, 324, 156, 174, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 7. 新型低成本光伏电催化制氢技术及成套装备（项目326）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1018, 326, 181, 174, 96.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 蒋成（202）的未评分项目 =====

-- 1. 化学链燃烧发电关键技术（项目257）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1019, 257, 181, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 无储能风电离网制氢系统关键技术研究（项目272）- 负责人：段琦玮（156）
INSERT INTO `project_evaluation` VALUES (1020, 272, 156, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 绿氢制备系统高频谐波诊断与集群协同控制技术研究（项目273）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1021, 273, 156, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 氢电耦合零碳园区建设模式与机制研究（项目324）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1022, 324, 156, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 5. 新型低成本制氢技术开发（项目312）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1023, 312, 181, 202, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 6. 新型低成本光伏电催化制氢技术及成套装备（项目326）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1024, 326, 181, 202, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 段琦玮（155）的未评分项目 =====

-- 1. 大唐多伦15万千瓦风光制氢一体化能量管控平台（项目316）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1011, 316, 181, 155, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 杨文博（174）的未评分项目 =====

-- 1. 二氧化碳提纯和高值利用关键技术研究（项目271）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1012, 271, 156, 174, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 无储能风电离网制氢系统关键技术研究（项目272）- 负责人：段琦玮（155）
INSERT INTO `project_evaluation` VALUES (1013, 272, 155, 174, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 绿氢制备系统高频谐波诊断与集群协同控制技术研究（项目273）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1014, 273, 156, 174, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 新型低成本制氢技术开发（项目312）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1015, 312, 181, 174, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 5. 大唐多伦15万千瓦风光制氢一体化能量管控平台（项目316）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1016, 316, 181, 174, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 6. 氢电耦合零碳园区建设模式与机制研究（项目324）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1017, 324, 156, 174, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 7. 新型低成本光伏电催化制氢技术及成套装备（项目326）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1018, 326, 181, 174, 96.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 蒋成（202）的未评分项目 =====

-- 1. 化学链燃烧发电关键技术（项目257）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1019, 257, 181, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 无储能风电离网制氢系统关键技术研究（项目272）- 负责人：段琦玮（155）
INSERT INTO `project_evaluation` VALUES (1020, 272, 155, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 绿氢制备系统高频谐波诊断与集群协同控制技术研究（项目273）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1021, 273, 156, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 氢电耦合零碳园区建设模式与机制研究（项目324）- 负责人：冯少广（156）
INSERT INTO `project_evaluation` VALUES (1022, 324, 156, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 5. 新型低成本制氢技术开发（项目312）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1023, 312, 181, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 6. 新型低成本光伏电催化制氢技术及成套装备（项目326）- 负责人：刘海洋（181）
INSERT INTO `project_evaluation` VALUES (1024, 326, 181, 202, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 验证插入的记录 =====
SELECT 
    '新增评分记录验证' as verification_type,
    pe.id,
    pi.project_name,
    su_evaluator.nick_name as evaluator_name,
    su_evaluatee.nick_name as evaluatee_name,
    pe.score,
    pe.evaluation_month,
    pe.evaluation_type
FROM project_evaluation pe
INNER JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE pe.id BETWEEN 1001 AND 1024
ORDER BY pe.id;

-- ===== 统计各人的项目负责人评分完成情况 =====

-- 闫敬书的项目负责人评分统计
SELECT
    '闫敬书项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '闫敬书'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 杜文韬的项目负责人评分统计
SELECT
    '杜文韬项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '杜文韬'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 乔彦涵的项目负责人评分统计
SELECT
    '乔彦涵项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '乔彦涵'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 段琦玮的项目负责人评分统计
SELECT
    '段琦玮项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '段琦玮'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 杨文博的项目负责人评分统计
SELECT
    '杨文博项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '杨文博'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 蒋成的项目负责人评分统计
SELECT
    '蒋成项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '蒋成'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- ===== 计算最终评分验证 =====
-- 验证最终评分计算（机构负责人评分 * 0.4 + 项目负责人平均分 * 0.6）

-- 闫敬书最终评分计算
SELECT 
    '闫敬书最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '闫敬书' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2 
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id 
         WHERE su2.nick_name = '闫敬书' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 杜文韬最终评分计算
SELECT 
    '杜文韬最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '杜文韬' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2 
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id 
         WHERE su2.nick_name = '杜文韬' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 乔彦涵最终评分计算
SELECT
    '乔彦涵最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        (SELECT pe1.score FROM project_evaluation pe1
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id
         WHERE su1.nick_name = '乔彦涵' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '乔彦涵' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 段琦玮最终评分计算
SELECT
    '段琦玮最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        (SELECT pe1.score FROM project_evaluation pe1
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id
         WHERE su1.nick_name = '段琦玮' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '段琦玮' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 杨文博最终评分计算
SELECT
    '杨文博最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        (SELECT pe1.score FROM project_evaluation pe1
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id
         WHERE su1.nick_name = '杨文博' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '杨文博' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 蒋成最终评分计算
SELECT
    '蒋成最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        (SELECT pe1.score FROM project_evaluation pe1
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id
         WHERE su1.nick_name = '蒋成' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '蒋成' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;
