package com.kc.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 员工奖金分配对象 employee_bonus_allocation
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class EmployeeBonusAllocation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 部门奖金分配ID */
    @Excel(name = "部门奖金分配ID")
    private Long deptBonusId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 用户姓名 */
    @Excel(name = "用户姓名")
    private String nickName;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 分配月份 格式：yyyy-MM */
    @Excel(name = "分配月份")
    private String allocationMonth;

    /** 分配奖金金额(可为负数) */
    @Excel(name = "分配奖金金额")
    private BigDecimal bonusAmount;

    /** 分配原因/备注 */
    @Excel(name = "分配原因")
    private String allocationReason;

    /** 分配人ID(部门负责人) */
    private Long allocatorId;

    /** 分配人姓名 */
    @Excel(name = "分配人姓名")
    private String allocatorName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptBonusId(Long deptBonusId) 
    {
        this.deptBonusId = deptBonusId;
    }

    public Long getDeptBonusId() 
    {
        return deptBonusId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }
    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public String getDeptName()
    {
        return deptName;
    }
    public void setAllocationMonth(String allocationMonth)
    {
        this.allocationMonth = allocationMonth;
    }

    public String getAllocationMonth() 
    {
        return allocationMonth;
    }
    public void setBonusAmount(BigDecimal bonusAmount) 
    {
        this.bonusAmount = bonusAmount;
    }

    public BigDecimal getBonusAmount() 
    {
        return bonusAmount;
    }
    public void setAllocationReason(String allocationReason) 
    {
        this.allocationReason = allocationReason;
    }

    public String getAllocationReason() 
    {
        return allocationReason;
    }
    public void setAllocatorId(Long allocatorId) 
    {
        this.allocatorId = allocatorId;
    }

    public Long getAllocatorId() 
    {
        return allocatorId;
    }
    public void setAllocatorName(String allocatorName) 
    {
        this.allocatorName = allocatorName;
    }

    public String getAllocatorName() 
    {
        return allocatorName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptBonusId", getDeptBonusId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("allocationMonth", getAllocationMonth())
            .append("bonusAmount", getBonusAmount())
            .append("allocationReason", getAllocationReason())
            .append("allocatorId", getAllocatorId())
            .append("allocatorName", getAllocatorName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
