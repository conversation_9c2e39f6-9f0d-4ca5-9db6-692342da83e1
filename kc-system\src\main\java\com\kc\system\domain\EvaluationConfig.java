package com.kc.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 评价系统配置对象 evaluation_config
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
public class EvaluationConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long id;

    /** 配置类型：participation-项目考核系数, leader_score-项目负责人评分, manager_score-机构负责人评分 */
    @Excel(name = "配置类型")
    private String configType;

    /** 是否启用（Y-是，N-否） */
    @Excel(name = "是否启用", readConverterExp = "Y=是,N=否")
    private String enabled;

    /** 开始填报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始填报时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 结束填报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束填报时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 评价月份 格式：yyyy-MM */
    @Excel(name = "评价月份")
    private String month;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setConfigType(String configType) 
    {
        this.configType = configType;
    }

    public String getConfigType() 
    {
        return configType;
    }
    
    public void setEnabled(String enabled) 
    {
        this.enabled = enabled;
    }

    public String getEnabled() 
    {
        return enabled;
    }
    
    public void setStartDate(Date startDate) 
    {
        this.startDate = startDate;
    }

    public Date getStartDate() 
    {
        return startDate;
    }
    
    public void setEndDate(Date endDate) 
    {
        this.endDate = endDate;
    }

    public Date getEndDate() 
    {
        return endDate;
    }
    
    public void setMonth(String month) 
    {
        this.month = month;
    }

    public String getMonth() 
    {
        return month;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("configType", getConfigType())
            .append("enabled", getEnabled())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("month", getMonth())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 