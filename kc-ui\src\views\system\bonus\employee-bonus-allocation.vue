<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分配月份" prop="allocationMonth">
        <el-date-picker
          v-model="queryParams.allocationMonth"
          type="month"
          value-format="yyyy-MM"
          placeholder="选择月份">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="员工姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入员工姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="部门" prop="deptId">
        <el-select v-model="queryParams.deptId" placeholder="请选择部门" clearable>
          <el-option
            v-for="dept in deptList"
            :key="dept.deptId"
            :label="dept.deptName"
            :value="dept.deptId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:employeeBonus:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="employeeBonusList">
      <el-table-column label="员工姓名" align="center" prop="nickName" />
      <el-table-column label="员工编号" align="center" prop="userName" />
      <el-table-column label="部门" align="center" prop="deptId">
        <template slot-scope="scope">
          {{ getDeptName(scope.row.deptId) }}
        </template>
      </el-table-column>
      <el-table-column label="分配月份" align="center" prop="allocationMonth" width="100" />
      <el-table-column label="奖金金额" align="center" prop="bonusAmount" width="120">
        <template slot-scope="scope">
          <span :class="scope.row.bonusAmount >= 0 ? 'text-success' : 'text-danger'">
            {{ formatMoney(scope.row.bonusAmount) }}
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="分配原因" align="center" prop="allocationReason" :show-overflow-tooltip="true" /> -->
      <el-table-column label="分配人" align="center" prop="allocatorName" width="100" />
      <el-table-column label="分配时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listEmployeeBonus } from "@/api/system/employeeBonus";
import { listDept } from "@/api/system/dept";

export default {
  name: "EmployeeBonusAllocation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 员工奖金分配表格数据
      employeeBonusList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        allocationMonth: null,
        nickName: null,
        deptId: null
      },
      // 部门列表
      deptList: []
    };
  },
  created() {
    this.getList();
    this.getDeptList();
  },
  methods: {
    /** 查询员工奖金分配列表 */
    getList() {
      this.loading = true;
      listEmployeeBonus(this.queryParams).then(response => {
        this.employeeBonusList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取部门列表 */
    getDeptList() {
      listDept().then(response => {
        this.deptList = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/employeeBonus/export', {
        ...this.queryParams
      }, `employeeBonus_${new Date().getTime()}.xlsx`)
    },
    /** 格式化金额 */
    formatMoney(amount) {
      if (amount == null) return '¥0.00';
      return '¥' + Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    /** 获取部门名称 */
    getDeptName(deptId) {
      const dept = this.deptList.find(d => d.deptId === deptId);
      return dept ? dept.deptName : '未知部门';
    }
  }
};
</script>

<style scoped>
.text-success {
  color: #67C23A;
}
.text-danger {
  color: #F56C6C;
}
</style>
