package com.kc.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;
import javax.validation.constraints.Pattern;

/**
 * 员工薪酬对象 employee_salary
 * 
 * <AUTHOR>
 * @date 2025-02-17
 */
public class EmployeeSalary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 员工编号 */
    @Excel(name = "员工编号")
    private String userName;

    /** 员工姓名 */
    @Excel(name = "员工姓名")
    private String realName;

    /** 工资月份(格式：YYYY-MM) */
    @Excel(name = "工资月份(格式：YYYY-MM)")
    @Pattern(regexp = "^\\d{4}-(?:0[1-9]|1[0-2])$", message = "月份格式不正确，应为YYYY-MM格式")
    private String salaryMonth;

    /** 工资数值 */
    @Excel(name = "工资数值")
    private BigDecimal salary;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedAt;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setRealName(String realName) 
    {
        this.realName = realName;
    }

    public String getRealName() 
    {
        return realName;
    }
    public void setSalaryMonth(String salaryMonth) 
    {
        this.salaryMonth = salaryMonth;
    }

    public String getSalaryMonth() 
    {
        return salaryMonth;
    }
    public void setSalary(BigDecimal salary) 
    {
        this.salary = salary;
    }

    public BigDecimal getSalary() 
    {
        return salary;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userName", getUserName())
            .append("realName", getRealName())
            .append("salaryMonth", getSalaryMonth())
            .append("salary", getSalary())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .toString();
    }
}
