package com.kc.system.service;

import java.util.List;
import java.util.Map;
import com.kc.system.domain.ProjectSalaryDetail;

/**
 * 项目劳务费构成Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface IProjectSalaryDetailService 
{
    /**
     * 查询项目劳务费构成
     * 
     * @param id 项目劳务费构成主键
     * @return 项目劳务费构成
     */
    public ProjectSalaryDetail selectProjectSalaryDetailById(Long id);

    /**
     * 查询项目劳务费构成列表
     * 
     * @param projectSalaryDetail 项目劳务费构成
     * @return 项目劳务费构成集合
     */
    public List<ProjectSalaryDetail> selectProjectSalaryDetailList(ProjectSalaryDetail projectSalaryDetail);

    /**
     * 新增项目劳务费构成
     * 
     * @param projectSalaryDetail 项目劳务费构成
     * @return 结果
     */
    public int insertProjectSalaryDetail(ProjectSalaryDetail projectSalaryDetail);

    /**
     * 修改项目劳务费构成
     * 
     * @param projectSalaryDetail 项目劳务费构成
     * @return 结果
     */
    public int updateProjectSalaryDetail(ProjectSalaryDetail projectSalaryDetail);

    /**
     * 批量删除项目劳务费构成
     * 
     * @param ids 需要删除的项目劳务费构成主键集合
     * @return 结果
     */
    public int deleteProjectSalaryDetailByIds(Long[] ids);

    /**
     * 删除项目劳务费构成信息
     * 
     * @param id 项目劳务费构成主键
     * @return 结果
     */
    public int deleteProjectSalaryDetailById(Long id);

    /**
     * 根据项目ID和工作月份查询劳务费明细
     * 
     * @param projectId 项目ID
     * @param workMonth 工作月份
     * @return 劳务费明细列表
     */
    public List<Map<String, Object>> selectProjectSalaryDetailsByProjectIdAndMonth(Long projectId, String workMonth);
}
