-- ===== 补充友好并网与新型配网技术研究所人员的项目负责人评分记录 =====
-- 评价月份：2025-07
-- 基于用户提供的精力分配明细和评分状态生成

-- 用户信息：
-- 丛聪 - 用户ID: 158, 机构负责人评分: 91分
-- 刘志杰 - 用户ID: 182, 机构负责人评分: 93分
-- 师长立 - 用户ID: 204, 机构负责人评分: 90分
-- 杨璐 - 用户ID: 205, 机构负责人评分: 90分
-- 刘伟东 - 用户ID: 206, 机构负责人评分: 94分
-- 王乐 - 用户ID: 207, 机构负责人评分: 90分

-- 项目信息：
-- 项目275: 新能源高渗透率下逆变器主导新型电力系统融合控制技术研究 - 负责人: 丛聪(158)
-- 项目276: 新能源场站主动支撑场景下系统强度评估及提升技术研究 - 负责人: 刘志杰(182)
-- 项目297: 支撑受端电网的柔性直流与送端多能广域协同技术 - 负责人: 殷波(157)
-- 项目309: 大规模新能源汇集区电力电子化动态无功协同优化与电压安全防御关键技术研究 - 负责人: 刘志杰(182)
-- 项目322: 闪电河项目 - 负责人: 殷波(157)
-- 项目323: 河池源网荷储一体化项目 - 负责人: 殷波(157)
-- 项目279: 规模化"风光储氢+"综合能源系统半实物仿真测试平台开发 - 负责人: 南雄(164)

-- ===== 丛聪（158）的未评分项目 =====

-- 1. 新能源场站主动支撑场景下系统强度评估及提升技术研究（项目276）- 负责人：刘志杰（182）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5001, 276, 182, 158, 90.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 支撑受端电网的柔性直流与送端多能广域协同技术（项目297）- 负责人：殷波（157）
-- 精力分配：70.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5002, 297, 157, 158, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 闪电河项目（项目322）- 负责人：殷波（157）
-- 精力分配：5.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5003, 322, 157, 158, 89.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 河池源网荷储一体化项目（项目323）- 负责人：殷波（157）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5004, 323, 157, 158, 90.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 刘志杰（182）的未评分项目 =====

-- 1. 支撑受端电网的柔性直流与送端多能广域协同技术（项目297）- 负责人：殷波（157）
-- 精力分配：5.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5005, 297, 157, 182, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 闪电河项目（项目322）- 负责人：殷波（157）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5006, 322, 157, 182, 93.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 河池源网荷储一体化项目（项目323）- 负责人：殷波（157）
-- 精力分配：5.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5007, 323, 157, 182, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 师长立（204）的未评分项目 =====

-- 1. 规模化"风光储氢+"综合能源系统半实物仿真测试平台开发（项目279）- 负责人：南雄（164）
-- 精力分配：90.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5008, 279, 164, 204, 91.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 支撑受端电网的柔性直流与送端多能广域协同技术（项目297）- 负责人：殷波（157）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5009, 297, 157, 204, 89.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 杨璐（205）的未评分项目 =====

-- 1. 支撑受端电网的柔性直流与送端多能广域协同技术（项目297）- 负责人：殷波（157）
-- 精力分配：100.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5010, 297, 157, 205, 90.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 刘伟东（206）的未评分项目 =====

-- 1. 支撑受端电网的柔性直流与送端多能广域协同技术（项目297）- 负责人：殷波（157）
-- 精力分配：70.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5011, 297, 157, 206, 95.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 大规模新能源汇集区电力电子化动态无功协同优化与电压安全防御关键技术研究（项目309）- 负责人：刘志杰（182）
-- 精力分配：30.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5012, 309, 182, 206, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 王乐（207）的未评分项目 =====

-- 1. 支撑受端电网的柔性直流与送端多能广域协同技术（项目297）- 负责人：殷波（157）
-- 精力分配：60.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (5013, 297, 157, 207, 91.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 验证插入的记录 =====
SELECT 
    '新增评分记录验证' as verification_type,
    pe.id,
    pi.project_name,
    su_evaluator.nick_name as evaluator_name,
    su_evaluatee.nick_name as evaluatee_name,
    pe.score,
    pe.evaluation_month,
    pe.evaluation_type,
    pe.comments
FROM project_evaluation pe
INNER JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE pe.id BETWEEN 5001 AND 5013
ORDER BY pe.id;

-- ===== 统计各人的项目负责人评分完成情况 =====

-- 丛聪的项目负责人评分统计
SELECT 
    '丛聪项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '丛聪'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 刘志杰的项目负责人评分统计
SELECT 
    '刘志杰项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '刘志杰'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 师长立的项目负责人评分统计
SELECT 
    '师长立项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '师长立'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 杨璐的项目负责人评分统计
SELECT 
    '杨璐项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '杨璐'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 刘伟东的项目负责人评分统计
SELECT 
    '刘伟东项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '刘伟东'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 王乐的项目负责人评分统计
SELECT
    '王乐项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '王乐'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- ===== 最终评分计算 =====

-- 丛聪最终评分计算
SELECT
    '丛聪最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        91 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '丛聪' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 刘志杰最终评分计算
SELECT
    '刘志杰最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        93 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '刘志杰' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 师长立最终评分计算
SELECT
    '师长立最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        90 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '师长立' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 杨璐最终评分计算
SELECT
    '杨璐最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        90 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '杨璐' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 刘伟东最终评分计算
SELECT
    '刘伟东最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        94 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '刘伟东' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 王乐最终评分计算
SELECT
    '王乐最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        90 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '王乐' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- ===== 按精力分配比例加权的项目负责人评分计算 =====

-- 丛聪加权项目负责人评分（已评分91分 + 未评分项目）
SELECT
    '丛聪加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (91 * 0.05 + 90 * 0.10 + 92 * 0.70 + 89 * 0.05 + 90 * 0.10) / (0.05 + 0.10 + 0.70 + 0.05 + 0.10), 2
    ) as weighted_project_leader_score
FROM dual;

-- 刘志杰加权项目负责人评分（已评分89,93,93分 + 未评分项目）
SELECT
    '刘志杰加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (89 * 0.10 + 93 * 0.30 + 94 * 0.05 + 93 * 0.30 + 93 * 0.20 + 92 * 0.05) / (0.10 + 0.30 + 0.05 + 0.30 + 0.20 + 0.05), 2
    ) as weighted_project_leader_score
FROM dual;

-- 师长立加权项目负责人评分
SELECT
    '师长立加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (91 * 0.90 + 89 * 0.10) / (0.90 + 0.10), 2
    ) as weighted_project_leader_score
FROM dual;

-- 杨璐加权项目负责人评分
SELECT
    '杨璐加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    90.0 as weighted_project_leader_score
FROM dual;

-- 刘伟东加权项目负责人评分
SELECT
    '刘伟东加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (95 * 0.70 + 94 * 0.30) / (0.70 + 0.30), 2
    ) as weighted_project_leader_score
FROM dual;

-- 王乐加权项目负责人评分（已评分90分 + 未评分项目）
SELECT
    '王乐加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (90 * 0.40 + 91 * 0.60) / (0.40 + 0.60), 2
    ) as weighted_project_leader_score
FROM dual;

-- ===== 执行完成确认 =====
SELECT
    '执行完成' as status,
    NOW() as completion_time,
    '友好并网与新型配网技术研究所6人的项目负责人评分记录已添加完成' as result,
    '预期最终得分已计算完成' as note;
