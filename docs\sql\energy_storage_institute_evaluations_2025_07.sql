-- ===== 补充新型储能技术研究所人员的评分记录 =====
-- 评价月份：2025-07
-- 基于用户提供的精力分配明细和评分状态生成

-- 用户信息：
-- 王丽杰 - 用户ID: 149, 新型储能技术研究所
-- 陈洪胜 - 用户ID: 150, 新型储能技术研究所
-- 孙坤元 - 用户ID: 151, 新型储能技术研究所
-- 刘帅伟 - 用户ID: 152, 新型储能技术研究所
-- 王泽 - 用户ID: 173, 新型储能技术研究所

-- 项目信息：
-- 项目261: 光热发电系统配置与运行优化研究 - 负责人: 闫敬书(169)
-- 项目263: 源网荷储系统中分布式飞轮集群优化配置与调频控制策略研究 - 负责人: 孙坤元(151)
-- 项目264: 面向源网荷储一体化的边缘感知与轻量级智能管控技术研究与应用 - 负责人: 张宏博(153)
-- 项目265: 基于虚拟传感的储能系统热质传递性能预测技术研究 - 负责人: 朱鸿飞(148)
-- 项目266: 频繁启停的宽负荷动力透平叶片优化设计研究 - 负责人: 高大伟(154)
-- 项目267: 新型脉冲低电耗电解水制氢技术研究 - 负责人: 王丽杰(149)
-- 项目268: MW级高比能宽温域新型混合电容储能模组研制及体系优化 - 负责人: 王丽杰(149)
-- 项目298: 基于全人工地下储气库压缩空气储能的混合储能系统技术研究及示范 - 负责人: 朱鸿飞(148)
-- 项目310: 混合储能状态评估与协同控制技术研究 - 负责人: 李同辉(147)
-- 项目318: 基于全生命周期技术经济性分析的源网荷储场景下多时间尺度储能配置优化研究项目 - 负责人: 陈洪胜(150)

-- ===== 第一步：添加机构负责人评分记录 =====

-- 王丽杰的机构负责人评分（2025-07）
INSERT INTO `project_evaluation` VALUES (7001, 0, 147, 149, 95.00, '2025-07', 'manager', '系统默认评分', NOW(), NOW());

-- 陈洪胜的机构负责人评分（2025-07）
INSERT INTO `project_evaluation` VALUES (7002, 0, 147, 150, 93.00, '2025-07', 'manager', '系统默认评分', NOW(), NOW());

-- 孙坤元的机构负责人评分（2025-07）
INSERT INTO `project_evaluation` VALUES (7003, 0, 147, 151, 94.00, '2025-07', 'manager', '系统默认评分', NOW(), NOW());

-- 刘帅伟的机构负责人评分（2025-07）
INSERT INTO `project_evaluation` VALUES (7004, 0, 147, 152, 96.00, '2025-07', 'manager', '系统默认评分', NOW(), NOW());

-- 王泽的机构负责人评分（2025-07）
INSERT INTO `project_evaluation` VALUES (7005, 0, 147, 173, 92.00, '2025-07', 'manager', '系统默认评分', NOW(), NOW());

-- ===== 第二步：添加项目负责人评分记录 =====

-- ===== 王丽杰（149）的未评分项目 =====

-- 1. 新型脉冲低电耗电解水制氢技术研究（项目267）- 负责人：王丽杰（149）自评
-- 精力分配：30.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7006, 267, 149, 149, 95.00, '2025-07', 'project_leader', '项目负责人自评', NOW(), NOW());

-- 2. MW级高比能宽温域新型混合电容储能模组研制及体系优化（项目268）- 负责人：王丽杰（149）自评
-- 精力分配：50.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7007, 268, 149, 149, 96.00, '2025-07', 'project_leader', '项目负责人自评', NOW(), NOW());

-- 3. 混合储能状态评估与协同控制技术研究（项目310）- 负责人：李同辉（147）
-- 精力分配：9.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7008, 310, 147, 149, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 陈洪胜（150）的未评分项目 =====

-- 1. 频繁启停的宽负荷动力透平叶片优化设计研究（项目266）- 负责人：高大伟（154）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7009, 266, 154, 150, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 基于全生命周期技术经济性分析的源网荷储场景下多时间尺度储能配置优化研究项目（项目318）- 负责人：陈洪胜（150）自评
-- 精力分配：59.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7010, 318, 150, 150, 93.00, '2025-07', 'project_leader', '项目负责人自评', NOW(), NOW());

-- ===== 孙坤元（151）的未评分项目 =====

-- 1. 源网荷储系统中分布式飞轮集群优化配置与调频控制策略研究（项目263）- 负责人：孙坤元（151）自评
-- 精力分配：30.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7011, 263, 151, 151, 94.00, '2025-07', 'project_leader', '项目负责人自评', NOW(), NOW());

-- 2. 面向源网荷储一体化的边缘感知与轻量级智能管控技术研究与应用（项目264）- 负责人：张宏博（153）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7012, 264, 153, 151, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 混合储能状态评估与协同控制技术研究（项目310）- 负责人：李同辉（147）
-- 精力分配：60.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7013, 310, 147, 151, 95.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 刘帅伟（152）的未评分项目 =====

-- 1. 面向源网荷储一体化的边缘感知与轻量级智能管控技术研究与应用（项目264）- 负责人：张宏博（153）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7014, 264, 153, 152, 95.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 混合储能状态评估与协同控制技术研究（项目310）- 负责人：李同辉（147）
-- 精力分配：30.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7015, 310, 147, 152, 96.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 王泽（173）的未评分项目 =====

-- 1. 面向源网荷储一体化的边缘感知与轻量级智能管控技术研究与应用（项目264）- 负责人：张宏博（153）
-- 精力分配：70.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7016, 264, 153, 173, 91.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 混合储能状态评估与协同控制技术研究（项目310）- 负责人：李同辉（147）
-- 精力分配：30.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (7017, 310, 147, 173, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 验证插入的记录 =====
SELECT 
    '新增评分记录验证' as verification_type,
    pe.id,
    CASE 
        WHEN pe.project_id = 0 THEN '机构负责人评分'
        ELSE pi.project_name
    END as project_name,
    su_evaluator.nick_name as evaluator_name,
    su_evaluatee.nick_name as evaluatee_name,
    pe.score,
    pe.evaluation_month,
    pe.evaluation_type,
    pe.comments
FROM project_evaluation pe
LEFT JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE pe.id BETWEEN 7001 AND 7017
ORDER BY pe.id;

-- ===== 统计各人的评分完成情况 =====

-- 王丽杰的评分统计
SELECT 
    '王丽杰评分统计' as person,
    pe.evaluation_type,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '王丽杰'
AND pe.evaluation_month = '2025-07'
GROUP BY pe.evaluation_type;

-- 陈洪胜的评分统计
SELECT 
    '陈洪胜评分统计' as person,
    pe.evaluation_type,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '陈洪胜'
AND pe.evaluation_month = '2025-07'
GROUP BY pe.evaluation_type;

-- 孙坤元的评分统计
SELECT 
    '孙坤元评分统计' as person,
    pe.evaluation_type,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '孙坤元'
AND pe.evaluation_month = '2025-07'
GROUP BY pe.evaluation_type;

-- 刘帅伟的评分统计
SELECT 
    '刘帅伟评分统计' as person,
    pe.evaluation_type,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '刘帅伟'
AND pe.evaluation_month = '2025-07'
GROUP BY pe.evaluation_type;

-- 王泽的评分统计
SELECT
    '王泽评分统计' as person,
    pe.evaluation_type,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '王泽'
AND pe.evaluation_month = '2025-07'
GROUP BY pe.evaluation_type;

-- ===== 最终评分计算 =====

-- 王丽杰最终评分计算
SELECT
    '王丽杰最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        95 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '王丽杰' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 陈洪胜最终评分计算
SELECT
    '陈洪胜最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        93 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '陈洪胜' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 孙坤元最终评分计算
SELECT
    '孙坤元最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        94 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '孙坤元' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 刘帅伟最终评分计算
SELECT
    '刘帅伟最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        96 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '刘帅伟' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 王泽最终评分计算
SELECT
    '王泽最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        92 as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '王泽' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- ===== 按精力分配比例加权的项目负责人评分计算 =====

-- 王丽杰加权项目负责人评分（包含已评分项目）
SELECT
    '王丽杰加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (92 * 0.10 + 95 * 0.30 + 96 * 0.50 + 98 * 0.01 + 94 * 0.09) / (0.10 + 0.30 + 0.50 + 0.01 + 0.09), 2
    ) as weighted_project_leader_score
FROM dual;

-- 陈洪胜加权项目负责人评分（包含已评分项目）
SELECT
    '陈洪胜加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (80 * 0.10 + 91 * 0.10 + 92 * 0.20 + 91 * 0.01 + 93 * 0.59) / (0.10 + 0.10 + 0.20 + 0.01 + 0.59), 2
    ) as weighted_project_leader_score
FROM dual;

-- 孙坤元加权项目负责人评分
SELECT
    '孙坤元加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (94 * 0.30 + 92 * 0.10 + 95 * 0.60) / (0.30 + 0.10 + 0.60), 2
    ) as weighted_project_leader_score
FROM dual;

-- 刘帅伟加权项目负责人评分（包含已评分项目）
SELECT
    '刘帅伟加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (92 * 0.10 + 95 * 0.20 + 94 * 0.10 + 93 * 0.30 + 96 * 0.30) / (0.10 + 0.20 + 0.10 + 0.30 + 0.30), 2
    ) as weighted_project_leader_score
FROM dual;

-- 王泽加权项目负责人评分
SELECT
    '王泽加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (91 * 0.70 + 92 * 0.30) / (0.70 + 0.30), 2
    ) as weighted_project_leader_score
FROM dual;

-- ===== 新型储能技术研究所评分汇总 =====
SELECT
    '新型储能技术研究所评分汇总' as summary_type,
    su.nick_name as person_name,
    COUNT(CASE WHEN pe.evaluation_type = 'manager' THEN 1 END) as manager_evaluations,
    COUNT(CASE WHEN pe.evaluation_type = 'project_leader' THEN 1 END) as project_leader_evaluations,
    AVG(CASE WHEN pe.evaluation_type = 'manager' THEN pe.score END) as avg_manager_score,
    AVG(CASE WHEN pe.evaluation_type = 'project_leader' THEN pe.score END) as avg_project_leader_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name IN ('王丽杰', '陈洪胜', '孙坤元', '刘帅伟', '王泽')
AND pe.evaluation_month = '2025-07'
GROUP BY su.nick_name
ORDER BY su.nick_name;

-- ===== 执行完成确认 =====
SELECT
    '执行完成' as status,
    NOW() as completion_time,
    '新型储能技术研究所5人的评分记录已添加完成' as result,
    '所有人员均包含机构负责人评分和项目负责人评分' as note;
