package com.kc.system.mapper;

import java.util.List;
import java.util.Map;
import com.kc.system.domain.ProjectInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 项目基础信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-14
 */
public interface ProjectInfoMapper 
{
    /**
     * 查询项目基础信息
     * 
     * @param id 项目基础信息主键
     * @return 项目基础信息
     */
    public ProjectInfo selectProjectInfoById(Long id);

    /**
     * 查询项目基础信息列表
     * 
     * @param projectInfo 项目基础信息
     * @return 项目基础信息集合
     */
    public List<ProjectInfo> selectProjectInfoList(ProjectInfo projectInfo);

    /**
     * 新增项目基础信息
     * 
     * @param projectInfo 项目基础信息
     * @return 结果
     */
    public int insertProjectInfo(ProjectInfo projectInfo);

    /**
     * 修改项目基础信息
     * 
     * @param projectInfo 项目基础信息
     * @return 结果
     */
    public int updateProjectInfo(ProjectInfo projectInfo);

    /**
     * 删除项目基础信息
     * 
     * @param id 项目基础信息主键
     * @return 结果
     */
    public int deleteProjectInfoById(Long id);

    /**
     * 批量删除项目基础信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectInfoByIds(Long[] ids);

    /**
     * 查询当前用户负责的项目列表
     * 
     * @param userName 用户名
     * @return 项目列表
     */
    public List<ProjectInfo> selectUserLeadProjectList(String userName);

    /**
     * 查询用户所有项目工时
     */
    public List<Map<String, Object>> selectUserProjectWorkloads(
        @Param("userName") String userName, 
        @Param("month") String month
    );

    /**
     * 根据项目名称查询项目信息
     *
     * @param projectName 项目名称
     * @return 项目信息
     */
    public ProjectInfo selectProjectInfoByName(String projectName);

    /**
     * 根据项目名称和部门ID查询项目信息
     *
     * @param projectName 项目名称
     * @param deptId 部门ID
     * @return 项目信息
     */
    public ProjectInfo selectProjectInfoByNameAndDept(@Param("projectName") String projectName, @Param("deptId") Long deptId);

    /**
     * 根据部门ID查询项目列表
     * 
     * @param deptId 部门ID
     * @return 项目列表
     */
    public List<ProjectInfo> selectProjectsByDeptId(Long deptId);
}
