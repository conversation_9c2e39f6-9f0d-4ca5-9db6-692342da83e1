package com.kc.system.strategy;

import com.kc.system.domain.EvaluationResult;
import com.kc.system.dto.ScoreCalculationContext;
import java.math.BigDecimal;
import java.util.List;

/**
 * 评分计算策略接口
 * 
 * <AUTHOR>
 */
public interface ScoreCalculationStrategy {
    
    /**
     * 计算最终评分
     * 
     * @param context 评分计算上下文
     * @return 最终评分
     */
    BigDecimal calculateScore(ScoreCalculationContext context);
    
    /**
     * 获取策略类型
     * 
     * @return 策略类型
     */
    String getStrategyType();
    
    /**
     * 验证是否适用于当前场景
     * 
     * @param context 评分计算上下文
     * @return 是否适用
     */
    boolean isApplicable(ScoreCalculationContext context);
    
    /**
     * 获取计算详情说明
     * 
     * @param context 评分计算上下文
     * @return 计算详情
     */
    String getCalculationDetails(ScoreCalculationContext context);
}
