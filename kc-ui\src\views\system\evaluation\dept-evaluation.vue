<template>
  <div class="app-container">
    <!-- 顶部搜索区域 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
        <el-form-item label="评价月份" prop="evaluationMonth">
          <el-date-picker
            v-model="queryParams.evaluationMonth"
            type="month"
            value-format="yyyy-MM"
            placeholder="请选择评价月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <!-- <el-button type="success" icon="el-icon-refresh-right" size="mini" @click="refreshAllData">刷新数据</el-button> -->
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 内容区域 -->
    <div class="content-container">
      <!-- 标签页 -->
      <el-card>
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <!-- 评分标签页 -->
          <el-tab-pane label="员工评分" name="evaluation">
            <!-- 月度/季度评价子标签页 -->
            <el-tabs v-model="activeEvaluationTab" @tab-click="handleEvaluationTabClick" class="evaluation-sub-tabs">
              <!-- 月度评价子标签页 -->
              <el-tab-pane label="月度评价" name="monthly">
                <!-- 部门选项卡 -->
            <el-card class="dept-tabs-card" shadow="never">
              <el-tabs
                v-model="activeDeptTab"
                type="card"
                @tab-click="handleDeptTabClick"
                class="dept-tabs">

                <!-- 各部门选项卡 -->
                <el-tab-pane
                  v-for="deptGroup in departmentGroups"
                  :key="deptGroup.deptId"
                  :label="deptGroup.deptName"
                  :name="deptGroup.deptId.toString()">

                  <!-- 部门成员列表 -->
                  <div class="member-card" v-loading="loading">
                    <div class="tab-header">
                      <span>{{ deptGroup.deptName }} - 成员列表 ({{ deptGroup.users.length }}人)</span>
                      <span class="header-month">评价月份: {{ queryParams.evaluationMonth || '未选择' }}</span>
                    </div>

                    <div v-if="!deptGroup.users || deptGroup.users.length === 0" class="placeholder-info">
                      该部门暂无成员或成员数据加载中...
                    </div>
                    <div v-else>
                      <!-- <el-alert
                        title="提示：直接对每个人评分，完成后点击提交按钮保存所有评分"
                        type="info"
                        :closable="false"
                        show-icon
                        style="margin-bottom: 15px;">
                      </el-alert> -->

                      <!-- 建议分值区间提示 -->
                      <el-alert
                        title="建议分值区间：非常满意：95分以上；比较满意：85-94分；满意：75-84分；不满意：74分及以下"
                        type="success"
                        :closable="false"
                        show-icon
                        style="margin-bottom: 15px;">
                      </el-alert>

                      <!-- 表格和配额信息的容器 -->
                      <div class="table-with-quota-container">
                        <!-- 左侧表格区域 -->
                        <div class="table-area">
                          <el-table
                        :data="deptGroup.users"
                        border
                        style="width: 100%"
                        row-key="userId"
                        @expand-change="handleExpandChange">
                  <el-table-column type="expand">
                    <template slot-scope="props">
                      <el-form label-position="left" inline class="project-expand-form">
                        <div v-if="props.row.loading" class="loading-data">
                          <i class="el-icon-loading"></i> 正在加载项目数据...
                        </div>
                        <div v-else-if="props.row.projectEvaluations && props.row.projectEvaluations.length > 0">
                          <!-- 最终评分显示区域 -->
                          <div class="final-score-section" v-if="props.row.finalScoreInfo">
                            <div class="final-score-header">
                              <span class="final-score-title">最终评分结果</span>
                              <el-button
                                type="text"
                                size="mini"
                                icon="el-icon-refresh"
                                @click="refreshFinalScore(props.row)"
                                :loading="props.row.scoreLoading">
                                刷新
                              </el-button>
                            </div>
                            <div class="final-score-content">
                              <div class="score-item">
                                <span class="score-label">最终得分:</span>
                                <span class="score-value final" :class="getScoreClass(props.row.finalScoreInfo.finalScore)">
                                  {{ props.row.finalScoreInfo.finalScore || '未计算' }}
                                </span>
                              </div>
                              <div class="score-item">
                                <span class="score-label">机构负责人评分:</span>
                                <span class="score-value">{{ props.row.finalScoreInfo.managerScore || '未评分' }}</span>
                              </div>
                              <div class="score-item">
                                <span class="score-label">项目负责人平均分:</span>
                                <span class="score-value">{{ props.row.finalScoreInfo.projectLeaderScore || '未评分' }}</span>
                              </div>
                            </div>
                          </div>

                          <div class="project-header">
                            <span class="project-title">项目精力分配明细 ({{props.row.projectEvaluations.length}}个项目)</span>
                          </div>

                          <div v-for="(item, index) in props.row.projectEvaluations" :key="index" class="project-item">
                            <el-form-item label="项目:">
                              <span>{{ item.projectName || '项目-' + item.projectId || '未知项目' }}</span>
                            </el-form-item>
                            <el-form-item label="精力分配:">
                              <span class="effort-value">{{ (item.participationRate * 100).toFixed(1) }}%</span>
                            </el-form-item>
                            <el-form-item label="评分状态:">
                              <span class="evaluation-status" :class="{
                                'status-evaluated': item.hasEvaluation,
                                'status-leader': item.role === '负责人',
                                'status-pending': !item.hasEvaluation && item.role !== '负责人'
                              }">
                                {{ item.evaluationStatus }}
                                <span v-if="item.hasEvaluation && item.score !== null" class="score-display">
                                  ({{ item.score }}分)
                                </span>
                              </span>
                            </el-form-item>
                          </div>
                          <!-- <div class="refresh-action">
                            <el-button
                              type="text"
                              size="mini"
                              icon="el-icon-refresh"
                              @click="refreshUserProjectData(props.row)">
                              刷新数据
                            </el-button>
                          </div> -->
                        </div>
                        <div v-else class="no-data">
                          <!-- 最终评分显示区域 -->
                          <div class="final-score-section" v-if="props.row.finalScoreInfo">
                            <div class="final-score-header">
                              <span class="final-score-title">最终评分结果</span>
                              <el-button
                                type="text"
                                size="mini"
                                icon="el-icon-refresh"
                                @click="refreshFinalScore(props.row)"
                                :loading="props.row.scoreLoading">
                                刷新
                              </el-button>
                            </div>
                            <div class="final-score-content">
                              <div class="score-item">
                                <span class="score-label">最终得分:</span>
                                <span class="score-value final" :class="getScoreClass(props.row.finalScoreInfo.finalScore)">
                                  {{ props.row.finalScoreInfo.finalScore || '未计算' }}
                                </span>
                              </div>
                              <div class="score-item">
                                <span class="score-label">机构负责人评分:</span>
                                <span class="score-value">{{ props.row.finalScoreInfo.managerScore || '未评分' }}</span>
                              </div>
                              <div class="score-item">
                                <span class="score-label">项目负责人平均分:</span>
                                <span class="score-value">{{ props.row.finalScoreInfo.projectLeaderScore || '未评分' }}</span>
                              </div>
                            </div>
                          </div>

                          <div class="no-project-info">
                            <i class="el-icon-warning-outline"></i> 该成员暂无参与项目或项目评分数据
                          </div>
                        </div>
                      </el-form>
                    </template>
                  </el-table-column>
                  <el-table-column label="序号" type="index" width="50" align="center" />
                  <el-table-column label="成员姓名" prop="nickName" min-width="120" align="center">
                    <template slot-scope="scope">
                      {{ scope.row.nickName || scope.row.userName }}
                     
                    </template>
                  </el-table-column>
                  <el-table-column label="所属部门" prop="dept.deptName" min-width="150" align="center">
                    <template slot-scope="scope">
                      {{ scope.row.dept ? scope.row.dept.deptName : '' }}
                    </template>
                  </el-table-column>
                  <el-table-column label="评分" min-width="280" align="center">
                    <template slot-scope="scope">
                      <div class="score-input-container">
                        <el-tooltip
                          v-if="scope.row.scoreInputDisabled && scope.row.disabledTooltip"
                          placement="top"
                          :open-delay="300"
                          popper-class="disabled-score-tooltip">
                          <div slot="content">
                            <div v-html="scope.row.disabledTooltip.replace(/\n/g, '<br>')"></div>
                          </div>
                          <el-input-number
                            v-model="scope.row.newScore"
                            :min="0"
                            :max="100"
                            :precision="0"
                            :disabled="scope.row.scoreInputDisabled"
                            @change="handleScoreChange(scope.row)"
                            size="small"
                            controls-position="right"
                            :class="{ 'high-score': scope.row.newScore >= 95, 'disabled-input': scope.row.scoreInputDisabled }">
                          </el-input-number>
                        </el-tooltip>
                        <el-input-number
                          v-else
                          v-model="scope.row.newScore"
                          :min="0"
                          :max="100"
                          :precision="0"
                          :disabled="scope.row.scoreInputDisabled"
                          @change="handleScoreChange(scope.row)"
                          size="small"
                          controls-position="right"
                          :class="{ 'high-score': scope.row.newScore >= 95, 'disabled-input': scope.row.scoreInputDisabled }">
                        </el-input-number>
                        <span class="score-unit">分</span>
                        <span v-if="scope.row.newScore >= 95" class="high-score-tip">
                          <i class="el-icon-warning"></i> 高分
                        </span>
                      </div>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="评价意见" min-width="200" align="center">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="primary"
                        @click="openCommentsDialog(scope.row)">填写评价</el-button>
                    </template>
                  </el-table-column> -->
                          </el-table>

                          <div class="table-footer">
                            <el-button
                              type="primary"
                              @click="batchSaveEvaluations">提交评分</el-button>
                            <!-- <el-button
                              type="info"
                              @click="refreshAllUsersFinalScore"
                              :loading="loading">刷新最终评分</el-button> -->
                            <el-button
                              type="success"
                              @click="exportDeptData(deptGroup)"
                              icon="el-icon-download">导出数据</el-button>
                          </div>
                        </div>

                        <!-- 右侧配额信息竖条 -->
                        <div class="quota-sidebar" v-if="deptGroup.quotaInfo && !isSubDeptLeader()">
                          <div class="quota-bar-container">
                            <div class="quota-bar-header">
                              <span class="quota-title">高分配额状态</span>
                              <!-- <div v-if="deptGroup.quotaInfo.isQuotaGroup" class="quota-group-tag">
                                <el-tag type="info" size="mini">配额组</el-tag>
                              </div> -->
                            </div>

                            <div class="quota-bar-items">
                              <div class="quota-bar-item">
                                <div class="quota-bar-label">总配额</div>
                                <div class="quota-bar-value primary">{{ deptGroup.quotaInfo.highScoreQuota || 0 }}</div>
                              </div>

                              <div class="quota-bar-item">
                                <div class="quota-bar-label">已使用</div>
                                <div class="quota-bar-value used">{{ deptGroup.quotaInfo.usedQuota || 0 }}</div>
                              </div>

                              <div class="quota-bar-item">
                                <div class="quota-bar-label">剩余</div>
                                <div class="quota-bar-value" :class="{ 'warning': deptGroup.quotaInfo.remainingQuota <= 0, 'success': deptGroup.quotaInfo.remainingQuota > 0 }">
                                  {{ deptGroup.quotaInfo.remainingQuota || 0 }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-card>
              </el-tab-pane>

              <!-- 季度评价子标签页 -->
              <el-tab-pane label="季度评价" name="quarterly">
                <!-- 季度评价部门选项卡 -->
                <el-card class="dept-tabs-card" shadow="never">
                  <el-tabs
                    v-model="activeQuarterlyDeptTab"
                    type="card"
                    @tab-click="handleQuarterlyDeptTabClick"
                    class="dept-tabs">

                    <!-- 各部门选项卡（季度评价） -->
                    <el-tab-pane
                      v-for="deptGroup in quarterlyDepartmentGroups"
                      :key="deptGroup.deptId"
                      :label="deptGroup.deptName"
                      :name="deptGroup.deptId.toString()">

                      <!-- 部门成员列表（季度评价） -->
                      <div class="member-card" v-loading="quarterlyLoading">
                        <div class="tab-header">
                          <span>{{ deptGroup.deptName }} - 成员列表 ({{ deptGroup.users.length }}人)</span>
                        </div>

                        <div v-if="!deptGroup.users || deptGroup.users.length === 0" class="placeholder-info">
                          该部门暂无成员或成员数据加载中...
                        </div>
                        <div v-else>
                          <!-- 建议分值区间提示 -->
                          <el-alert
                            title="建议分值区间：非常满意：95分以上；比较满意：85-94分；满意：75-84分；不满意：74分及以下"
                            type="success"
                            :closable="false"
                            show-icon
                            style="margin-bottom: 15px;">
                          </el-alert>

                          <!-- 表格和配额信息的容器 -->
                          <div class="table-with-quota-container">
                            <!-- 左侧表格区域 -->
                            <div class="table-area">
                              <el-table
                                :data="deptGroup.users"
                                border
                                style="width: 100%"
                                row-key="userId"
                                @expand-change="handleQuarterlyExpandChange">
                                <!-- 季度评价时禁用展开功能 -->
                                <el-table-column type="expand" v-if="false">
                                  <template slot-scope="props">
                                    <el-form label-position="left" inline class="project-expand-form">
                                      <div v-if="props.row.loading" class="loading-data">
                                        <i class="el-icon-loading"></i> 正在加载项目数据...
                                      </div>
                                      <div v-else-if="props.row.quarterlyProjectEvaluations && props.row.quarterlyProjectEvaluations.length > 0">
                                        <!-- 最终评分显示区域 -->
                                        <div class="final-score-section" v-if="props.row.quarterlyFinalScoreInfo">
                                          <div class="final-score-header">
                                            <span class="final-score-title">最终评分结果</span>
                                            <el-button
                                              type="text"
                                              size="mini"
                                              icon="el-icon-refresh"
                                              @click="refreshQuarterlyFinalScore(props.row)"
                                              :loading="props.row.quarterlyScoreLoading">
                                              刷新
                                            </el-button>
                                          </div>
                                          <div class="final-score-content">
                                            <div class="score-item">
                                              <span class="score-label">最终得分:</span>
                                              <span class="score-value final" :class="getScoreClass(props.row.quarterlyFinalScoreInfo.finalScore)">
                                                {{ props.row.quarterlyFinalScoreInfo.finalScore || '未计算' }}
                                              </span>
                                            </div>
                                            <div class="score-item">
                                              <span class="score-label">机构负责人评分:</span>
                                              <span class="score-value">{{ props.row.quarterlyFinalScoreInfo.managerScore || '未评分' }}</span>
                                            </div>
                                            <div class="score-item">
                                              <span class="score-label">项目负责人平均分:</span>
                                              <span class="score-value">{{ props.row.quarterlyFinalScoreInfo.projectLeaderScore || '未评分' }}</span>
                                            </div>
                                          </div>
                                        </div>

                                        <div class="project-header">
                                          <span class="project-title">项目精力分配明细 ({{props.row.quarterlyProjectEvaluations.length}}个项目)</span>
                                        </div>

                                        <div v-for="(item, index) in props.row.quarterlyProjectEvaluations" :key="index" class="project-item">
                                          <el-form-item label="项目:">
                                            <span>{{ item.projectName || '项目-' + item.projectId || '未知项目' }}</span>
                                          </el-form-item>
                                          <el-form-item label="精力分配:">
                                            <span class="effort-value">{{ (item.participationRate * 100).toFixed(1) }}%</span>
                                          </el-form-item>
                                          <el-form-item label="评分状态:">
                                            <span class="evaluation-status" :class="{
                                              'status-evaluated': item.hasEvaluation,
                                              'status-leader': item.role === '负责人',
                                              'status-pending': !item.hasEvaluation && item.role !== '负责人'
                                            }">
                                              {{ item.evaluationStatus }}
                                              <span v-if="item.hasEvaluation && item.score !== null" class="score-display">
                                                ({{ item.score }}分)
                                              </span>
                                            </span>
                                          </el-form-item>
                                        </div>
                                      </div>
                                      <div v-else class="no-data">
                                        <!-- 最终评分显示区域 -->
                                        <div class="final-score-section" v-if="props.row.quarterlyFinalScoreInfo">
                                          <div class="final-score-header">
                                            <span class="final-score-title">最终评分结果</span>
                                            <el-button
                                              type="text"
                                              size="mini"
                                              icon="el-icon-refresh"
                                              @click="refreshQuarterlyFinalScore(props.row)"
                                              :loading="props.row.quarterlyScoreLoading">
                                              刷新
                                            </el-button>
                                          </div>
                                          <div class="final-score-content">
                                            <div class="score-item">
                                              <span class="score-label">最终得分:</span>
                                              <span class="score-value final" :class="getScoreClass(props.row.quarterlyFinalScoreInfo.finalScore)">
                                                {{ props.row.quarterlyFinalScoreInfo.finalScore || '未计算' }}
                                              </span>
                                            </div>
                                            <div class="score-item">
                                              <span class="score-label">机构负责人评分:</span>
                                              <span class="score-value">{{ props.row.quarterlyFinalScoreInfo.managerScore || '未评分' }}</span>
                                            </div>
                                            <div class="score-item">
                                              <span class="score-label">项目负责人平均分:</span>
                                              <span class="score-value">{{ props.row.quarterlyFinalScoreInfo.projectLeaderScore || '未评分' }}</span>
                                            </div>
                                          </div>
                                        </div>

                                        <div class="no-project-info">
                                          <i class="el-icon-warning-outline"></i> 该成员暂无参与项目或项目评分数据
                                        </div>
                                      </div>
                                    </el-form>
                                  </template>
                                </el-table-column>
                                <el-table-column label="序号" type="index" width="50" align="center" />
                                <el-table-column label="成员姓名" prop="nickName" min-width="120" align="center">
                                  <template slot-scope="scope">
                                    {{ scope.row.nickName || scope.row.userName }}
                                  </template>
                                </el-table-column>
                                <el-table-column label="所属部门" prop="dept.deptName" min-width="150" align="center">
                                  <template slot-scope="scope">
                                    {{ scope.row.dept ? scope.row.dept.deptName : '' }}
                                  </template>
                                </el-table-column>
                                <el-table-column label="评分" min-width="280" align="center">
                                  <template slot-scope="scope">
                                    <div class="score-input-container">
                                      <el-tooltip
                                        v-if="scope.row.quarterlyScoreInputDisabled && scope.row.quarterlyDisabledTooltip"
                                        placement="top"
                                        :open-delay="300"
                                        popper-class="disabled-score-tooltip">
                                        <div slot="content">
                                          <div v-html="scope.row.quarterlyDisabledTooltip.replace(/\\n/g, '<br>')"></div>
                                        </div>
                                        <el-input-number
                                          v-model="scope.row.quarterlyNewScore"
                                          :min="0"
                                          :max="100"
                                          :precision="0"
                                          :disabled="scope.row.quarterlyScoreInputDisabled"
                                          @change="handleQuarterlyScoreChange(scope.row)"
                                          size="small"
                                          controls-position="right"
                                          :class="{ 'high-score': scope.row.quarterlyNewScore >= 95, 'disabled-input': scope.row.quarterlyScoreInputDisabled }">
                                        </el-input-number>
                                      </el-tooltip>
                                      <el-input-number
                                        v-else
                                        v-model="scope.row.quarterlyNewScore"
                                        :min="0"
                                        :max="100"
                                        :precision="0"
                                        :disabled="scope.row.quarterlyScoreInputDisabled"
                                        @change="handleQuarterlyScoreChange(scope.row)"
                                        size="small"
                                        controls-position="right"
                                        :class="{ 'high-score': scope.row.quarterlyNewScore >= 95, 'disabled-input': scope.row.quarterlyScoreInputDisabled }">
                                      </el-input-number>
                                      <span class="score-unit">分</span>
                                      <span v-if="scope.row.quarterlyNewScore >= 95" class="high-score-tip">
                                        <i class="el-icon-warning"></i> 高分
                                      </span>
                                    </div>
                                  </template>
                                </el-table-column>
                              </el-table>

                              <div class="table-footer">
                                <el-button
                                  type="primary"
                                  @click="batchSaveQuarterlyEvaluations">提交评分</el-button>
                                <el-button
                                  type="success"
                                  @click="exportQuarterlyDeptData(deptGroup)"
                                  icon="el-icon-download">导出数据</el-button>
                              </div>
                            </div>

                            <!-- 右侧配额信息竖条 -->
                            <div class="quota-sidebar" v-if="deptGroup.quotaInfo && !isSubDeptLeader()">
                              <div class="quota-bar-container">
                                <div class="quota-bar-header">
                                  <span class="quota-title">高分配额状态</span>
                                </div>

                                <div class="quota-bar-items">
                                  <div class="quota-bar-item">
                                    <div class="quota-bar-label">总配额</div>
                                    <div class="quota-bar-value primary">{{ deptGroup.quotaInfo.highScoreQuota || 0 }}</div>
                                  </div>

                                  <div class="quota-bar-item">
                                    <div class="quota-bar-label">已使用</div>
                                    <div class="quota-bar-value used">{{ deptGroup.quotaInfo.usedQuota || 0 }}</div>
                                  </div>

                                  <div class="quota-bar-item">
                                    <div class="quota-bar-label">剩余</div>
                                    <div class="quota-bar-value" :class="{ 'warning': deptGroup.quotaInfo.remainingQuota <= 0, 'success': deptGroup.quotaInfo.remainingQuota > 0 }">
                                      {{ deptGroup.quotaInfo.remainingQuota || 0 }}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </el-card>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>

          <!-- 奖金分配标签页 -->
          <el-tab-pane label="奖金分配" name="bonus" v-if="!isSubDeptLeader()">
            <!-- 月度/季度奖金分配子标签页 -->
            <el-tabs v-model="activeBonusTab" @tab-click="handleBonusTabClick" class="evaluation-sub-tabs">
              <!-- 月度奖金分配子标签页 -->
              <el-tab-pane label="月度奖金分配" name="monthly">
                <div class="bonus-card" v-loading="bonusLoading">
                  <div class="tab-header">
                    <span>{{ currentDeptName || '当前部门' }} - 奖金分配</span>
                    <span class="header-month">分配月份: {{ queryParams.evaluationMonth || '未选择' }}</span>
                  </div>

                  <!-- 奖金分配状态信息 -->
                  <div v-if="deptBonusStatusList.length === 0" style="margin-bottom: 15px;">
                    <el-alert
                      title="该月份尚未进行部门奖金分配，请联系薪酬考核人员先进行部门奖金分配"
                      type="warning"
                      :closable="false"
                      show-icon>
                    </el-alert>
                  </div>

                  <!-- 按部门显示奖金分配状态 -->
                  <div v-else style="margin-bottom: 15px;">
                    <div v-for="deptStatus in deptBonusStatusList" :key="deptStatus.deptId" style="margin-bottom: 10px;">
                      <el-alert
                        :title="`${deptStatus.deptName || '部门'}: 总奖金 ${formatMoney(deptStatus.totalBonus)} | 已分配 ${formatMoney(deptStatus.allocatedBonus)} | 剩余 ${formatMoney(deptStatus.remainingBonus)}`"
                        :type="deptStatus.remainingBonus >= 0 ? 'success' : 'warning'"
                        :closable="false"
                        show-icon>
                      </el-alert>
                    </div>
                  </div>

                  <!-- 员工奖金分配表格 -->
                  <div v-if="hasAnyBonusAllocation">
                    <!-- 无员工可分配提示 -->
                    <div v-if="bonusEmployees.length === 0" class="no-data">
                      <i class="el-icon-info"></i>
                      <p>当前负责的部门中，只有奖金金额为0的部门，无员工可进行奖金分配</p>
                    </div>

                    <!-- 表格和奖金信息的容器 -->
                    <div v-else class="table-with-bonus-container">
                      <!-- 左侧表格区域 -->
                      <div class="table-area">
                        <el-table
                          :data="bonusEmployees"
                          border
                          style="width: 100%"
                          row-key="userId">
                          <el-table-column label="序号" type="index" width="50" align="center" />
                          <el-table-column label="员工姓名" prop="nickName" min-width="120" align="center">
                            <template slot-scope="scope">
                              {{ scope.row.nickName || scope.row.userName }}
                            </template>
                          </el-table-column>
                          <el-table-column label="员工编号" prop="userName" min-width="100" align="center" />
                          <el-table-column label="所属部门" prop="deptName" min-width="120" align="center">
                            <template slot-scope="scope">
                              {{ scope.row.deptName || (scope.row.dept && scope.row.dept.deptName) || '' }}
                            </template>
                          </el-table-column>

                          <el-table-column label="分配奖金" min-width="150" align="center">
                            <template slot-scope="scope">
                              <el-input-number
                                v-model="scope.row.bonusAmount"
                                :precision="2"
                                :step="100"
                                :min="bonusStatus.totalBonus < 0 ? bonusStatus.totalBonus : 0"
                                :max="bonusStatus.totalBonus > 0 ? bonusStatus.totalBonus : 0"
                                @change="handleBonusChange"
                                size="small"
                                controls-position="right"
                                style="width: 130px">
                              </el-input-number>
                              <span class="bonus-unit">元</span>
                            </template>
                          </el-table-column>
                        </el-table>

                        <div v-if="bonusEmployees.length > 0" class="table-footer">
                          <el-button
                            type="primary"
                            @click="submitBonusAllocation"
                            :disabled="!canSubmitBonus">提交奖金分配</el-button>
                          <el-button
                            type="info"
                            @click="resetBonusAllocation">重置</el-button>
                        </div>
                      </div>

                      <!-- 右侧奖金信息竖条 -->
                      <div class="bonus-sidebar">
                        <div class="bonus-bar-container">
                          <div class="bonus-bar-header">
                            <span class="bonus-title">奖金分配状态</span>
                          </div>

                          <div class="bonus-bar-items">
                            <div class="bonus-bar-item">
                              <div class="bonus-bar-label">总可分配</div>
                              <div class="bonus-bar-value primary">{{ formatMoney(totalAvailableAmount) }}</div>
                            </div>

                            <div class="bonus-bar-item">
                              <div class="bonus-bar-label">已分配</div>
                              <div class="bonus-bar-value used">{{ formatMoney(totalAllocatedAmount) }}</div>
                            </div>

                            <div class="bonus-bar-item">
                              <div class="bonus-bar-label">剩余</div>
                              <div class="bonus-bar-value" :class="{ 'warning': remainingAmount < 0, 'success': remainingAmount >= 0 }">
                                {{ formatMoney(remainingAmount) }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 季度奖金分配子标签页 -->
              <el-tab-pane label="季度奖金分配" name="quarterly">
                <div class="bonus-card" v-loading="quarterlyBonusLoading">
                  <div class="tab-header">
                    <span>{{ currentDeptName || '当前部门' }} - 季度奖金分配</span>
                  </div>

                  <!-- 季度奖金分配状态信息 -->
                  <div v-if="quarterlyDeptBonusStatusList.length === 0" style="margin-bottom: 15px;">
                    <el-alert
                      title="该月份尚未进行部门奖金分配，请联系薪酬考核人员先进行部门奖金分配"
                      type="warning"
                      :closable="false"
                      show-icon>
                    </el-alert>
                  </div>

                  <!-- 按部门显示季度奖金分配状态 -->
                  <div v-else style="margin-bottom: 15px;">
                    <div v-for="deptStatus in quarterlyDeptBonusStatusList" :key="deptStatus.deptId" style="margin-bottom: 10px;">
                      <el-alert
                        :title="`${deptStatus.deptName || '部门'}: 总奖金 ${formatMoney(deptStatus.totalBonus)} | 已分配 ${formatMoney(deptStatus.allocatedBonus)} | 剩余 ${formatMoney(deptStatus.remainingBonus)}`"
                        :type="deptStatus.remainingBonus >= 0 ? 'success' : 'warning'"
                        :closable="false"
                        show-icon>
                      </el-alert>
                    </div>
                  </div>

                  <!-- 季度员工奖金分配表格 -->
                  <div v-if="hasAnyQuarterlyBonusAllocation">
                    <!-- 无员工可分配提示 -->
                    <div v-if="quarterlyBonusEmployees.length === 0" class="no-data">
                      <i class="el-icon-info"></i>
                      <p>当前负责的部门中，只有奖金金额为0的部门，无员工可进行奖金分配</p>
                    </div>

                    <!-- 表格和奖金信息的容器 -->
                    <div v-else class="table-with-bonus-container">
                      <!-- 左侧表格区域 -->
                      <div class="table-area">
                        <el-table
                          :data="quarterlyBonusEmployees"
                          border
                          style="width: 100%"
                          row-key="userId">
                          <el-table-column label="序号" type="index" width="50" align="center" />
                          <el-table-column label="员工姓名" prop="nickName" min-width="120" align="center">
                            <template slot-scope="scope">
                              {{ scope.row.nickName || scope.row.userName }}
                            </template>
                          </el-table-column>
                          <el-table-column label="员工编号" prop="userName" min-width="100" align="center" />
                          <el-table-column label="所属部门" prop="deptName" min-width="120" align="center">
                            <template slot-scope="scope">
                              {{ scope.row.deptName || (scope.row.dept && scope.row.dept.deptName) || '' }}
                            </template>
                          </el-table-column>

                          <el-table-column label="分配奖金" min-width="150" align="center">
                            <template slot-scope="scope">
                              <el-input-number
                                v-model="scope.row.quarterlyBonusAmount"
                                :precision="2"
                                :step="100"
                                :min="quarterlyBonusStatus.totalBonus < 0 ? quarterlyBonusStatus.totalBonus : 0"
                                :max="quarterlyBonusStatus.totalBonus > 0 ? quarterlyBonusStatus.totalBonus : 0"
                                @change="handleQuarterlyBonusChange"
                                size="small"
                                controls-position="right"
                                style="width: 130px">
                              </el-input-number>
                              <span class="bonus-unit">元</span>
                            </template>
                          </el-table-column>
                        </el-table>

                        <div v-if="quarterlyBonusEmployees.length > 0" class="table-footer">
                          <el-button
                            type="primary"
                            @click="submitQuarterlyBonusAllocation"
                            :disabled="!canSubmitQuarterlyBonus">提交季度奖金分配</el-button>
                          <el-button
                            type="info"
                            @click="resetQuarterlyBonusAllocation">重置</el-button>
                        </div>
                      </div>

                      <!-- 右侧奖金信息竖条 -->
                      <div class="bonus-sidebar">
                        <div class="bonus-bar-container">
                          <div class="bonus-bar-header">
                            <span class="bonus-title">季度奖金分配状态</span>
                          </div>

                          <div class="bonus-bar-items">
                            <div class="bonus-bar-item">
                              <div class="bonus-bar-label">总可分配</div>
                              <div class="bonus-bar-value primary">{{ formatMoney(quarterlyTotalAvailableAmount) }}</div>
                            </div>

                            <div class="bonus-bar-item">
                              <div class="bonus-bar-label">已分配</div>
                              <div class="bonus-bar-value used">{{ formatMoney(quarterlyTotalAllocatedAmount) }}</div>
                            </div>

                            <div class="bonus-bar-item">
                              <div class="bonus-bar-label">剩余</div>
                              <div class="bonus-bar-value" :class="{ 'warning': quarterlyRemainingAmount < 0, 'success': quarterlyRemainingAmount >= 0 }">
                                {{ formatMoney(quarterlyRemainingAmount) }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>

    <!-- 评价对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="被评价人">
          <span>{{ form.userName }}</span>
        </el-form-item>
        <el-form-item label="评价月份">
          <span>{{ form.evaluationMonth }}</span>
        </el-form-item>
        <el-form-item label="评分" prop="score">
          <el-input-number
            v-model="form.score"
            :min="0"
            :max="100"
            controls-position="right">
          </el-input-number>
          <span class="score-unit">分 (0-100)</span>
        </el-form-item>
        <el-form-item label="评价意见" prop="comments">
          <el-input
            type="textarea"
            v-model="form.comments"
            placeholder="请输入评价意见"
            maxlength="500"
            show-word-limit
            :rows="4">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 验证错误弹框 -->
    <el-dialog
      title="评分验证失败"
      :visible.sync="validationErrorDialog"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="validationErrorData.length > 0">
        <p style="margin-bottom: 15px; color: #E6A23C; font-weight: bold;">
          <i class="el-icon-warning"></i>
          以下员工无法进行评分，请先完成相关项目负责人的评分：
        </p>

        <div v-for="(errorUser, index) in validationErrorData" :key="index" style="margin-bottom: 20px; padding: 15px; border: 1px solid #EBEEF5; border-radius: 4px;">
          <div style="margin-bottom: 10px;">
            <strong style="color: #303133;">员工：{{ errorUser.userName }}</strong>
          </div>

          <div v-if="errorUser.missingProjects && errorUser.missingProjects.length > 0">
            <p style="margin-bottom: 8px; color: #606266;">需要完成评分的项目：</p>
            <ul style="margin: 0; padding-left: 20px;">
              <li v-for="(project, pIndex) in errorUser.missingProjects" :key="pIndex" style="margin-bottom: 5px; color: #909399;">
                <span style="color: #409EFF;">{{ project.projectName }}</span>
                <span style="color: #606266;"> - 负责人：</span>
                <span style="color: #F56C6C;">{{ project.leaderName }}</span>
              </li>
            </ul>
          </div>

          <div v-else style="color: #909399; font-style: italic;">
            {{ errorUser.errorMessage }}
          </div>
        </div>

        <!-- <div style="margin-top: 20px; padding: 10px; background-color: #F0F9FF; border-left: 4px solid #409EFF; color: #606266;">
          <i class="el-icon-info"></i>
          <strong>操作建议：</strong>请联系相关项目负责人先完成对这些员工的项目评分，然后再进行机构负责人评分。
        </div> -->
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="validUsersForPartialSubmit.length > 0"
          type="primary"
          @click="submitValidUsersOnly"
        >
          仅提交验证通过的用户 ({{ validUsersForPartialSubmit.length }}人)
        </el-button>
        <el-button @click="closeValidationErrorDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserProfile } from "@/api/system/user";
import { listUser } from "@/api/system/user";
import { addEvaluation, listEvaluation, updateEvaluation, validateManagerEvaluation, exportDeptEvaluationData } from "@/api/system/evaluation";
import { getUserProjectEvaluations } from "@/api/system/evaluation";
import { getDept } from "@/api/system/dept";
import { listDept } from "@/api/system/dept";
import { getDeptInfo, getDeptEmployees, allocateBonus, getBonusStatus } from "@/api/system/managerEvaluationBonus";
import { deleteEmployeeBonusByDeptBonusId } from "@/api/system/employeeBonus";
import { useQuota, releaseQuota } from "@/api/system/deptQuota";
import { checkQuotaAvailable, getQuotaDetail } from "@/api/system/quotaManagement";
import { checkDeptInQuotaGroup, getQuotaGroupQuotaByDept } from "@/api/system/quotaGroup";
import { calculateEvaluationResults, calculateUserEvaluationResults, listEvaluation_result } from "@/api/system/evaluation_result";
import { getUserMonthlyEffort } from "@/api/system/participation";


export default {
  name: "DeptEvaluation",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 当前部门名称
      currentDeptName: "",
      // 当前部门ID
      currentDeptId: null,
      // 部门成员列表
      deptUsers: [],
      // 当前激活的标签页
      activeTab: 'evaluation',
      // 当前激活的评价类型标签页
      activeEvaluationTab: 'monthly',
      // 当前激活的奖金分配类型标签页
      activeBonusTab: 'monthly',
      // 奖金分配相关
      bonusLoading: false,
      bonusStatus: null,
      bonusEmployees: [],
      deptBonusId: null,
      // 多部门奖金状态
      deptBonusStatusList: [], // 各部门奖金状态列表
      
      // 季度奖金分配相关
      quarterlyBonusLoading: false, // 季度奖金分配加载状态
      quarterlyBonusStatus: null, // 季度奖金状态
      quarterlyBonusEmployees: [], // 季度奖金分配员工列表
      quarterlyDeptBonusId: null, // 季度部门奖金ID
      quarterlyDeptBonusStatusList: [], // 季度各部门奖金状态列表
      quarterlyBonusEvaluationMonth: '2022-07', // 季度奖金分配固定月份
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 验证错误弹框
      validationErrorDialog: false,
      validationErrorData: [],
      validUsersForPartialSubmit: [], // 验证通过的用户，用于部分提交
      // 日期选择器配置
      pickerOptions: {
        disabledDate: function(time) {
          return time.getTime() > new Date().setDate(1);
        }
      },
      // 查询参数
      queryParams: {
        evaluationMonth: this.getCurrentMonth()
      },
      // 表单参数
      form: {
        deptId: null,
        evaluateeId: null,
        evaluatorId: null,
        userName: null,
        score: null,
        evaluationMonth: null,
        evaluationType: "manager",
        comments: null
      },
      // 表单校验
      rules: {
        score: [
          { required: true, message: "评分不能为空", trigger: "change" }
        ]
      },
      // 是否有未保存的评分
      hasUnsavedChanges: false,
      // 已存在的评价记录
      existingEvaluations: {},
      // 是否启用自动计算评价结果
      autoCalculateEnabled: true,
      // 部门信息
      deptInfo: null,
      // 数据初始化状态
      initialized: false,
      // 部门负责人
      deptLeader: null,
      // 部门负责人管理的所有部门
      leaderDepts: [],

      // 高分配额相关
      quotaInfoList: [], // 多部门配额信息列表
      totalQuotaInfo: {   // 汇总配额信息
        totalEmployees: 0,
        highScoreQuota: 0,
        usedQuota: 0,
        remainingQuota: 0
      },
      currentYear: new Date().getFullYear().toString(),
      quotaLoading: false, // 配额信息加载状态

      // 部门选项卡相关
      activeDeptTab: '', // 当前激活的部门选项卡
      departmentGroups: [], // 按部门分组的数据
      
      // 季度评价相关
      quarterlyLoading: false, // 季度评价加载状态
      activeQuarterlyDeptTab: '', // 当前激活的季度评价部门选项卡
      quarterlyDepartmentGroups: [], // 季度评价按部门分组的数据
      quarterlyEvaluationMonth: '2022-08', // 季度评价固定月份

      // 子部门负责人状态
      isCurrentUserSubDeptLeader: false, // 当前用户是否为子部门负责人
      // 防重复提交标志
      isSubmitting: false
    };
  },
  computed: {
    // 总分配金额（所有部门的分配金额总和）
    totalAllocatedAmount() {
      if (!this.bonusEmployees || this.bonusEmployees.length === 0) {
        return 0;
      }
      return this.bonusEmployees.reduce((total, employee) => {
        return total + (employee.bonusAmount || 0);
      }, 0);
    },
    // 按部门分组的分配金额
    deptAllocatedAmounts() {
      const amounts = {};
      if (!this.bonusEmployees || this.bonusEmployees.length === 0) {
        return amounts;
      }

      this.bonusEmployees.forEach(employee => {
        const deptId = employee.deptId;
        if (!amounts[deptId]) {
          amounts[deptId] = 0;
        }
        amounts[deptId] += (employee.bonusAmount || 0);
      });

      return amounts;
    },
    // 总可分配金额（所有部门的总奖金之和）
    totalAvailableAmount() {
      if (!this.deptBonusStatusList || this.deptBonusStatusList.length === 0) {
        return 0;
      }
      return this.deptBonusStatusList.reduce((total, status) => {
        return total + (status.hasBonusAllocation ? status.totalBonus : 0);
      }, 0);
    },
    // 剩余可分配金额（总可分配 - 总已分配）
    remainingAmount() {
      return this.totalAvailableAmount - this.totalAllocatedAmount;
    },
    // 按部门的剩余金额
    deptRemainingAmounts() {
      const remaining = {};
      this.deptBonusStatusList.forEach(status => {
        if (status.hasBonusAllocation) {
          const allocated = this.deptAllocatedAmounts[status.deptId] || 0;
          remaining[status.deptId] = status.totalBonus - allocated;
        }
      });
      return remaining;
    },
    // 检查是否有部门超额分配
    hasOverAllocation() {
      const deptRemaining = this.deptRemainingAmounts;
      return Object.values(deptRemaining).some(amount => amount < 0);
    },
    // 是否可以提交奖金分配
    canSubmitBonus() {
      return this.hasAnyBonusAllocation &&
             this.bonusEmployees.length > 0 &&
             this.totalAllocatedAmount !== 0 &&
             !this.hasOverAllocation; // 确保没有部门超额分配
    },
    // 是否有任何部门有奖金分配
    hasAnyBonusAllocation() {
      return this.deptBonusStatusList.some(status => status.hasBonusAllocation);
    },

    // 季度奖金分配相关计算属性
    // 季度总分配金额（所有部门的分配金额总和）
    quarterlyTotalAllocatedAmount() {
      if (!this.quarterlyBonusEmployees || this.quarterlyBonusEmployees.length === 0) {
        return 0;
      }
      return this.quarterlyBonusEmployees.reduce((total, employee) => {
        return total + (employee.quarterlyBonusAmount || 0);
      }, 0);
    },
    // 季度按部门分组的分配金额
    quarterlyDeptAllocatedAmounts() {
      const amounts = {};
      if (!this.quarterlyBonusEmployees || this.quarterlyBonusEmployees.length === 0) {
        return amounts;
      }

      this.quarterlyBonusEmployees.forEach(employee => {
        const deptId = employee.deptId;
        if (!amounts[deptId]) {
          amounts[deptId] = 0;
        }
        amounts[deptId] += (employee.quarterlyBonusAmount || 0);
      });

      return amounts;
    },
    // 季度总可分配金额（所有部门的总奖金之和）
    quarterlyTotalAvailableAmount() {
      if (!this.quarterlyDeptBonusStatusList || this.quarterlyDeptBonusStatusList.length === 0) {
        return 0;
      }
      return this.quarterlyDeptBonusStatusList.reduce((total, status) => {
        return total + (status.hasBonusAllocation ? status.totalBonus : 0);
      }, 0);
    },
    // 季度剩余可分配金额（总可分配 - 总已分配）
    quarterlyRemainingAmount() {
      return this.quarterlyTotalAvailableAmount - this.quarterlyTotalAllocatedAmount;
    },
    // 季度按部门的剩余金额
    quarterlyDeptRemainingAmounts() {
      const remaining = {};
      this.quarterlyDeptBonusStatusList.forEach(status => {
        if (status.hasBonusAllocation) {
          const allocated = this.quarterlyDeptAllocatedAmounts[status.deptId] || 0;
          remaining[status.deptId] = status.totalBonus - allocated;
        }
      });
      return remaining;
    },
    // 检查是否有部门超额分配（季度）
    hasQuarterlyOverAllocation() {
      const deptRemaining = this.quarterlyDeptRemainingAmounts;
      return Object.values(deptRemaining).some(amount => amount < 0);
    },
    // 是否可以提交季度奖金分配
    canSubmitQuarterlyBonus() {
      return this.hasAnyQuarterlyBonusAllocation &&
             this.quarterlyBonusEmployees.length > 0 &&
             this.quarterlyTotalAllocatedAmount !== 0 &&
             !this.hasQuarterlyOverAllocation; // 确保没有部门超额分配
    },
    // 是否有任何部门有季度奖金分配
    hasAnyQuarterlyBonusAllocation() {
      return this.quarterlyDeptBonusStatusList.some(status => status.hasBonusAllocation);
    }
  },
  created() {
    // 防止重复初始化
    if (!this.initialized && !this.loading) {
      this.initData();
    }
  },
  mounted() {
    // 组件挂载后，确保数据加载（只在未初始化且未加载时执行）
    if (!this.initialized && !this.loading) {
      this.initData();
    }
  },
  methods: {
    // 初始化数据
    initData() {
      // 设置初始化标志
      this.initialized = false;
      this.loading = true;
      
      // 检查当前用户信息
      if (!this.$store.state.user || !this.$store.state.user.id) {
        this.$message.error("无法获取当前用户信息，请重新登录");
        this.loading = false;
        return;
      }
      
      // 获取当前用户详细信息
      this.getCurrentUserInfo()
        .catch(error => {
          this.$message.error("初始化数据失败: " + (error.message || error));
          this.loading = false;
        });
    },
    
    // 获取当前月份，格式为yyyy-MM
    getCurrentMonth() {
      const date = new Date();
      const year = date.getFullYear();
      let month = date.getMonth() + 1;
      month = month < 10 ? '0' + month : month;
      return `${year}-${month}`;
    },
    
    // 获取当前用户信息
    getCurrentUserInfo() {
      return new Promise((resolve, reject) => {
        getUserProfile().then(response => {
          const userInfo = response.data;
          this.currentDeptId = userInfo.deptId;
          this.currentDeptName = (userInfo.dept && userInfo.dept.deptName) || "当前部门";
          
          // 获取部门详细信息
          this.getDeptInfo()
            .then(resolve)
            .catch(reject);
        }).catch(error => {
          this.$message.error("获取用户信息失败：" + error);
          reject(error);
        });
      });
    },
    
    // 获取部门详细信息
    getDeptInfo() {
      return new Promise((resolve, reject) => {
        if (!this.currentDeptId) {
          this.loading = false;
          reject(new Error("没有部门ID"));
          return;
        }
        
        getDept(this.currentDeptId).then(response => {
          this.deptInfo = response.data;
          // 尝试获取当前登录用户信息
          const currentUser = this.$store.state.user.userName ||
                             this.$store.state.user.name ||
                             this.$store.getters.name ||
                             this.$store.getters.userName;

          // 如果能获取到当前用户，使用当前用户；否则使用当前部门负责人
          this.deptLeader = currentUser || this.deptInfo.leader;



          // 获取负责人管理的所有部门
          this.getLeaderDepts()
            .then(() => {
              // 获取所有相关部门的成员
              this.getAllDeptUsers()
                .then(resolve)
                .catch(reject);
            })
            .catch(error => {
              this.$message.error("获取部门负责人的部门列表失败：" + error);
              // 退回到只获取当前部门成员
              this.getDeptUsers()
                .then(resolve)
                .catch(reject);
            });
        }).catch(error => {
          this.$message.error("获取部门信息失败：" + error);
          // 仍然尝试获取部门成员
          this.getDeptUsers()
            .then(resolve)
            .catch(reject);
        });
      });
    },
    
    // 获取部门负责人管理的所有部门（包括子部门）
    getLeaderDepts() {
      return new Promise((resolve, reject) => {
        if (!this.deptLeader) {
          this.$message.warning("未找到部门负责人信息，将只显示当前部门成员");
          this.leaderDepts = [this.currentDeptId]; // 只包含当前部门
          // 设置子部门负责人状态为false（没有部门负责人信息）
          this.isCurrentUserSubDeptLeader = false;
          resolve();
          return;
        }

        // 查询所有部门，然后在前端过滤
        listDept({}).then(response => {
          const allDepts = response.data || [];

          // 在前端过滤出负责人是当前登录用户的部门
          const directDepts = allDepts.filter(dept => dept.leader === this.deptLeader);



          if (directDepts.length === 0) {
            this.$message.warning("未找到部门负责人管理的部门，将只显示当前部门成员");
            this.leaderDepts = [this.currentDeptId]; // 只包含当前部门
            // 设置子部门负责人状态为false（没有负责任何部门）
            this.isCurrentUserSubDeptLeader = false;
          } else {
            // 判断用户是否为子部门负责人
            const isSubDeptLeader = this.isUserSubDeptLeader(directDepts, allDepts);

            // 设置子部门负责人状态
            this.isCurrentUserSubDeptLeader = isSubDeptLeader;

            if (isSubDeptLeader) {
              // 子部门负责人只能看到直接负责的部门
              this.leaderDepts = directDepts.map(dept => dept.deptId);
              this.currentDeptName = directDepts.map(dept => dept.deptName).join('、');
            } else {
              // 父部门负责人可以看到负责的部门及其子部门

              // 获取直接管理的部门ID
              this.leaderDepts = directDepts.map(dept => dept.deptId);

              // 查找所有子部门（基于ancestors字段）
              const allManagedDepts = [...this.leaderDepts];

              this.leaderDepts.forEach(parentDeptId => {
                const childDepts = allDepts.filter(dept => {
                  // 检查ancestors字段是否包含父部门ID
                  return dept.ancestors && dept.ancestors.split(',').includes(parentDeptId.toString());
                });

                childDepts.forEach(childDept => {
                  if (!allManagedDepts.includes(childDept.deptId)) {
                    allManagedDepts.push(childDept.deptId);
                  }
                });
              });

              this.leaderDepts = allManagedDepts;

              // 如果当前部门不在列表中且负责人与当前部门负责人一致，添加进去
              if (!this.leaderDepts.includes(this.currentDeptId) &&
                  this.deptInfo && this.deptInfo.leader === this.deptLeader) {
                this.leaderDepts.push(this.currentDeptId);
              }

              // 更新显示的部门名称为相关部门
              this.currentDeptName = directDepts.map(dept => dept.deptName).join('、');
              if (allManagedDepts.length > directDepts.length) {
                this.currentDeptName += '（含子部门）';
              }
            }
          }
          resolve();
        }).catch(error => {
          this.$message.error("获取部门列表失败：" + error);
          this.leaderDepts = [this.currentDeptId]; // 只包含当前部门
          // 设置子部门负责人状态为false（获取部门列表失败）
          this.isCurrentUserSubDeptLeader = false;
          reject(error);
        });
      });
    },

    // 判断用户是否为子部门负责人
    isUserSubDeptLeader(directDepts, allDepts) {
      // 如果没有负责任何部门，返回false
      if (!directDepts || directDepts.length === 0) {
        return false;
      }

      // 统计部门类型
      let topLevelDepts = 0;  // 顶级部门数量
      let subDepts = 0;       // 子部门数量
      let subDeptsWithOtherParentLeader = 0; // 有其他父部门负责人的子部门数量

      for (let dept of directDepts) {
        if (!dept.parentId || dept.parentId === 0) {
          // 顶级部门
          topLevelDepts++;
        } else {
          // 子部门
          subDepts++;

          // 检查父部门是否有其他负责人
          const parentDept = allDepts.find(d => d.deptId === dept.parentId);
          if (parentDept && parentDept.leader && parentDept.leader !== this.deptLeader) {
            // 有其他人负责父部门
            subDeptsWithOtherParentLeader++;
          }
        }
      }

      // 判断逻辑：
      // 1. 如果有任何顶级部门，则不是纯子部门负责人
      // 2. 如果所有部门都是子部门，且都有其他父部门负责人，则是纯子部门负责人
      const isPureSubDeptLeader = topLevelDepts === 0 && subDepts > 0 && subDeptsWithOtherParentLeader === subDepts;

      return isPureSubDeptLeader;
    },

    // 判断当前用户是否为子部门负责人（同步版本，用于模板和其他方法调用）
    isSubDeptLeader() {
      return this.isCurrentUserSubDeptLeader;
    },

    // 检查用户是否在当前月份参与项目（基于精力分配）- 修复版本
    async checkUserHasProjects(userName) {
      try {
        // 检查当前选择的月份
        if (!this.queryParams.evaluationMonth) {
          return false;
        }

        // 调用精力分配API，检查用户在当前月份是否有项目精力分配
        const response = await getUserMonthlyEffort(userName, this.queryParams.evaluationMonth);

        if (response.data && Array.isArray(response.data)) {
          // 检查是否有精力分配大于0的项目
          const hasEffortAllocation = response.data.some(participation => {
            const rate = participation.participationRate;
            return rate && parseFloat(rate) > 0;
          });
          return hasEffortAllocation;
        }
        return false;
      } catch (error) {
        return false; // 出错时默认认为不参与项目
      }
    },

    // 批量检查用户项目参与状态
    async batchCheckUsersProjects(users) {
      const promises = users.map(async (user) => {
        const hasProjects = await this.checkUserHasProjects(user.userName);
        return {
          ...user,
          hasProjects: hasProjects
        };
      });

      return Promise.all(promises);
    },

    // 获取所有相关部门的成员
    getAllDeptUsers() {
      return new Promise((resolve, reject) => {
        if (this.leaderDepts.length === 0) {
          this.loading = false;
          reject(new Error("没有部门ID列表"));
          return;
        }
        
        // 所有用户的集合，用userId作为键避免重复
        const allUsers = {};
        // 完成的请求计数
        let completedRequests = 0;
        // 总请求数
        const totalRequests = this.leaderDepts.length;
        
        // 处理每个部门的用户
        this.leaderDepts.forEach(deptId => {
          listUser({ deptId: deptId }).then(response => {
            const users = response.rows || [];

            // 添加用户到集合中，避免重复
            users.forEach(user => {
              if (!allUsers[user.userId]) {
                allUsers[user.userId] = user;
              }
            });
            
            // 检查是否所有请求都完成了
            completedRequests++;
            if (completedRequests === totalRequests) {
              // 将用户集合转为数组
              let filteredUsers = Object.values(allUsers).filter(user => {
                // 排除部门负责人和特聘专家
                return user.userName !== this.deptLeader && user.remark !== '特聘专家';
              });

              // 如果是子部门负责人，需要进一步过滤掉参与项目的用户
              if (this.isSubDeptLeader()) {
                this.batchCheckUsersProjects(filteredUsers).then(usersWithProjectStatus => {
                  // 过滤掉参与项目的用户
                  const finalFilteredUsers = usersWithProjectStatus.filter(user => !user.hasProjects);



                  this.deptUsers = finalFilteredUsers.map(user => ({
                    ...user,
                    newScore: null,
                    comments: "",
                    _uid: user.userId,  // 用于table的key
                    projectEvaluations: [],  // 初始化项目评价列表
                    dataLoaded: false  // 标记项目数据是否已加载
                  }));

                  // 按部门分组用户数据，然后获取已有评价记录
                  this.groupUsersByDepartment().then(() => {
                    // 获取已有评价记录（在分组之后）
                    this.getExistingEvaluations();
                    // 验证所有用户的评分前置条件
                    this.validateAllUsersOnLoad();
                  });

                  // 延迟获取高分配额信息，避免重复请求
                  setTimeout(() => {
                    this.getQuotaInfo();
                  }, 100);

                  // 标记初始化完成
                  this.initialized = true;
                  resolve();
                }).catch(error => {
                  // 出错时使用原有逻辑，不过滤用户
                  this.deptUsers = filteredUsers.map(user => ({
                    ...user,
                    newScore: null,
                    comments: "",
                    _uid: user.userId,
                    projectEvaluations: [],
                    dataLoaded: false
                  }));

                  this.groupUsersByDepartment().then(() => {
                    this.getExistingEvaluations();
                  });

                  setTimeout(() => {
                    this.getQuotaInfo();
                  }, 100);

                  this.initialized = true;
                  resolve();
                });
              } else {
                // 父部门负责人，不需要过滤参与项目的用户
                this.deptUsers = filteredUsers.map(user => ({
                  ...user,
                  newScore: null,
                  comments: "",
                  _uid: user.userId,  // 用于table的key
                  projectEvaluations: [],  // 初始化项目评价列表
                  dataLoaded: false  // 标记项目数据是否已加载
                }));

                // 按部门分组用户数据，然后获取已有评价记录
                this.groupUsersByDepartment().then(() => {
                  // 获取已有评价记录（在分组之后）
                  this.getExistingEvaluations();
                  // 验证所有用户的评分前置条件
                  this.validateAllUsersOnLoad();
                });

                // 延迟获取高分配额信息，避免重复请求
                setTimeout(() => {
                  this.getQuotaInfo();
                }, 100);

                // 标记初始化完成
                this.initialized = true;
                resolve();
              }
            }
          }).catch(error => {
            this.$message.error(`获取部门(ID:${deptId})用户失败：${error}`);
            
            // 即使出错也计数，确保流程能继续
            completedRequests++;
            if (completedRequests === totalRequests) {
              if (Object.keys(allUsers).length > 0) {
                // 有一些用户数据
                let filteredUsers = Object.values(allUsers).filter(user => {
                  return user.userName !== this.deptLeader;
                });

                // 如果是子部门负责人，需要进一步过滤掉参与项目的用户
                if (this.isSubDeptLeader()) {
                  this.batchCheckUsersProjects(filteredUsers).then(usersWithProjectStatus => {
                    // 过滤掉参与项目的用户
                    const finalFilteredUsers = usersWithProjectStatus.filter(user => !user.hasProjects);



                    this.deptUsers = finalFilteredUsers.map(user => ({
                      ...user,
                      newScore: null,
                      comments: "",
                      _uid: user.userId,
                      projectEvaluations: [],
                      dataLoaded: false
                    }));

                    // 按部门分组用户数据，然后获取已有评价记录
                    this.groupUsersByDepartment().then(() => {
                      // 获取已有评价记录（在分组之后）
                      this.getExistingEvaluations();
                      // 验证所有用户的评分前置条件
                      this.validateAllUsersOnLoad();
                    });

                    // 延迟获取高分配额信息，避免重复请求
                    setTimeout(() => {
                      this.getQuotaInfo();
                    }, 100);

                    this.initialized = true;
                    resolve();
                  }).catch(projectError => {
                    // 出错时使用原有逻辑，不过滤用户
                    this.deptUsers = filteredUsers.map(user => ({
                      ...user,
                      newScore: null,
                      comments: "",
                      _uid: user.userId,
                      projectEvaluations: [],
                      dataLoaded: false
                    }));

                    this.groupUsersByDepartment().then(() => {
                      this.getExistingEvaluations();
                      this.validateAllUsersOnLoad();
                    });

                    setTimeout(() => {
                      this.getQuotaInfo();
                    }, 100);

                    this.initialized = true;
                    resolve();
                  });
                } else {
                  // 父部门负责人，不需要过滤参与项目的用户
                  this.deptUsers = filteredUsers.map(user => ({
                    ...user,
                    newScore: null,
                    comments: "",
                    _uid: user.userId,
                    projectEvaluations: [],
                    dataLoaded: false
                  }));

                  // 按部门分组用户数据，然后获取已有评价记录
                  this.groupUsersByDepartment().then(() => {
                    // 获取已有评价记录（在分组之后）
                    this.getExistingEvaluations();
                    // 验证所有用户的评分前置条件
                    this.validateAllUsersOnLoad();
                  });

                  // 延迟获取高分配额信息，避免重复请求
                  setTimeout(() => {
                    this.getQuotaInfo();
                  }, 100);

                  this.initialized = true;
                  resolve();
                }
              } else {
                this.loading = false;
                reject(error);
              }
            }
          });
        });
      });
    },
    
    // 获取用户的项目精力分配和评分状态 (按需加载)
    loadUserProjectEvaluations(row) {
      // 先判断是否传入了有效的row对象
      if (!row || !row.userId) {
        this.$message.error("无效的用户数据");
        return Promise.reject("无效的用户数据");
      }
      
      // 检查评价月份是否选择
      if (!this.queryParams.evaluationMonth) {
        this.$message.warning("请先选择评价月份");
        return Promise.reject("未选择评价月份");
      }
      
      // 查找用户在部门组中的位置
      let userRef = null;
      let deptGroupIndex = -1;
      let userIndex = -1;

      for (let i = 0; i < this.departmentGroups.length; i++) {
        const group = this.departmentGroups[i];
        const index = group.users.findIndex(u => u.userId === row.userId);
        if (index !== -1) {
          userRef = group.users[index];
          deptGroupIndex = i;
          userIndex = index;
          break;
        }
      }

      if (!userRef) {
        this.$message.error("未找到用户: " + row.userName);
        return Promise.reject("用户不存在");
      }

      // 标记加载状态
      this.$set(this.departmentGroups[deptGroupIndex].users[userIndex], 'loading', true);

      // 如果数据已加载且不是强制刷新，则不重复加载
      if (userRef.dataLoaded) {
        this.$set(this.departmentGroups[deptGroupIndex].users[userIndex], 'loading', false);
        return Promise.resolve(userRef.projectEvaluations);
      }
      
      // 获取用户ID
      const userId = row.userId;
      const evaluationMonth = this.queryParams.evaluationMonth;
      
      // 创建一个Promise来处理数据加载
      return new Promise((resolve, reject) => {
        // 同时获取精力分配数据和项目评分数据
        Promise.all([
          // 获取精力分配数据
          this.getUserMonthlyEffort(row.userName, evaluationMonth),
          // 获取项目评分数据
          getUserProjectEvaluations(userId, evaluationMonth)
        ]).then(([effortResponse, evaluationResponse]) => {
          let projectData = [];

          // 处理精力分配数据
          if (effortResponse && effortResponse.data && Array.isArray(effortResponse.data)) {
            const effortMap = {};
            effortResponse.data.forEach(item => {
              if (item.projectId && parseFloat(item.participationRate) > 0) {
                effortMap[item.projectId] = {
                  projectId: item.projectId,
                  projectName: item.projectName,
                  participationRate: item.participationRate,
                  role: item.role || '参与',
                  hasEffort: true
                };
              }
            });

            // 处理项目评分数据
            const evaluationMap = {};
            if (evaluationResponse && evaluationResponse.data) {
              evaluationResponse.data.forEach(item => {
                if (item.projectId) {
                  evaluationMap[item.projectId] = {
                    score: item.score,
                    evaluationId: item.id,
                    hasEvaluation: true
                  };
                }
              });
            }

            // 合并数据：以精力分配为主，补充评分信息
            Object.keys(effortMap).forEach(projectId => {
              const effort = effortMap[projectId];
              const evaluation = evaluationMap[projectId] || {};

              projectData.push({
                projectId: effort.projectId,
                projectName: effort.projectName,
                participationRate: effort.participationRate,
                role: effort.role,
                score: evaluation.score || null,
                hasEvaluation: evaluation.hasEvaluation || false,
                evaluationStatus: this.getEvaluationStatus(effort.role, evaluation.hasEvaluation)
              });
            });
          }

          // 更新用户的项目数据
          this.$set(this.departmentGroups[deptGroupIndex].users[userIndex], 'projectEvaluations', projectData);
          this.$set(this.departmentGroups[deptGroupIndex].users[userIndex], 'dataLoaded', true);
          this.$set(this.departmentGroups[deptGroupIndex].users[userIndex], 'loading', false);

          resolve(projectData);
        }).catch(error => {
          // 出错时设置空数据
          this.$set(this.departmentGroups[deptGroupIndex].users[userIndex], 'projectEvaluations', []);
          this.$set(this.departmentGroups[deptGroupIndex].users[userIndex], 'dataLoaded', true);
          this.$set(this.departmentGroups[deptGroupIndex].users[userIndex], 'loading', false);

          this.$message.error("获取项目数据失败: " + (error.message || error));
          resolve([]);
        });
      });
    },

    // 获取用户精力分配数据
    async getUserMonthlyEffort(userName, month) {
      try {
        return await getUserMonthlyEffort(userName, month);
      } catch (error) {
        console.error('获取精力分配数据失败:', error);
        return { data: [] };
      }
    },

    // 获取评分状态
    getEvaluationStatus(role, hasEvaluation) {
      if (role === '负责人') {
        return '项目负责人(使用机构负责人评分)';
      } else if (hasEvaluation) {
        return '已评分';
      } else {
        return '未评分';
      }
    },

    // 获取已有评价记录 - 修改为支持多部门和多评价类型
    getExistingEvaluations(shouldValidateUsers = false) {
      if (!this.queryParams.evaluationMonth) {
        this.loading = false;
        return Promise.resolve();
      }

      // 获取当前登录用户ID
      const currentUserId = this.$store.state.user.id;

      // 需要查询两种评价类型：manager 和 parent_manager
      const managerParams = {
        evaluationType: "manager",
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluatorId: currentUserId,
      };

      const parentManagerParams = {
        evaluationType: "parent_manager",
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluatorId: currentUserId,
      };

      // 并行查询两种评价类型
      return Promise.all([
        listEvaluation(managerParams),
        listEvaluation(parentManagerParams)
      ]).then(responses => {
        const managerEvaluations = responses[0].rows || [];
        const parentManagerEvaluations = responses[1].rows || [];

        // 合并两种评价类型的结果
        const allEvaluations = [...managerEvaluations, ...parentManagerEvaluations];

        // 创建一个映射表，使用 evaluateeId + evaluationType 作为键
        const evaluationMap = {};

        allEvaluations.forEach(evaluation => {
          if (evaluation.evaluateeId !== null && evaluation.evaluateeId !== undefined) {
            const evaluateeId = Number(evaluation.evaluateeId);
            if (!isNaN(evaluateeId) && evaluation.evaluatorId === currentUserId) {
              // 使用 evaluateeId + evaluationType 作为键，确保能区分不同类型的评价
              const key = `${evaluateeId}_${evaluation.evaluationType}`;
              evaluationMap[key] = evaluation;
            }
          }
        });

        // 更新用户的评分
        // 更新部门组中用户的评分
        this.departmentGroups.forEach((deptGroup, deptIndex) => {
          deptGroup.users.forEach((user, userIndex) => {
            // 始终将userId转为数字类型进行匹配
            const userId = Number(user.userId);

            if (!isNaN(userId)) {
              // 根据当前登录用户的身份确定评价类型
              let evaluationType = "manager"; // 默认为manager类型

              // 如果当前用户是父部门负责人，且被评价用户是子部门成员，则使用parent_manager类型
              if (!this.isSubDeptLeader() && user.isSubDeptMember) {
                evaluationType = "parent_manager";
              }

              const key = `${userId}_${evaluationType}`;

              if (evaluationMap[key]) {
                const evaluation = evaluationMap[key];

                // 处理评分，确保是数字类型
                let score = null;
                if (evaluation.score !== null && evaluation.score !== undefined) {
                  if (typeof evaluation.score === 'object' && evaluation.score.toFixed) {
                    // 处理Java BigDecimal类型
                    score = Number(evaluation.score.toFixed(0));
                  } else {
                    // 处理其他类型
                    score = Number(evaluation.score);
                  }

                  // 确保score是有效数字
                  if (isNaN(score)) {
                    score = null;
                  }
                }

                // 更新用户的评分和评价信息
                this.$set(this.departmentGroups[deptIndex].users[userIndex], 'newScore', score);
                this.$set(this.departmentGroups[deptIndex].users[userIndex], 'comments', evaluation.comments || "");
                this.$set(this.departmentGroups[deptIndex].users[userIndex], 'evaluationId', evaluation.id);
              } else {
                // 重置未评价的用户数据
                this.$set(this.departmentGroups[deptIndex].users[userIndex], 'newScore', null);
                this.$set(this.departmentGroups[deptIndex].users[userIndex], 'comments', "");
                this.$set(this.departmentGroups[deptIndex].users[userIndex], 'evaluationId', null);
              }
            }
          });
        });

        this.loading = false;

        // 如果需要验证用户，则在数据加载完成后验证
        if (shouldValidateUsers) {
          setTimeout(() => {
            this.validateAllUsersOnLoad();
          }, 100);
        }
      }).catch(error => {
        this.$message.error("获取评价记录失败：" + error);
        this.loading = false;
        throw error;
      });
    },
    
    // 处理查询 - 重置数据加载状态
    handleQuery() {
      this.loading = true;

      // 重置所有部门组用户的数据加载状态
      this.departmentGroups.forEach((deptGroup, deptIndex) => {
        deptGroup.users.forEach((user, userIndex) => {
          this.$set(this.departmentGroups[deptIndex].users[userIndex], 'dataLoaded', false);
        });
      });

      // 获取评价记录并在完成后验证用户评分前置条件
      this.getExistingEvaluations(true);
    },

    // 重置查询 - 重置数据加载状态
    resetQuery() {
      this.queryParams.evaluationMonth = this.getCurrentMonth();
      this.loading = true;

      // 重置所有部门组用户的数据加载状态
      this.departmentGroups.forEach((deptGroup, deptIndex) => {
        deptGroup.users.forEach((user, userIndex) => {
          this.$set(this.departmentGroups[deptIndex].users[userIndex], 'dataLoaded', false);
        });
      });

      // 获取评价记录并在完成后验证用户评分前置条件
      this.getExistingEvaluations(true);
    },
    
    // 处理评分变更
    handleScoreChange(row) {
      // 如果不为空，确保是整数值
      if (row.newScore !== null && row.newScore !== undefined) {
        row.newScore = Math.round(row.newScore);
        if (row.newScore < 0) row.newScore = 0;
        if (row.newScore > 100) row.newScore = 100;

        // 检查高分配额
        if (row.newScore >= 95) {
          this.checkHighScoreQuota(row);
        }
      }

      this.hasUnsavedChanges = true;
    },

    // 获取单个部门的配额信息（支持配额组）
    async getDeptQuotaInfo(deptId, retryCount = 0) {
      const maxRetries = 2;

      try {
        // 首先检查部门是否属于配额组
        const quotaGroupResponse = await checkDeptInQuotaGroup(deptId);
        const isInQuotaGroup = quotaGroupResponse.data;

        if (isInQuotaGroup) {
          // 如果属于配额组，获取配额组的配额信息
          const quotaResponse = await getQuotaGroupQuotaByDept(deptId, this.currentYear);
          const quotaData = quotaResponse.data;

          if (quotaData) {
            // 转换配额组数据格式为标准格式
            return {
              deptId: deptId,
              totalEmployees: quotaData.totalEmployees || 0,
              highScoreQuota: quotaData.highScoreQuota || 0,
              usedQuota: quotaData.usedQuota || 0,
              remainingQuota: quotaData.remainingQuota || 0,
              isQuotaGroup: true,
              groupName: quotaData.groupName
            };
          }
        } else {
          // 如果不属于配额组，获取普通部门配额信息
          const quotaResponse = await getQuotaDetail(deptId, this.currentYear);
          const quotaData = quotaResponse.data;

          if (quotaData) {
            const result = {
              deptId: deptId,
              totalEmployees: quotaData.totalEmployees || 0,
              highScoreQuota: quotaData.highScoreQuota || 0,
              usedQuota: quotaData.usedQuota || 0,
              remainingQuota: quotaData.remainingQuota || 0,
              isQuotaGroup: false
            };
            return result;
          }
        }

        // 如果没有配额信息，返回默认值
        return {
          deptId: deptId,
          totalEmployees: 0,
          highScoreQuota: 0,
          usedQuota: 0,
          remainingQuota: 0,
          isQuotaGroup: false
        };
      } catch (error) {
        // 检查是否是网络错误
        const isNetworkError = error.code === 'ERR_NETWORK' ||
                              error.code === 'ERR_NETWORK_CHANGED' ||
                              error.message === 'Network Error';

        if (isNetworkError && retryCount < maxRetries) {
          // 延迟重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
          return this.getDeptQuotaInfo(deptId, retryCount + 1);
        }

        // 返回默认值
        return {
          deptId: deptId,
          totalEmployees: 0,
          highScoreQuota: 0,
          usedQuota: 0,
          remainingQuota: 0,
          isQuotaGroup: false
        };
      }
    },

    // 获取配额信息 - 支持配额组和配额管理系统
    async getQuotaInfo() {
      // 如果是子部门负责人，不获取配额信息
      if (this.isSubDeptLeader()) {

        this.quotaInfoList = [];
        this.totalQuotaInfo = {
          totalEmployees: 0,
          highScoreQuota: 0,
          usedQuota: 0,
          remainingQuota: 0
        };
        return;
      }

      // 防止重复请求
      if (this.quotaLoading) {

        return;
      }

      this.quotaLoading = true;

      try {
        if (this.leaderDepts && this.leaderDepts.length > 0) {
          // 确保部门ID是数字类型
          const deptIds = this.leaderDepts.map(id => Number(id));

          // 获取多个部门的配额信息 - 支持配额组
          const quotaPromises = deptIds.map(deptId => this.getDeptQuotaInfo(deptId));
          const quotaInfos = await Promise.all(quotaPromises);

          this.quotaInfoList = quotaInfos.filter(data => data);

          // 添加部门名称信息
          await this.enrichQuotaWithDeptNames();

          // 计算汇总信息
          this.calculateTotalQuotaInfo();

          // 更新部门组的配额信息
          this.enrichDepartmentGroupsWithQuota();
        } else if (this.currentDeptId) {
          // 单个部门 - 支持配额组
          const quotaInfo = await this.getDeptQuotaInfo(this.currentDeptId);
          this.quotaInfoList = [quotaInfo];
          this.totalQuotaInfo = quotaInfo;

          // 添加部门名称
          await this.enrichQuotaWithDeptNames();

          // 更新部门组的配额信息
          this.enrichDepartmentGroupsWithQuota();
        } else {
          // 没有部门信息，初始化空数据
          this.quotaInfoList = [];
          this.totalQuotaInfo = {
            totalEmployees: 0,
            highScoreQuota: 0,
            usedQuota: 0,
            remainingQuota: 0
          };
        }
      } catch (error) {
        // 检查是否是网络错误
        const isNetworkError = error.code === 'ERR_NETWORK' ||
                              error.code === 'ERR_NETWORK_CHANGED' ||
                              error.message === 'Network Error';

        // 只有在非网络错误且非重复提交错误时才显示错误消息
        if (!isNetworkError && (!error.message || !error.message.includes('重复提交'))) {
          this.$message.error('获取配额信息失败，请稍后重试');
        }
        // 初始化空数据
        this.quotaInfoList = [];
        this.totalQuotaInfo = {
          totalEmployees: 0,
          highScoreQuota: 0,
          usedQuota: 0,
          remainingQuota: 0
        };
      } finally {
        this.quotaLoading = false;
      }
    },

    // 为配额信息添加部门名称
    async enrichQuotaWithDeptNames() {
      for (let quota of this.quotaInfoList) {
        try {
          const deptResponse = await getDept(quota.deptId);
          quota.deptName = deptResponse.data.deptName;
        } catch (error) {
          quota.deptName = `部门${quota.deptId}`;
        }
      }
    },

    // 计算汇总配额信息
    calculateTotalQuotaInfo() {
      this.totalQuotaInfo = {
        totalEmployees: this.quotaInfoList.reduce((sum, quota) => sum + (quota.totalEmployees || 0), 0),
        highScoreQuota: this.quotaInfoList.reduce((sum, quota) => sum + (quota.highScoreQuota || 0), 0),
        usedQuota: this.quotaInfoList.reduce((sum, quota) => sum + (quota.usedQuota || 0), 0),
        remainingQuota: this.quotaInfoList.reduce((sum, quota) => sum + (quota.remainingQuota || 0), 0)
      };
    },

    // 刷新配额信息
    refreshQuotaInfo() {
      // 重置加载状态，允许重新请求
      this.quotaLoading = false;
      this.getQuotaInfo();
    },

    // 强制刷新配额信息
    forceRefreshQuotaInfo() {

      // 重置加载状态和缓存
      this.quotaLoading = false;
      this.quotaInfoList = [];
      this.totalQuotaInfo = {
        totalEmployees: 0,
        highScoreQuota: 0,
        usedQuota: 0,
        remainingQuota: 0
      };

      // 延迟一点时间再获取，确保数据库已更新
      setTimeout(() => {
        this.getQuotaInfo();
      }, 200);
    },

    // 提交评分后立即刷新配额信息
    refreshQuotaInfoAfterSave() {
      // 立即刷新配额信息，不等待
      this.getQuotaInfo().then(() => {

        // 更新部门组的配额信息显示
        this.enrichDepartmentGroupsWithQuota();

        // 强制更新视图
        this.$forceUpdate();
      }).catch(error => {
        // 如果立即刷新失败，延迟重试
        setTimeout(() => {
          this.forceRefreshQuotaInfo();
        }, 1000);
      });
    },

    // 按部门分组用户数据（父部门和子部门合并显示）
    async groupUsersByDepartment() {
      if (!this.deptUsers || this.deptUsers.length === 0) {
        this.departmentGroups = [];
        return;
      }

      // 获取部门层级信息
      const allDepts = await this.getAllDepartments();

      // 按父部门分组（将子部门成员合并到父部门）
      const deptGroups = {};

      this.deptUsers.forEach(user => {
        const userDeptId = user.deptId || (user.dept && user.dept.deptId) || this.currentDeptId;
        const userDept = allDepts.find(d => d.deptId === userDeptId);

        // 确定显示的部门（父部门）
        let displayDeptId = userDeptId;
        let displayDeptName = (user.dept && user.dept.deptName) || user.deptName || '';
        let isSubDeptMember = false;

        if (userDept && userDept.ancestors) {
          // 检查是否为子部门成员
          const ancestors = userDept.ancestors.split(',').filter(id => id !== '0');

          // 查找父部门（当前负责人管理的部门）
          for (let ancestorId of ancestors.reverse()) {
            const ancestorDept = allDepts.find(d => d.deptId.toString() === ancestorId);
            if (ancestorDept && ancestorDept.leader === this.deptLeader) {
              displayDeptId = ancestorDept.deptId;
              displayDeptName = ancestorDept.deptName;
              isSubDeptMember = true;
              break;
            }
          }

          // 如果没有找到父部门，但当前部门的负责人是当前用户，则使用当前部门
          if (!isSubDeptMember && userDept.leader === this.deptLeader) {
            displayDeptId = userDeptId;
            displayDeptName = userDept.deptName;
          }
        }

        if (!deptGroups[displayDeptId]) {
          deptGroups[displayDeptId] = {
            deptId: displayDeptId,
            deptName: displayDeptName,
            users: [],
            quotaInfo: null
          };
        }

        // 为子部门成员添加标识
        const userWithMark = {
          ...user,
          isSubDeptMember: isSubDeptMember,
          originalDeptName: userDept ? userDept.deptName : (user.dept && user.dept.deptName) || user.deptName
        };

        deptGroups[displayDeptId].users.push(userWithMark);
      });

      // 转换为数组并排序
      this.departmentGroups = Object.values(deptGroups).sort((a, b) => {
        return a.deptName.localeCompare(b.deptName);
      });

      // 为每个部门组添加配额信息
      this.enrichDepartmentGroupsWithQuota();

      // 设置默认激活的选项卡为第一个部门
      if (this.departmentGroups.length > 0) {
        this.activeDeptTab = this.departmentGroups[0].deptId.toString();
      }
    },

    // 获取所有部门信息
    getAllDepartments() {
      return new Promise((resolve, reject) => {
        listDept({}).then(response => {
          resolve(response.data || []);
        }).catch(error => {
          resolve([]);
        });
      });
    },

    // 为部门组添加配额信息
    enrichDepartmentGroupsWithQuota() {
      this.departmentGroups.forEach(deptGroup => {
        const quotaInfo = this.quotaInfoList.find(q => q.deptId === deptGroup.deptId);
        if (quotaInfo) {
          deptGroup.quotaInfo = quotaInfo;
        }
      });
    },

    // 处理部门选项卡点击
    handleDeptTabClick(tab) {

      // 可以在这里添加切换部门时的逻辑
    },

    // 处理高分配额使用
    async handleQuotaUsage(allUsers, evaluatorId) {
      // 检查当前用户是否为子部门负责人，如果是则不使用配额限制
      if (this.isSubDeptLeader()) {

        return;
      }

      const currentYear = new Date().getFullYear().toString();
      const currentMonth = this.queryParams.evaluationMonth;

      if (!currentMonth) {

        return;
      }



      // 处理高分用户的配额使用
      const quotaPromises = [];
      const highScoreUsers = [];

      allUsers.forEach(user => {
        if (user.newScore >= 95) {
          highScoreUsers.push(user);
          // 高分用户，使用配额
          const quotaData = {
            deptId: user.deptId || this.currentDeptId,
            userId: user.userId,
            year: currentYear,
            month: currentMonth,
            score: user.newScore,
            evaluatorId: evaluatorId
          };



          quotaPromises.push(
            useQuota(quotaData)
              .then(response => {
                return response;
              })
              .catch(error => {
                this.$message.error(`用户${user.nickName || user.userName}配额使用失败: ${error.message || error}`);
                throw error; // 重新抛出错误以便上层处理
              })
          );
        }
      });



      // 等待所有配额操作完成
      if (quotaPromises.length > 0) {
        try {
          const results = await Promise.all(quotaPromises);

          this.$message.success(`成功为${quotaPromises.length}个高分用户使用配额`);

          // 立即强制刷新配额信息
          this.forceRefreshQuotaInfo();

          // 额外的延迟刷新作为保险
          setTimeout(() => {
            this.forceRefreshQuotaInfo();
          }, 1000);
        } catch (error) {
          this.$message.error('部分配额使用失败，请检查配额设置');
        }
      }
    },

    // 处理单个评分的配额使用
    async handleSingleQuotaUsage(formData) {
      // 检查当前用户是否为子部门负责人，如果是则不使用配额限制
      if (this.isSubDeptLeader()) {
        return;
      }

      if (formData.score >= 95) {
        const currentYear = new Date().getFullYear().toString();
        const quotaData = {
          deptId: formData.deptId,
          userId: formData.evaluateeId,
          year: currentYear,
          month: formData.evaluationMonth,
          score: formData.score,
          evaluatorId: formData.evaluatorId
        };

        try {
          const response = await useQuota(quotaData);

          this.$message.success('配额使用成功');

          // 立即强制刷新配额信息
          this.forceRefreshQuotaInfo();

          // 额外的延迟刷新作为保险
          setTimeout(() => {
            this.forceRefreshQuotaInfo();
          }, 1000);
        } catch (error) {
          this.$message.error(`配额使用失败: ${error.message || error}`);
        }
      }
    },

    // 检查高分配额
    async checkHighScoreQuota(user) {
      // 检查当前用户是否为子部门负责人，如果是则不使用配额限制
      if (this.isSubDeptLeader()) {

        return true;
      }

      // 获取用户所在部门ID
      const userDeptId = user.deptId || this.currentDeptId;

      // 找到用户所在的部门组
      const deptGroup = this.departmentGroups.find(group => group.deptId === userDeptId);
      if (!deptGroup) {

        return true;
      }

      // 统计该部门当前要提交的高分人数
      const deptHighScoreCount = deptGroup.users.filter(u => u.newScore >= 95).length;

      try {
        const available = await checkQuotaAvailable(userDeptId, this.currentYear, deptHighScoreCount);
        if (!available.data) {
          // 找到对应部门的配额信息
          const deptQuota = this.quotaInfoList.find(q => q.deptId === userDeptId);
          const deptName = deptQuota ? deptQuota.deptName : deptGroup.deptName;
          const quotaLimit = deptQuota ? deptQuota.highScoreQuota : 0;

          this.$message.warning(`${deptName}高分配额已用完！本年度该部门95-100分人员不能超过${quotaLimit}人`);
          // 重置评分
          user.newScore = 94;
          return false;
        }
      } catch (error) {
        // 静默处理配额检查失败
      }
      return true;
    },

    // 打开评价意见对话框
    openCommentsDialog(row) {
      if (!this.queryParams.evaluationMonth) {
        this.$message.warning("请先选择评价月份");
        return;
      }
    
      // 获取当前用户ID作为评价人ID
      const evaluatorId = this.$store.state.user.id;
    
      this.title = "评价详情";
      // 根据当前登录用户的身份确定评价类型
      let evaluationType = "manager"; // 默认为manager类型

      // 如果当前用户是父部门负责人，且被评价用户是子部门成员，则使用parent_manager类型
      if (!this.isSubDeptLeader() && row.isSubDeptMember) {
        evaluationType = "parent_manager";
      }

      this.form = {
        deptId: row.deptId || this.currentDeptId, // 使用用户实际所在的部门ID
        evaluateeId: row.userId, // 使用userId作为evaluateeId
        evaluatorId: evaluatorId, // 添加评价人ID
        userName: row.nickName || row.userName,
        score: row.newScore || 0,
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluationType: evaluationType,
        comments: row.comments || ""
      };
      
      if (row.evaluationId) {
        this.form.id = row.evaluationId;
      }
      
      this.open = true;
    },
    
    // 提交评价表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 获取当前用户ID作为评价人ID
          const evaluatorId = this.$store.state.user.id;
          if (!evaluatorId) {
            this.$message.error("获取当前用户信息失败，请重新登录后再试");
            return;
          }
          
          // 设置评价人ID
          this.form.evaluatorId = evaluatorId;
          
          // 更新用户的评分和评价
          let targetUser = null;
          this.departmentGroups.forEach(deptGroup => {
            const user = deptGroup.users.find(u => u.userId === this.form.evaluateeId);
            if (user) {
              targetUser = user;
            }
          });

          if (targetUser) {
            targetUser.newScore = this.form.score;
            targetUser.comments = this.form.comments;
          }
          
          // 添加默认projectId
          this.form.projectId = 0;
          
          if (this.form.id) {
            // 更新评价
            updateEvaluation(this.form).then(response => {
              this.$message.success("修改评价成功");
              this.open = false;

              this.getExistingEvaluations();

              // 强制刷新配额信息
              setTimeout(() => {
                this.forceRefreshQuotaInfo();
              }, 300);

              // 自动计算评价结果（针对修改的用户）
              this.autoCalculateModifiedUserResult(this.form.evaluateeId);
            });
          } else {
            // 新增评价
            addEvaluation(this.form).then(response => {
              this.$message.success("新增评价成功");
              this.open = false;

              this.getExistingEvaluations();

              // 强制刷新配额信息
              setTimeout(() => {
                this.forceRefreshQuotaInfo();
              }, 300);

              // 自动计算评价结果
              this.autoCalculateEvaluationResults();
            });
          }
        }
      });
    },
    
    // 批量保存评价 - 按当前激活部门进行提交
    batchSaveEvaluations() {
      // 防重复提交检查
      if (this.isSubmitting) {
        this.$message.warning("数据正在处理中，请勿重复提交");
        return;
      }

      if (!this.queryParams.evaluationMonth) {
        this.$message.warning("请先选择评价月份");
        return;
      }

      // 获取当前激活的部门ID
      const currentDeptId = Number(this.activeDeptTab);
      if (!currentDeptId) {
        this.$message.warning("请先选择要提交的部门");
        return;
      }

      // 获取当前用户ID作为评价人ID
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$message.error("获取当前用户信息失败，请重新登录后再试");
        return;
      }

      // 设置提交状态
      this.isSubmitting = true;
      // 首先查询当前月份的评价记录 - 支持多评价类型和评分人过滤
      this.loading = true;

      // 需要查询两种评价类型：manager 和 parent_manager
      const managerParams = {
        evaluationType: "manager",
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluatorId: evaluatorId,
      };

      const parentManagerParams = {
        evaluationType: "parent_manager",
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluatorId: evaluatorId,
      };

      // 并行查询两种评价类型
      Promise.all([
        listEvaluation(managerParams),
        listEvaluation(parentManagerParams)
      ]).then(responses => {
        const managerEvaluations = responses[0].rows || [];
        const parentManagerEvaluations = responses[1].rows || [];

        // 合并两种评价类型的结果
        const allEvaluations = [...managerEvaluations, ...parentManagerEvaluations];

        // 创建一个映射表，使用 evaluateeId + evaluationType 作为键
        const evaluationMap = {};
        allEvaluations.forEach(evaluation => {
          if (evaluation.evaluateeId !== null && evaluation.evaluateeId !== undefined) {
            const evaluateeId = Number(evaluation.evaluateeId);
            if (!isNaN(evaluateeId) && evaluation.evaluatorId === evaluatorId) {
              // 使用 evaluateeId + evaluationType 作为键，确保能区分不同类型的评价
              const key = `${evaluateeId}_${evaluation.evaluationType}`;
              evaluationMap[key] = evaluation;
            }
          }
        });
        
        // 准备保存的评价列表
        const promises = [];
        const updatedUsers = [];
        
        // 只处理当前激活部门的用户
        const currentDeptGroup = this.departmentGroups.find(group => group.deptId === currentDeptId);
        if (!currentDeptGroup) {
          this.$message.error("未找到当前部门的用户数据");
          this.loading = false;
          return;
        }

        const currentDeptName = currentDeptGroup.deptName;


        // 收集当前部门的用户
        const allUsers = currentDeptGroup.users;

        // 收集需要提交的用户（排除禁用的用户和无效评分用户）
        const usersToSubmit = [];
        const invalidScoreUsers = [];

        allUsers.forEach(user => {
          const userId = Number(user.userId);


          // 检查是否为无效评分（空值、0分或负数）
          if (!isNaN(userId) && !user.scoreInputDisabled) {
            if (user.newScore === null || user.newScore === undefined || user.newScore === 0 || user.newScore < 0) {
              invalidScoreUsers.push(user.nickName || user.userName);
            } else if (user.newScore > 0) {
              // 只处理未禁用且有有效评分（大于0）的用户
              usersToSubmit.push(user);
            }
          }
        });

        // 如果有无效评分用户，提示用户
        if (invalidScoreUsers.length > 0) {
          this.$message.warning(`部门 ${currentDeptName} 中以下用户评分无效（为空或0分），无法提交：${invalidScoreUsers.join('、')}。请为这些用户设置有效评分后再提交。`);
          this.loading = false;
          return;
        }



        // 如果没有需要提交的用户，直接返回
        if (usersToSubmit.length === 0) {
          this.$message.warning(`部门 ${currentDeptName} 没有可以提交的评价（所有用户都被禁用或无有效评分）`);
          this.loading = false;
          return;
        }

        // 直接处理评分，不需要再次验证
        this.processBatchEvaluation(usersToSubmit, currentDeptName);
      }).catch(error => {
        this.$message.error("获取评价记录失败：" + error);
        this.loading = false;
        this.isSubmitting = false; // 重置提交状态
      });
    },
    
    // 刷新评分显示
    refreshEvaluationDisplay(updatedUserIds) {
      // 标记所有已更新用户的评分为已保存状态
      this.departmentGroups.forEach(deptGroup => {
        deptGroup.users.forEach((user, index) => {
          const userId = Number(user.userId);
          if (!isNaN(userId) && updatedUserIds.includes(userId)) {
            // 可以在这里添加一些视觉效果，如果需要
          }
        });
      });

      this.loading = false;
      this.isSubmitting = false; // 重置提交状态
    },

    // 自动计算评价结果
    autoCalculateEvaluationResults() {
      const evaluationMonth = this.queryParams.evaluationMonth;
      if (!evaluationMonth) {

        return;
      }

      // 收集所有需要计算的用户ID
      const userIds = [];
      this.departmentGroups.forEach(deptGroup => {
        deptGroup.users.forEach(user => {
          if (user.userId) {
            userIds.push(Number(user.userId));
          }
        });
      });

      if (userIds.length === 0) {

        return;
      }



      // 延迟执行，确保评分数据已保存
      setTimeout(() => {
        calculateUserEvaluationResults({
          evaluationMonth: evaluationMonth,
          userIds: userIds
        })
          .then(response => {

            this.$message.success('评分已保存，评价结果已自动计算完成');

            // 自动刷新所有用户的最终评分
            this.refreshAllUsersFinalScore();
          })
          .catch(error => {
            this.$message.warning('评分已保存，但自动计算评价结果失败，请手动计算');
          });
      }, 1000); // 延迟1秒执行，确保数据库事务完成
    },

    // 自动计算修改用户的评价结果（针对单个用户）
    autoCalculateModifiedUserResult(userId) {
      const evaluationMonth = this.queryParams.evaluationMonth;
      if (!evaluationMonth) {

        return;
      }

      if (!userId) {

        return;
      }

      const userIdNum = Number(userId);


      // 延迟执行，确保评分数据已保存
      setTimeout(() => {
        calculateUserEvaluationResults({
          evaluationMonth: evaluationMonth,
          userIds: [userIdNum]
        })
          .then(response => {

            this.$message.success('评分已保存，评价结果已自动计算完成');

            // 刷新该用户的最终评分
            const user = this.findUserById(userIdNum);
            if (user) {
              this.refreshFinalScore(user);
            }
          })
          .catch(error => {
            this.$message.warning('评分已保存，但自动计算评价结果失败，请手动计算');
          });
      }, 1000); // 延迟1秒执行，确保数据库事务完成
    },

    // 根据用户ID查找用户对象
    findUserById(userId) {
      for (const deptGroup of this.departmentGroups) {
        for (const user of deptGroup.users) {
          if (Number(user.userId) === Number(userId)) {
            return user;
          }
        }
      }
      return null;
    },

    // 自动计算提交用户的评价结果（针对批量提交）
    autoCalculateSubmittedUsersResults(submittedUsers) {
      const evaluationMonth = this.queryParams.evaluationMonth;
      if (!evaluationMonth) {

        return;
      }

      if (!submittedUsers || submittedUsers.length === 0) {

        return;
      }

      // 提取用户ID
      const userIds = submittedUsers.map(user => Number(user.userId)).filter(id => !isNaN(id));

      if (userIds.length === 0) {

        return;
      }



      // 延迟执行，确保评分数据已保存
      setTimeout(() => {
        calculateUserEvaluationResults({
          evaluationMonth: evaluationMonth,
          userIds: userIds
        })
          .then(response => {

            this.$message.success('评分已保存，评价结果已自动计算完成');

            // 刷新这些用户的最终评分
            userIds.forEach(userId => {
              const user = this.findUserById(userId);
              if (user) {
                setTimeout(() => {
                  this.refreshFinalScore(user);
                }, Math.random() * 1000); // 随机延迟，避免并发过多
              }
            });
          })
          .catch(error => {
            this.$message.warning('评分已保存，但自动计算评价结果失败，请手动计算');
          });
      }, 1000); // 延迟1秒执行，确保数据库事务完成
    },

    // 获取用户最终评分信息
    async getUserFinalScore(userId, evaluationMonth) {
      try {
        const response = await listEvaluation_result({
          userId: userId,
          evaluationMonth: evaluationMonth,
          pageNum: 1,
          pageSize: 1
        });

        if (response.rows && response.rows.length > 0) {
          return response.rows[0];
        }
        return null;
      } catch (error) {
        return null;
      }
    },

    // 刷新单个用户的最终评分
    async refreshFinalScore(user) {
      if (!user || !user.userId) {
        return;
      }

      this.$set(user, 'scoreLoading', true);

      try {
        const finalScoreInfo = await this.getUserFinalScore(user.userId, this.queryParams.evaluationMonth);
        this.$set(user, 'finalScoreInfo', finalScoreInfo);


      } catch (error) {
        this.$message.error('刷新最终评分失败');
      } finally {
        this.$set(user, 'scoreLoading', false);
      }
    },

    // 刷新所有用户的最终评分
    refreshAllUsersFinalScore() {
      this.departmentGroups.forEach(deptGroup => {
        deptGroup.users.forEach((user, index) => {
          // 延迟刷新，避免并发请求过多
          setTimeout(() => {
            this.refreshFinalScore(user);
          }, index * 200); // 每个用户延迟200ms，避免并发过多
        });
      });
    },



    // 获取评分样式类
    getScoreClass(score) {
      if (!score) return '';
      const numScore = Number(score);
      if (numScore >= 95) {
        return 'score-excellent';
      } else if (numScore >= 85) {
        return 'score-good';
      } else if (numScore >= 70) {
        return 'score-average';
      } else {
        return 'score-poor';
      }
    },
    
    // 取消对话框
    cancel() {
      this.open = false;
      this.form = {
        deptId: this.currentDeptId,
        evaluateeId: null,
        evaluatorId: this.$store.state.user.id, // 保留当前用户ID作为评价人ID
        userName: null,
        score: null,
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluationType: "manager",
        comments: null
      };
    },
    
    // 处理表格展开事件 - 懒加载数据
    handleExpandChange(row, expandedRows) {
      // 如果是展开操作，则按需加载该用户的项目评分数据和最终评分
      if (expandedRows.some(item => item.userId === row.userId)) {
        // 加载该成员的项目评价数据，直接传递整个row对象
        this.loadUserProjectEvaluations(row)
          .catch(error => {
            // 处理错误
          });

        // 同时加载最终评分信息
        this.refreshFinalScore(row);
      }
    },
    
    // 刷新用户项目数据
    refreshUserProjectData(row) {
      if (!row || !row.userId) {
        this.$message.error("无效的用户数据");
        return;
      }
      
      // 重置数据加载状态
      const userIndex = this.deptUsers.findIndex(u => u.userId === row.userId);
      if (userIndex !== -1) {
        this.$set(this.deptUsers[userIndex], 'dataLoaded', false);
        // 重新加载数据
        this.loadUserProjectEvaluations(row);
      }
    },
    
    // 完全刷新数据
    refreshAllData() {
      this.$confirm('确定要刷新数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        this.initialized = false;
        this.deptUsers = [];
        this.existingEvaluations = {};
        
        // 重新初始化所有数据
        setTimeout(() => {
          this.initData();
        }, 100);
      }).catch(() => {
        // 用户取消操作
      });
    },

    // 标签页切换事件
    handleTabClick(tab) {
      if (tab.name === 'bonus') {
        // 切换到奖金分配时，根据当前激活的子标签页加载对应数据
        if (this.activeBonusTab === 'monthly') {
          this.loadBonusData();
        } else if (this.activeBonusTab === 'quarterly') {
          this.loadQuarterlyBonusData();
        }
      }
    },

    // 评价类型子标签页切换事件
    handleEvaluationTabClick(tab) {
      if (tab.name === 'quarterly') {
        // 切换到季度评价时，加载季度评价数据
        this.loadQuarterlyEvaluationData();
      }
    },

    // 奖金分配类型子标签页切换事件
    handleBonusTabClick(tab) {
      if (tab.name === 'quarterly') {
        // 切换到季度奖金分配时，加载季度奖金分配数据
        this.loadQuarterlyBonusData();
      } else if (tab.name === 'monthly') {
        // 切换到月度奖金分配时，加载月度奖金分配数据
        this.loadBonusData();
      }
    },

    // 加载季度评价数据
    loadQuarterlyEvaluationData() {
      this.quarterlyLoading = true;
      
      try {
        // 复制月度评价的用户数据到季度评价，但使用独立的数据结构
        this.quarterlyDepartmentGroups = this.departmentGroups.map(deptGroup => ({
          ...deptGroup,
          users: deptGroup.users.map(user => ({
            ...user,
            // 季度评价独立的数据字段
            quarterlyNewScore: null,
            quarterlyComments: "",
            quarterlyEvaluationId: null,
            quarterlyProjectEvaluations: [],
            quarterlyDataLoaded: false,
            quarterlyFinalScoreInfo: null,
            quarterlyScoreLoading: false,
            quarterlyScoreInputDisabled: false,
            quarterlyDisabledTooltip: ""
          }))
        }));

        // 设置默认激活的季度部门选项卡
        if (this.quarterlyDepartmentGroups.length > 0) {
          this.activeQuarterlyDeptTab = this.quarterlyDepartmentGroups[0].deptId.toString();
        }

        // 获取季度评价的已有评分记录
        this.getQuarterlyExistingEvaluations();
        
      } catch (error) {
        this.$message.error("加载季度评价数据失败：" + (error.message || error));
      } finally {
        this.quarterlyLoading = false;
      }
    },

    // 获取季度评价的已有评分记录
    getQuarterlyExistingEvaluations() {
      // 获取当前登录用户ID
      const currentUserId = this.$store.state.user.id;

      // 查询季度评价记录，使用固定的2022-08月份和季度专用评价类型
      const managerParams = {
        evaluationType: "quarterly_manager",
        evaluationMonth: this.quarterlyEvaluationMonth,
        evaluatorId: currentUserId,
      };

      const parentManagerParams = {
        evaluationType: "quarterly_parent_manager",
        evaluationMonth: this.quarterlyEvaluationMonth,
        evaluatorId: currentUserId,
      };

      // 并行查询两种评价类型
      Promise.all([
        listEvaluation(managerParams),
        listEvaluation(parentManagerParams)
      ]).then(responses => {
        const managerEvaluations = responses[0].rows || [];
        const parentManagerEvaluations = responses[1].rows || [];

        // 合并两种评价类型的结果
        const allEvaluations = [...managerEvaluations, ...parentManagerEvaluations];

        // 创建一个映射表，使用 evaluateeId + evaluationType 作为键
        const evaluationMap = {};
        allEvaluations.forEach(evaluation => {
          if (evaluation.evaluateeId !== null && evaluation.evaluateeId !== undefined) {
            const evaluateeId = Number(evaluation.evaluateeId);
            if (!isNaN(evaluateeId) && evaluation.evaluatorId === currentUserId) {
              const key = `${evaluateeId}_${evaluation.evaluationType}`;
              evaluationMap[key] = evaluation;
            }
          }
        });

        // 更新季度评价用户的评分
        this.quarterlyDepartmentGroups.forEach((deptGroup, deptIndex) => {
          deptGroup.users.forEach((user, userIndex) => {
            const userId = Number(user.userId);
            
            if (!isNaN(userId)) {
              // 根据当前登录用户的身份确定季度评价类型
              let evaluationType = "quarterly_manager"; // 默认为quarterly_manager类型

              // 如果当前用户是父部门负责人，且被评价用户是子部门成员，则使用quarterly_parent_manager类型
              if (!this.isSubDeptLeader() && user.isSubDeptMember) {
                evaluationType = "quarterly_parent_manager";
              }

              const key = `${userId}_${evaluationType}`;

              if (evaluationMap[key]) {
                const evaluation = evaluationMap[key];

                // 处理评分，确保是数字类型
                let score = null;
                if (evaluation.score !== null && evaluation.score !== undefined) {
                  if (typeof evaluation.score === 'object' && evaluation.score.toFixed) {
                    score = Number(evaluation.score.toFixed(0));
                  } else {
                    score = Number(evaluation.score);
                  }

                  if (isNaN(score)) {
                    score = null;
                  }
                }

                // 更新季度评价用户的评分和评价信息
                this.$set(this.quarterlyDepartmentGroups[deptIndex].users[userIndex], 'quarterlyNewScore', score);
                this.$set(this.quarterlyDepartmentGroups[deptIndex].users[userIndex], 'quarterlyComments', evaluation.comments || "");
                this.$set(this.quarterlyDepartmentGroups[deptIndex].users[userIndex], 'quarterlyEvaluationId', evaluation.id);
              } else {
                // 重置未评价的用户数据
                this.$set(this.quarterlyDepartmentGroups[deptIndex].users[userIndex], 'quarterlyNewScore', null);
                this.$set(this.quarterlyDepartmentGroups[deptIndex].users[userIndex], 'quarterlyComments', "");
                this.$set(this.quarterlyDepartmentGroups[deptIndex].users[userIndex], 'quarterlyEvaluationId', null);
              }
            }
          });
        });

      }).catch(error => {
        this.$message.error("获取季度评价记录失败：" + error);
      });
    },

    // 处理季度评价部门选项卡点击
    handleQuarterlyDeptTabClick(tab) {
      // 可以在这里添加切换季度部门时的逻辑
    },

    // 处理季度评价评分变更
    handleQuarterlyScoreChange(row) {
      // 如果不为空，确保是整数值
      if (row.quarterlyNewScore !== null && row.quarterlyNewScore !== undefined) {
        row.quarterlyNewScore = Math.round(row.quarterlyNewScore);
        if (row.quarterlyNewScore < 0) row.quarterlyNewScore = 0;
        if (row.quarterlyNewScore > 100) row.quarterlyNewScore = 100;

        // 检查高分配额（季度评价使用相同的配额检查逻辑）
        if (row.quarterlyNewScore >= 95) {
          this.checkQuarterlyHighScoreQuota(row);
        }
      }

      this.hasUnsavedChanges = true;
    },

    // 检查季度评价高分配额
    async checkQuarterlyHighScoreQuota(user) {
      // 季度评价不受高分配额限制，直接返回true
      return true;
    },

    // 处理季度评价表格展开事件
    handleQuarterlyExpandChange(row, expandedRows) {
      // 如果是展开操作，则按需加载该用户的季度项目评分数据和最终评分
      if (expandedRows.some(item => item.userId === row.userId)) {
        // 加载该成员的季度项目评价数据
        this.loadQuarterlyUserProjectEvaluations(row)
          .catch(error => {
            // 处理错误
          });

        // 同时加载季度最终评分信息
        this.refreshQuarterlyFinalScore(row);
      }
    },

    // 加载季度用户项目评分数据
    loadQuarterlyUserProjectEvaluations(row) {
      if (!row || !row.userId) {
        this.$message.error("无效的用户数据");
        return Promise.reject("无效的用户数据");
      }

      // 查找用户在季度评价部门组中的位置
      let userRef = null;
      let deptGroupIndex = -1;
      let userIndex = -1;

      for (let i = 0; i < this.quarterlyDepartmentGroups.length; i++) {
        const group = this.quarterlyDepartmentGroups[i];
        const index = group.users.findIndex(u => u.userId === row.userId);
        if (index !== -1) {
          userRef = group.users[index];
          deptGroupIndex = i;
          userIndex = index;
          break;
        }
      }

      if (!userRef) {
        this.$message.error("未找到用户: " + row.userName);
        return Promise.reject("用户不存在");
      }

      // 标记加载状态
      this.$set(this.quarterlyDepartmentGroups[deptGroupIndex].users[userIndex], 'loading', true);

      // 如果数据已加载且不是强制刷新，则不重复加载
      if (userRef.quarterlyDataLoaded) {
        this.$set(this.quarterlyDepartmentGroups[deptGroupIndex].users[userIndex], 'loading', false);
        return Promise.resolve(userRef.quarterlyProjectEvaluations);
      }

      // 获取用户ID和季度评价月份
      const userId = row.userId;
      const evaluationMonth = this.quarterlyEvaluationMonth; // 使用固定的2022-08

      // 创建一个Promise来处理数据加载
      return new Promise((resolve, reject) => {
        // 同时获取精力分配数据和项目评分数据
        Promise.all([
          // 获取精力分配数据（使用2022-08月份）
          this.getUserMonthlyEffort(row.userName, evaluationMonth),
          // 获取项目评分数据（使用2022-08月份）
          getUserProjectEvaluations(userId, evaluationMonth)
        ]).then(([effortResponse, evaluationResponse]) => {
          let projectData = [];

          // 处理精力分配数据
          if (effortResponse && effortResponse.data && Array.isArray(effortResponse.data)) {
            const effortMap = {};
            effortResponse.data.forEach(item => {
              if (item.projectId && parseFloat(item.participationRate) > 0) {
                effortMap[item.projectId] = {
                  projectId: item.projectId,
                  projectName: item.projectName,
                  participationRate: item.participationRate,
                  role: item.role || '参与',
                  hasEffort: true
                };
              }
            });

            // 处理项目评分数据
            const evaluationMap = {};
            if (evaluationResponse && evaluationResponse.data) {
              evaluationResponse.data.forEach(item => {
                if (item.projectId) {
                  evaluationMap[item.projectId] = {
                    score: item.score,
                    evaluationId: item.id,
                    hasEvaluation: true
                  };
                }
              });
            }

            // 合并数据：以精力分配为主，补充评分信息
            Object.keys(effortMap).forEach(projectId => {
              const effort = effortMap[projectId];
              const evaluation = evaluationMap[projectId] || {};

              projectData.push({
                projectId: effort.projectId,
                projectName: effort.projectName,
                participationRate: effort.participationRate,
                role: effort.role,
                score: evaluation.score || null,
                hasEvaluation: evaluation.hasEvaluation || false,
                evaluationStatus: this.getEvaluationStatus(effort.role, evaluation.hasEvaluation)
              });
            });
          }

          // 更新用户的季度项目数据
          this.$set(this.quarterlyDepartmentGroups[deptGroupIndex].users[userIndex], 'quarterlyProjectEvaluations', projectData);
          this.$set(this.quarterlyDepartmentGroups[deptGroupIndex].users[userIndex], 'quarterlyDataLoaded', true);
          this.$set(this.quarterlyDepartmentGroups[deptGroupIndex].users[userIndex], 'loading', false);

          resolve(projectData);
        }).catch(error => {
          // 出错时设置空数据
          this.$set(this.quarterlyDepartmentGroups[deptGroupIndex].users[userIndex], 'quarterlyProjectEvaluations', []);
          this.$set(this.quarterlyDepartmentGroups[deptGroupIndex].users[userIndex], 'quarterlyDataLoaded', true);
          this.$set(this.quarterlyDepartmentGroups[deptGroupIndex].users[userIndex], 'loading', false);

          this.$message.error("获取季度项目数据失败: " + (error.message || error));
          resolve([]);
        });
      });
    },

    // 刷新季度单个用户的最终评分
    async refreshQuarterlyFinalScore(user) {
      if (!user || !user.userId) {
        return;
      }

      this.$set(user, 'quarterlyScoreLoading', true);

      try {
        const finalScoreInfo = await this.getUserFinalScore(user.userId, this.quarterlyEvaluationMonth);
        this.$set(user, 'quarterlyFinalScoreInfo', finalScoreInfo);
      } catch (error) {
        this.$message.error('刷新季度最终评分失败');
      } finally {
        this.$set(user, 'quarterlyScoreLoading', false);
      }
    },

    // 批量保存季度评价
    batchSaveQuarterlyEvaluations() {
      // 防重复提交检查
      if (this.isSubmitting) {
        this.$message.warning("数据正在处理中，请勿重复提交");
        return;
      }

      // 获取当前激活的季度部门ID
      const currentDeptId = Number(this.activeQuarterlyDeptTab);
      if (!currentDeptId) {
        this.$message.warning("请先选择要提交的部门");
        return;
      }

      // 获取当前用户ID作为评价人ID
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$message.error("获取当前用户信息失败，请重新登录后再试");
        return;
      }

      // 设置提交状态
      this.isSubmitting = true;
      this.quarterlyLoading = true;

      // 只处理当前激活季度部门的用户
      const currentDeptGroup = this.quarterlyDepartmentGroups.find(group => group.deptId === currentDeptId);
      if (!currentDeptGroup) {
        this.$message.error("未找到当前部门的用户数据");
        this.quarterlyLoading = false;
        this.isSubmitting = false;
        return;
      }

      const currentDeptName = currentDeptGroup.deptName;

      // 收集需要提交的用户（排除禁用的用户和无效评分用户）
      const usersToSubmit = [];
      const invalidScoreUsers = [];

      currentDeptGroup.users.forEach(user => {
        const userId = Number(user.userId);

        // 检查是否为无效评分（空值、0分或负数）
        if (!isNaN(userId) && !user.quarterlyScoreInputDisabled) {
          if (user.quarterlyNewScore === null || user.quarterlyNewScore === undefined || user.quarterlyNewScore === 0 || user.quarterlyNewScore < 0) {
            invalidScoreUsers.push(user.nickName || user.userName);
          } else if (user.quarterlyNewScore > 0) {
            // 只处理未禁用且有有效评分（大于0）的用户
            usersToSubmit.push(user);
          }
        }
      });

      // 如果有无效评分用户，提示用户
      if (invalidScoreUsers.length > 0) {
        this.$message.warning(`部门 ${currentDeptName} 中以下用户季度评分无效（为空或0分），无法提交：${invalidScoreUsers.join('、')}。请为这些用户设置有效评分后再提交。`);
        this.quarterlyLoading = false;
        this.isSubmitting = false;
        return;
      }

      // 如果没有需要提交的用户，直接返回
      if (usersToSubmit.length === 0) {
        this.$message.warning(`部门 ${currentDeptName} 没有可以提交的季度评价（所有用户都被禁用或无有效评分）`);
        this.quarterlyLoading = false;
        this.isSubmitting = false;
        return;
      }

      // 直接处理季度评分，不需要再次验证
      this.processQuarterlyBatchEvaluation(usersToSubmit, currentDeptName);
    },

    // 处理批量季度评分（提取的公共方法）
    processQuarterlyBatchEvaluation(validUsers, deptName = '') {
      // 获取当前用户ID作为评价人ID
      const evaluatorId = this.$store.state.user.id;

      // 查询季度评价记录 - 使用季度专用评价类型
      const managerParams = {
        evaluationType: "quarterly_manager",
        evaluationMonth: this.quarterlyEvaluationMonth,
        evaluatorId: evaluatorId,
      };

      const parentManagerParams = {
        evaluationType: "quarterly_parent_manager",
        evaluationMonth: this.quarterlyEvaluationMonth,
        evaluatorId: evaluatorId,
      };

      // 并行查询两种评价类型
      Promise.all([
        listEvaluation(managerParams),
        listEvaluation(parentManagerParams)
      ]).then(responses => {
        const managerEvaluations = responses[0].rows || [];
        const parentManagerEvaluations = responses[1].rows || [];

        // 合并两种评价类型的结果
        const allEvaluations = [...managerEvaluations, ...parentManagerEvaluations];

        // 创建一个映射表，使用 evaluateeId + evaluationType 作为键
        const evaluationMap = {};
        allEvaluations.forEach(evaluation => {
          if (evaluation.evaluateeId !== null && evaluation.evaluateeId !== undefined) {
            const evaluateeId = Number(evaluation.evaluateeId);
            if (!isNaN(evaluateeId) && evaluation.evaluatorId === evaluatorId) {
              const key = `${evaluateeId}_${evaluation.evaluationType}`;
              evaluationMap[key] = evaluation;
            }
          }
        });

        // 准备保存的评价列表
        const promises = [];
        const updatedUsers = [];

        validUsers.forEach(user => {
          // 确保用户ID是数字类型
          const userId = Number(user.userId);

          // 只处理有有效季度评分的用户（不包括空值和0分）
          if (!isNaN(userId) && user.quarterlyNewScore !== null && user.quarterlyNewScore !== undefined && user.quarterlyNewScore > 0) {
            // 标记该用户已处理
            updatedUsers.push(userId);

            // 使用用户所在的部门ID
            const userDeptId = user.deptId || this.currentDeptId;

            // 根据当前登录用户的身份确定评价类型
            // 季度评价使用专门的评价类型，避免触发自动生成项目负责人自评替代记录
            let evaluationType = "quarterly_manager"; // 季度评价专用类型

            // 如果当前用户是父部门负责人，且被评价用户是子部门成员，则使用季度父部门负责人类型
            if (!this.isSubDeptLeader() && user.isSubDeptMember) {
              evaluationType = "quarterly_parent_manager";
            }

            // 准备季度评价数据
            const evaluation = {
              deptId: userDeptId,
              evaluateeId: userId,
              evaluatorId: evaluatorId,
              score: user.quarterlyNewScore,
              evaluationMonth: this.quarterlyEvaluationMonth, // 使用固定的2022-08
              evaluationType: evaluationType,
              comments: user.quarterlyComments || "季度评价",
              projectId: 0 // 默认项目ID
            };

            // 检查是否已有评价记录 - 根据用户类型和评价类型查找
            const evaluationKey = `${userId}_${evaluationType}`;
            const existingEval = evaluationMap[evaluationKey];

            if (existingEval) {
              // 更新已有评价
              evaluation.id = existingEval.id;
              promises.push(updateEvaluation(evaluation));
            } else {
              // 新增评价
              promises.push(addEvaluation(evaluation));
            }
          }
        });

        // 如果没有要保存的评价，直接返回
        if (promises.length === 0) {
          this.$message.warning("没有需要保存的季度评价");
          this.quarterlyLoading = false;
          this.isSubmitting = false;
          return;
        }

        // 批量保存季度评价
        Promise.all(promises)
          .then(results => {
            const successMsg = deptName ?
              `部门 ${deptName} 成功保存 ${promises.length} 条季度评价记录` :
              `成功保存 ${promises.length} 条季度评价记录`;
            this.$message.success(successMsg);
            this.hasUnsavedChanges = false;

            // 季度评价不处理高分配额，不调用配额相关接口
            // 季度评价不需要计算评价结果，只提交评分即可

            // 重新加载季度评价数据
            setTimeout(() => {
              this.getQuarterlyExistingEvaluations();
            }, 300);
          })
          .catch(error => {
            this.$message.error("季度评价提交失败：" + error);
          })
          .finally(() => {
            this.quarterlyLoading = false;
            this.isSubmitting = false;
          });
      }).catch(error => {
        this.$message.error("获取季度评价记录失败：" + error);
        this.quarterlyLoading = false;
        this.isSubmitting = false;
      });
    },

    // 季度评价不需要计算评价结果，已移除相关方法

    // 季度评价相关查找方法已移除

    // 导出季度部门数据
    async exportQuarterlyDeptData(deptGroup) {
      if (!deptGroup || !deptGroup.deptId) {
        this.$message.warning("部门信息不完整");
        return;
      }

      // 检查是否有显示的用户数据
      if (!deptGroup.users || deptGroup.users.length === 0) {
        this.$message.warning("当前部门没有显示的用户数据");
        return;
      }

      this.quarterlyLoading = true;

      try {
        // 收集当前表格中显示的用户ID
        const displayedUserIds = deptGroup.users.map(user => user.userId);

        console.log(`导出部门 ${deptGroup.deptName} 的季度数据，包含 ${displayedUserIds.length} 个用户:`, displayedUserIds);

        // 调用后端导出接口，传入用户ID列表（使用季度评价月份）
        const response = await exportDeptEvaluationData(
          deptGroup.deptId,
          this.quarterlyEvaluationMonth, // 使用固定的2022-08
          deptGroup.deptName,
          displayedUserIds
        );

        // 创建下载链接
        const blob = new Blob([response], { type: 'text/csv;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${deptGroup.deptName}_季度评分数据_${this.quarterlyEvaluationMonth}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success(`季度数据导出成功，共导出 ${displayedUserIds.length} 个用户的数据`);

      } catch (error) {
        this.$message.error('导出季度数据失败：' + (error.message || '未知错误'));
      } finally {
        this.quarterlyLoading = false;
      }
    },
    loadBonusData() {
      if (!this.queryParams.evaluationMonth) {
        this.$message.warning("请先选择评价月份");
        return;
      }

      if (!this.leaderDepts || this.leaderDepts.length === 0) {
        this.$message.error("无法获取部门信息");
        return;
      }

      this.bonusLoading = true;

      // 获取所有负责部门的奖金分配状态
      this.loadAllDeptBonusStatus()
        .then(() => {
          // 获取所有负责部门的员工列表（包含奖金分配信息）
          return this.getAllDeptEmployeesWithBonusInfo();
        })
        .then(employees => {
          if (employees) {
            this.bonusEmployees = employees;
          }
          this.bonusLoading = false;
        })
        .catch(error => {
          this.$message.error("加载奖金分配数据失败：" + (error.message || error));
          this.bonusLoading = false;
        });
    },

    // 加载所有部门的奖金分配状态
    loadAllDeptBonusStatus() {
      return new Promise((resolve, reject) => {
        const promises = this.leaderDepts.map(deptId => {
          return getBonusStatus(deptId, this.queryParams.evaluationMonth)
            .then(response => {
              const bonusStatus = response.data;
              // 添加部门ID到状态对象中
              bonusStatus.deptId = deptId;

              // 从部门组中获取部门名称
              const deptGroup = this.departmentGroups.find(group => group.deptId === deptId);
              bonusStatus.deptName = deptGroup ? deptGroup.deptName : '';

              return bonusStatus;
            })
            .catch(error => {
              // 检查是否是权限错误
              if (error.message && error.message.includes('无权限')) {
                // 权限错误，返回null，后续会被过滤掉
                return null;
              } else {
                // 其他错误，返回默认状态

                // 从部门组中获取部门名称
                const deptGroup = this.departmentGroups.find(group => group.deptId === deptId);
                const deptName = deptGroup ? deptGroup.deptName : '';

                // 返回一个默认的状态对象
                return {
                  deptId: deptId,
                  hasBonusAllocation: false,
                  totalBonus: 0,
                  allocatedBonus: 0,
                  remainingBonus: 0,
                  deptName: deptName
                };
              }
            });
        });

        Promise.all(promises)
          .then(statusList => {
            // 过滤掉null值（权限错误的部门）和没有奖金分配的部门
            this.deptBonusStatusList = statusList.filter(status =>
              status !== null && status.hasBonusAllocation && status.totalBonus > 0);

            // 设置主要的bonusStatus为第一个有奖金分配的部门，或者第一个部门
            const filteredStatusList = this.deptBonusStatusList;
            const primaryStatus = filteredStatusList.find(status => status && status.hasBonusAllocation) || filteredStatusList[0];
            if (primaryStatus) {
              this.bonusStatus = primaryStatus;
              this.deptBonusId = primaryStatus.deptBonusId;
            }

            resolve(filteredStatusList);
          })
          .catch(reject);
      });
    },

    // 获取所有负责部门的员工并包含奖金分配信息
    getAllDeptEmployeesWithBonusInfo() {
      return new Promise((resolve, reject) => {
        // 过滤出有奖金分配的部门
        const deptsWithBonus = this.deptBonusStatusList
          .filter(status => status.hasBonusAllocation && status.totalBonus > 0)
          .map(status => status.deptId);

        if (deptsWithBonus.length === 0) {
          // 如果没有部门有奖金分配，返回空数组
          resolve([]);
          return;
        }

        // 获取有奖金分配的部门的员工
        const allEmployees = {};
        let completedRequests = 0;
        const totalRequests = deptsWithBonus.length;

        deptsWithBonus.forEach(deptId => {
          getDeptEmployees(deptId, this.queryParams.evaluationMonth)
            .then(response => {
              const employees = response.data || [];

              // 添加员工到集合中，避免重复，保留奖金分配信息
              employees.forEach(employee => {
                if (!allEmployees[employee.userId]) {
                  allEmployees[employee.userId] = {
                    ...employee,
                    bonusAmount: employee.bonusAmount || 0,
                    allocationReason: employee.allocationReason || ''
                  };
                } else {
                  // 如果员工已存在，更新奖金信息（保留已分配的奖金）
                  if (employee.bonusAmount && employee.bonusAmount > 0) {
                    allEmployees[employee.userId].bonusAmount = employee.bonusAmount;
                    allEmployees[employee.userId].allocationReason = employee.allocationReason || '';
                  }
                }
              });

              // 检查是否所有请求都完成了
              completedRequests++;
              if (completedRequests === totalRequests) {
                // 将员工集合转为数组，奖金分配包含部门负责人
                const filteredEmployees = Object.values(allEmployees);

                resolve(filteredEmployees);
              }
            })
            .catch(error => {
              // 即使出错也计数，确保流程能继续
              completedRequests++;
              if (completedRequests === totalRequests) {
                if (Object.keys(allEmployees).length > 0) {
                  // 奖金分配包含部门负责人
                  const filteredEmployees = Object.values(allEmployees);

                  resolve(filteredEmployees);
                } else {
                  // 如果所有部门都获取失败，返回空数组而不是报错
                  resolve([]);
                }
              }
            });
        });
      });
    },

    // 获取所有负责部门的员工用于奖金分配
    getAllDeptEmployeesForBonus() {
      return new Promise((resolve, reject) => {
        if (!this.leaderDepts || this.leaderDepts.length === 0) {
          // 如果没有负责部门列表，只获取当前部门的员工
          getDeptEmployees(this.currentDeptId, this.queryParams.evaluationMonth)
            .then(response => {
              resolve(response.data || []);
            })
            .catch(reject);
          return;
        }

        // 获取所有负责部门的员工
        const allEmployees = {};
        let completedRequests = 0;
        const totalRequests = this.leaderDepts.length;

        this.leaderDepts.forEach(deptId => {
          getDeptEmployees(deptId, this.queryParams.evaluationMonth)
            .then(response => {
              const employees = response.data || [];

              // 添加员工到集合中，避免重复
              employees.forEach(employee => {
                if (!allEmployees[employee.userId]) {
                  allEmployees[employee.userId] = employee;
                }
              });

              // 检查是否所有请求都完成了
              completedRequests++;
              if (completedRequests === totalRequests) {
                // 将员工集合转为数组，奖金分配包含部门负责人
                const filteredEmployees = Object.values(allEmployees);

                resolve(filteredEmployees);
              }
            })
            .catch(error => {
              this.$message.error(`获取部门(ID:${deptId})员工失败：${error}`);

              // 即使出错也计数，确保流程能继续
              completedRequests++;
              if (completedRequests === totalRequests) {
                if (Object.keys(allEmployees).length > 0) {
                  // 奖金分配包含部门负责人
                  const filteredEmployees = Object.values(allEmployees);
                  resolve(filteredEmployees);
                } else {
                  reject(error);
                }
              }
            });
        });
      });
    },

    // 处理奖金金额变更
    handleBonusChange() {
      // 可以在这里添加实时验证逻辑
    },

    // 获取评分样式类
    getScoreClass(score) {
      if (score >= 95) {
        return 'score-excellent';
      } else if (score >= 85) {
        return 'score-good';
      } else if (score >= 70) {
        return 'score-average';
      } else {
        return 'score-poor';
      }
    },









    // 验证各部门分配金额
    validateDeptAllocations() {
      const deptRemaining = this.deptRemainingAmounts;
      const overAllocatedDepts = [];

      // 检查每个部门是否超额分配
      this.deptBonusStatusList.forEach(status => {
        if (status.hasBonusAllocation) {
          const remaining = deptRemaining[status.deptId];
          if (remaining < 0) {
            overAllocatedDepts.push({
              deptName: status.deptName,
              overAmount: Math.abs(remaining)
            });
          }
        }
      });

      if (overAllocatedDepts.length > 0) {
        const messages = overAllocatedDepts.map(dept =>
          `${dept.deptName}: 超额 ${this.formatMoney(dept.overAmount)}`
        );
        return {
          valid: false,
          message: `以下部门分配金额超出限额：\n${messages.join('\n')}`
        };
      }

      return { valid: true };
    },

    // 提交奖金分配
    submitBonusAllocation() {
      if (!this.canSubmitBonus) {
        this.$message.warning("请检查分配信息");
        return;
      }

      // 按部门验证分配金额
      const validationResult = this.validateDeptAllocations();
      if (!validationResult.valid) {
        this.$message.error(validationResult.message);
        return;
      }

      // 验证总分配金额
      if (Math.abs(this.remainingAmount) > 0.01) {
        this.$confirm(
          `分配金额与部门总奖金不匹配，剩余金额：${this.formatMoney(this.remainingAmount)}，是否继续提交？`,
          '确认提交',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          this.doSubmitBonusAllocation();
        });
      } else {
        this.doSubmitBonusAllocation();
      }
    },

    // 执行奖金分配提交
    doSubmitBonusAllocation() {
      this.bonusLoading = true;

      // 按部门分组员工
      const employeesByDept = {};
      this.bonusEmployees.filter(emp => emp.bonusAmount !== 0).forEach(emp => {
        if (!employeesByDept[emp.deptId]) {
          employeesByDept[emp.deptId] = [];
        }
        employeesByDept[emp.deptId].push({
          userId: emp.userId,
          userName: emp.userName,
          nickName: emp.nickName,
          bonusAmount: emp.bonusAmount,
          allocationReason: emp.allocationReason || '部门负责人分配'
        });
      });

      // 按部门顺序提交（避免并发死锁）
      this.submitBonusByDept(Object.keys(employeesByDept), employeesByDept, 0);
    },

    // 按部门顺序提交月度奖金分配
    submitBonusByDept(deptIds, employeesByDept, index) {
      if (index >= deptIds.length) {
        // 所有部门都提交完成
        this.$message.success("奖金分配提交成功");
        this.loadBonusData();
        return;
      }

      const deptId = deptIds[index];
      const deptBonusStatus = this.deptBonusStatusList.find(status => status.deptId == deptId);

      if (!deptBonusStatus) {
        this.$message.error(`无法找到部门${deptId}的奖金分配状态`);
        this.bonusLoading = false;
        return;
      }

      const submitData = {
        deptBonusId: deptBonusStatus.deptBonusId,
        allocationMonth: this.queryParams.evaluationMonth,
        deptId: deptId,
        remainingBonus: deptBonusStatus.remainingBonus,
        employeeBonusList: employeesByDept[deptId]
      };

      allocateBonus(submitData)
        .then(response => {
          // 提交下一个部门
          this.submitBonusByDept(deptIds, employeesByDept, index + 1);
        })
        .catch(error => {
          this.$message.error(`部门${deptBonusStatus.deptName}奖金分配失败：${error.message || error}`);
          this.bonusLoading = false;
        });
    },

    // 重置奖金分配
    resetBonusAllocation() {
      this.$confirm('确定要重置奖金分配吗？这将删除数据库中已分配的奖金记录，操作不可撤销。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 获取所有有奖金分配的部门ID
        const deptBonusIds = this.deptBonusStatusList
          .filter(status => status.hasBonusAllocation && status.deptBonusId)
          .map(status => status.deptBonusId);

        if (deptBonusIds.length === 0) {
          this.$message.error("无法获取部门奖金分配ID");
          return;
        }

        this.bonusLoading = true;

        // 顺序删除各部门的奖金分配记录（避免并发问题）
        this.resetBonusByDept(deptBonusIds, 0);
      });
    },

    // 按部门顺序重置月度奖金分配
    resetBonusByDept(deptBonusIds, index) {
      if (index >= deptBonusIds.length) {
        // 所有部门都重置完成
        // 重置前端数据
        this.bonusEmployees.forEach(employee => {
          employee.bonusAmount = 0;
          employee.allocationReason = '';
        });

        // 重新获取奖金状态和员工列表
        this.loadBonusData();
        this.$message.success("已重置奖金分配，数据库记录已清除");
        return;
      }

      const deptBonusId = deptBonusIds[index];

      deleteEmployeeBonusByDeptBonusId(deptBonusId)
        .then(response => {
          // 重置下一个部门
          this.resetBonusByDept(deptBonusIds, index + 1);
        })
        .catch(error => {
          this.$message.error("重置失败：" + (error.msg || error.message || error));
          this.bonusLoading = false;
        });
    },

    // 季度奖金分配相关方法
    // 加载季度奖金分配数据
    loadQuarterlyBonusData() {
      this.quarterlyBonusLoading = true;

      // 获取所有负责部门的奖金分配状态（使用固定的2022-07月份）
      this.loadAllQuarterlyDeptBonusStatus()
        .then(() => {
          // 获取所有负责部门的员工列表（包含奖金分配信息）
          return this.getAllQuarterlyDeptEmployeesWithBonusInfo();
        })
        .then(employees => {
          if (employees) {
            this.quarterlyBonusEmployees = employees;
          }
          this.quarterlyBonusLoading = false;
        })
        .catch(error => {
          this.$message.error("加载季度奖金分配数据失败：" + (error.message || error));
          this.quarterlyBonusLoading = false;
        });
    },

    // 加载所有部门的季度奖金分配状态
    loadAllQuarterlyDeptBonusStatus() {
      return new Promise((resolve, reject) => {
        const promises = this.leaderDepts.map(deptId => {
          return getBonusStatus(deptId, this.quarterlyBonusEvaluationMonth) // 使用固定的2022-07
            .then(response => {
              const bonusStatus = response.data;
              // 添加部门ID到状态对象中
              bonusStatus.deptId = deptId;

              // 从部门组中获取部门名称
              const deptGroup = this.departmentGroups.find(group => group.deptId === deptId);
              bonusStatus.deptName = deptGroup ? deptGroup.deptName : '';

              return bonusStatus;
            })
            .catch(error => {
              // 检查是否是权限错误
              if (error.message && error.message.includes('无权限')) {
                // 权限错误，返回null，后续会被过滤掉
                return null;
              } else {
                // 其他错误，返回默认状态
                // 从部门组中获取部门名称
                const deptGroup = this.departmentGroups.find(group => group.deptId === deptId);
                const deptName = deptGroup ? deptGroup.deptName : '';

                // 返回一个默认的状态对象
                return {
                  deptId: deptId,
                  hasBonusAllocation: false,
                  totalBonus: 0,
                  allocatedBonus: 0,
                  remainingBonus: 0,
                  deptName: deptName
                };
              }
            });
        });

        Promise.all(promises)
          .then(statusList => {
            // 过滤掉null值（权限错误的部门）和没有奖金分配的部门
            this.quarterlyDeptBonusStatusList = statusList.filter(status =>
              status !== null && status.hasBonusAllocation && status.totalBonus > 0);

            // 设置主要的季度bonusStatus为第一个有奖金分配的部门，或者第一个部门
            const filteredStatusList = this.quarterlyDeptBonusStatusList;
            const primaryStatus = filteredStatusList.find(status => status && status.hasBonusAllocation) || filteredStatusList[0];
            if (primaryStatus) {
              this.quarterlyBonusStatus = primaryStatus;
              this.quarterlyDeptBonusId = primaryStatus.deptBonusId;
            }

            resolve(filteredStatusList);
          })
          .catch(reject);
      });
    },

    // 获取所有负责部门的员工并包含季度奖金分配信息
    getAllQuarterlyDeptEmployeesWithBonusInfo() {
      return new Promise((resolve, reject) => {
        // 过滤出有奖金分配的部门
        const deptsWithBonus = this.quarterlyDeptBonusStatusList
          .filter(status => status.hasBonusAllocation && status.totalBonus > 0)
          .map(status => status.deptId);

        if (deptsWithBonus.length === 0) {
          // 如果没有部门有奖金分配，返回空数组
          resolve([]);
          return;
        }

        // 获取有奖金分配的部门的员工
        const allEmployees = {};
        let completedRequests = 0;
        const totalRequests = deptsWithBonus.length;

        deptsWithBonus.forEach(deptId => {
          getDeptEmployees(deptId, this.quarterlyBonusEvaluationMonth) // 使用固定的2022-07
            .then(response => {
              const employees = response.data || [];

              // 添加员工到集合中，避免重复，保留奖金分配信息
              employees.forEach(employee => {
                if (!allEmployees[employee.userId]) {
                  allEmployees[employee.userId] = {
                    ...employee,
                    quarterlyBonusAmount: employee.bonusAmount || 0,
                    quarterlyAllocationReason: employee.allocationReason || ''
                  };
                } else {
                  // 如果员工已存在，更新奖金信息（保留已分配的奖金）
                  if (employee.bonusAmount && employee.bonusAmount > 0) {
                    allEmployees[employee.userId].quarterlyBonusAmount = employee.bonusAmount;
                    allEmployees[employee.userId].quarterlyAllocationReason = employee.allocationReason || '';
                  }
                }
              });

              // 检查是否所有请求都完成了
              completedRequests++;
              if (completedRequests === totalRequests) {
                // 将员工集合转为数组，奖金分配包含部门负责人
                const filteredEmployees = Object.values(allEmployees);

                resolve(filteredEmployees);
              }
            })
            .catch(error => {
              // 即使出错也计数，确保流程能继续
              completedRequests++;
              if (completedRequests === totalRequests) {
                if (Object.keys(allEmployees).length > 0) {
                  // 奖金分配包含部门负责人
                  const filteredEmployees = Object.values(allEmployees);

                  resolve(filteredEmployees);
                } else {
                  // 如果所有部门都获取失败，返回空数组而不是报错
                  resolve([]);
                }
              }
            });
        });
      });
    },

    // 处理季度奖金金额变更
    handleQuarterlyBonusChange() {
      // 可以在这里添加实时验证逻辑
    },

    // 验证季度各部门分配金额
    validateQuarterlyDeptAllocations() {
      const deptRemaining = this.quarterlyDeptRemainingAmounts;
      const overAllocatedDepts = [];

      // 检查每个部门是否超额分配
      this.quarterlyDeptBonusStatusList.forEach(status => {
        if (status.hasBonusAllocation) {
          const remaining = deptRemaining[status.deptId];
          if (remaining < 0) {
            overAllocatedDepts.push({
              deptName: status.deptName,
              overAmount: Math.abs(remaining)
            });
          }
        }
      });

      if (overAllocatedDepts.length > 0) {
        const messages = overAllocatedDepts.map(dept =>
          `${dept.deptName}: 超额 ${this.formatMoney(dept.overAmount)}`
        );
        return {
          valid: false,
          message: `以下部门季度分配金额超出限额：\\n${messages.join('\\n')}`
        };
      }

      return { valid: true };
    },

    // 提交季度奖金分配
    submitQuarterlyBonusAllocation() {
      if (!this.canSubmitQuarterlyBonus) {
        this.$message.warning("请检查季度分配信息");
        return;
      }

      // 按部门验证分配金额
      const validationResult = this.validateQuarterlyDeptAllocations();
      if (!validationResult.valid) {
        this.$message.error(validationResult.message);
        return;
      }

      // 验证总分配金额
      if (Math.abs(this.quarterlyRemainingAmount) > 0.01) {
        this.$confirm(
          `季度分配金额与部门总奖金不匹配，剩余金额：${this.formatMoney(this.quarterlyRemainingAmount)}，是否继续提交？`,
          '确认提交',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          this.doSubmitQuarterlyBonusAllocation();
        });
      } else {
        this.doSubmitQuarterlyBonusAllocation();
      }
    },

    // 执行季度奖金分配提交
    doSubmitQuarterlyBonusAllocation() {
      this.quarterlyBonusLoading = true;

      // 按部门分组员工
      const employeesByDept = {};
      this.quarterlyBonusEmployees.filter(emp => emp.quarterlyBonusAmount !== 0).forEach(emp => {
        if (!employeesByDept[emp.deptId]) {
          employeesByDept[emp.deptId] = [];
        }
        employeesByDept[emp.deptId].push({
          userId: emp.userId,
          userName: emp.userName,
          nickName: emp.nickName,
          bonusAmount: emp.quarterlyBonusAmount,
          allocationReason: emp.quarterlyAllocationReason || '季度部门负责人分配'
        });
      });

      // 按部门顺序提交（避免并发死锁）
      this.submitQuarterlyBonusByDept(Object.keys(employeesByDept), employeesByDept, 0);
    },

    // 按部门顺序提交季度奖金分配
    submitQuarterlyBonusByDept(deptIds, employeesByDept, index) {
      if (index >= deptIds.length) {
        // 所有部门都提交完成
        this.$message.success("季度奖金分配提交成功");
        this.loadQuarterlyBonusData();
        return;
      }

      const deptId = deptIds[index];
      const deptBonusStatus = this.quarterlyDeptBonusStatusList.find(status => status.deptId == deptId);

      if (!deptBonusStatus) {
        this.$message.error(`无法找到部门${deptId}的奖金分配状态`);
        this.quarterlyBonusLoading = false;
        return;
      }

      const submitData = {
        deptBonusId: deptBonusStatus.deptBonusId,
        allocationMonth: this.quarterlyBonusEvaluationMonth,
        deptId: deptId,
        remainingBonus: deptBonusStatus.remainingBonus,
        employeeBonusList: employeesByDept[deptId]
      };

      allocateBonus(submitData)
        .then(response => {
          // 提交下一个部门
          this.submitQuarterlyBonusByDept(deptIds, employeesByDept, index + 1);
        })
        .catch(error => {
          this.$message.error(`部门${deptBonusStatus.deptName}季度奖金分配失败：${error.message || error}`);
          this.quarterlyBonusLoading = false;
        });
    },

    // 重置季度奖金分配
    resetQuarterlyBonusAllocation() {
      this.$confirm('确定要重置季度奖金分配吗？这将删除数据库中已分配的奖金记录，操作不可撤销。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 获取所有有奖金分配的部门ID
        const deptBonusIds = this.quarterlyDeptBonusStatusList
          .filter(status => status.hasBonusAllocation && status.deptBonusId)
          .map(status => status.deptBonusId);

        if (deptBonusIds.length === 0) {
          this.$message.error("无法获取季度部门奖金分配ID");
          return;
        }

        this.quarterlyBonusLoading = true;

        // 顺序删除各部门的奖金分配记录（避免并发问题）
        this.resetQuarterlyBonusByDept(deptBonusIds, 0);
      });
    },

    // 按部门顺序重置季度奖金分配
    resetQuarterlyBonusByDept(deptBonusIds, index) {
      if (index >= deptBonusIds.length) {
        // 所有部门都重置完成
        // 重置前端数据
        this.quarterlyBonusEmployees.forEach(employee => {
          employee.quarterlyBonusAmount = 0;
          employee.quarterlyAllocationReason = '';
        });

        // 重新获取季度奖金状态和员工列表
        this.loadQuarterlyBonusData();
        this.$message.success("已重置季度奖金分配，数据库记录已清除");
        return;
      }

      const deptBonusId = deptBonusIds[index];

      deleteEmployeeBonusByDeptBonusId(deptBonusId)
        .then(response => {
          // 重置下一个部门
          this.resetQuarterlyBonusByDept(deptBonusIds, index + 1);
        })
        .catch(error => {
          this.$message.error("重置失败：" + (error.msg || error.message || error));
          this.quarterlyBonusLoading = false;
        });
    },

    // 格式化金额显示
    formatMoney(amount) {
      if (amount == null || amount === undefined) return '¥0.00';
      return '¥' + Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    // 显示验证错误弹框
    showValidationErrorDialog(invalidUsers, validUsers = []) {
      // 处理验证错误数据
      this.validationErrorData = invalidUsers.map(result => {
        const user = result.user;
        const errorResponse = result.error;

        // 解析错误信息，提取项目详情
        let missingProjects = [];
        try {
          // 如果错误信息包含项目详情，尝试解析
          if (errorResponse && typeof errorResponse === 'string' && errorResponse.includes('项目：')) {
            // 简单解析错误信息中的项目信息
            const lines = errorResponse.split('\n');
            lines.forEach(line => {
              if (line.includes('项目：') && line.includes('负责人：')) {
                const projectMatch = line.match(/项目：([^，]+)，负责人：(.+)/);
                if (projectMatch) {
                  missingProjects.push({
                    projectName: projectMatch[1],
                    leaderName: projectMatch[2]
                  });
                }
              }
            });
          }
        } catch (e) {
          // 静默处理解析错误
        }

        return {
          userName: user.nickName || user.userName,
          userId: user.userId,
          errorMessage: errorResponse,
          missingProjects: missingProjects
        };
      });

      // 保存验证通过的用户，用于部分提交
      this.validUsersForPartialSubmit = validUsers;

      // 显示弹框
      this.validationErrorDialog = true;
    },

    // 关闭验证错误弹框
    closeValidationErrorDialog() {
      this.validationErrorDialog = false;
      this.validationErrorData = [];
      this.validUsersForPartialSubmit = [];
    },

    // 页面加载时验证所有用户的评分前置条件
    async validateAllUsersOnLoad() {
      if (!this.queryParams.evaluationMonth) {
        return;
      }

      // 收集所有用户
      const allUsers = [];
      this.departmentGroups.forEach(deptGroup => {
        deptGroup.users.forEach(user => {
          allUsers.push({
            ...user,
            deptId: deptGroup.deptId,
            deptName: deptGroup.deptName
          });
        });
      });



      // 首先检查哪些用户参与了项目
      const usersWithProjectStatus = await Promise.all(
        allUsers.map(async (user) => {
          try {
            const hasProjects = await this.checkUserHasProjects(user.userName);
            return { ...user, hasProjects };
          } catch (error) {
            return { ...user, hasProjects: false };
          }
        })
      );

      // 只对参与项目的用户进行验证
      const validationPromises = usersWithProjectStatus.map(user => {
        const userId = Number(user.userId);
        if (isNaN(userId)) {
          return Promise.resolve({ user, valid: true, disabled: false });
        }

        // 如果用户没有参与项目，则不需要验证，直接允许评分
        if (!user.hasProjects) {
          return Promise.resolve({ user, valid: true, disabled: false });
        }

        // 只对参与项目的用户进行验证
        return validateManagerEvaluation(userId, this.queryParams.evaluationMonth)
          .then(response => {
            return { user, valid: true, disabled: false };
          })
          .catch(error => {
            // 静默处理验证失败，不显示错误消息

            // 解析错误信息，提取项目详情
            let missingProjects = [];
            try {
              const errorMessage = error.msg || error.message || '';
              if (errorMessage.includes('项目：')) {
                const lines = errorMessage.split('\n');
                lines.forEach(line => {
                  if (line.includes('项目：') && line.includes('负责人：')) {
                    const projectMatch = line.match(/项目：([^，]+)，负责人：(.+)/);
                    if (projectMatch) {
                      missingProjects.push({
                        projectName: projectMatch[1],
                        leaderName: projectMatch[2]
                      });
                    }
                  }
                });
              }
            } catch (e) {
              // 静默处理解析错误
            }

            return {
              user,
              valid: false,
              disabled: true,
              error: error.msg || error.message || '验证失败',
              missingProjects: missingProjects
            };
          });
      });

      try {
        const validationResults = await Promise.all(validationPromises);

        // 根据验证结果设置用户的禁用状态和提示信息
        validationResults.forEach(result => {
          const { user, disabled, missingProjects, error } = result;

          // 找到用户在departmentGroups中的位置并设置禁用状态
          this.departmentGroups.forEach((deptGroup, deptIndex) => {
            const userIndex = deptGroup.users.findIndex(u => u.userId === user.userId);
            if (userIndex !== -1) {
              this.$set(this.departmentGroups[deptIndex].users[userIndex], 'scoreInputDisabled', disabled);

              if (disabled) {
                // 设置悬停提示信息
                let tooltipContent = '';
                if (missingProjects && missingProjects.length > 0) {
                  tooltipContent = '需要以下项目负责人先完成评分：\n';
                  missingProjects.forEach(project => {
                    tooltipContent += `• ${project.projectName} - 负责人：${project.leaderName}\n`;
                  });
                } else {
                  tooltipContent = error || '需要项目负责人先完成评分';
                }

                this.$set(this.departmentGroups[deptIndex].users[userIndex], 'disabledTooltip', tooltipContent);

              } else {
                // 清除提示信息
                this.$set(this.departmentGroups[deptIndex].users[userIndex], 'disabledTooltip', '');
              }
            }
          });
        });



      } catch (error) {

      }
    },

    // 部分提交评分（只提交验证通过的用户）
    submitValidUsersOnly() {
      if (this.validUsersForPartialSubmit.length === 0) {
        this.$message.warning("没有可以提交的用户评分");
        this.closeValidationErrorDialog();
        return;
      }

      // 关闭弹框
      this.closeValidationErrorDialog();

      // 继续处理验证通过的用户评分
      this.processBatchEvaluation(this.validUsersForPartialSubmit, '部分用户');
    },

    // 处理批量评分（提取的公共方法）
    processBatchEvaluation(validUsers, deptName = '') {
      this.loading = true;

      // 获取当前用户ID作为评价人ID
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$message.error("获取当前用户信息失败，请重新登录后再试");
        this.loading = false;
        return;
      }

      // 首先查询当前月份的评价记录 - 支持多评价类型和评分人过滤
      const managerParams = {
        evaluationType: "manager",
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluatorId: evaluatorId,
      };

      const parentManagerParams = {
        evaluationType: "parent_manager",
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluatorId: evaluatorId,
      };

      // 并行查询两种评价类型
      Promise.all([
        listEvaluation(managerParams),
        listEvaluation(parentManagerParams)
      ]).then(responses => {
        const managerEvaluations = responses[0].rows || [];
        const parentManagerEvaluations = responses[1].rows || [];

        // 合并两种评价类型的结果
        const allEvaluations = [...managerEvaluations, ...parentManagerEvaluations];

        // 创建一个映射表，使用 evaluateeId + evaluationType 作为键
        const evaluationMap = {};
        allEvaluations.forEach(evaluation => {
          if (evaluation.evaluateeId !== null && evaluation.evaluateeId !== undefined) {
            const evaluateeId = Number(evaluation.evaluateeId);
            if (!isNaN(evaluateeId) && evaluation.evaluatorId === evaluatorId) {
              // 使用 evaluateeId + evaluationType 作为键，确保能区分不同类型的评价
              const key = `${evaluateeId}_${evaluation.evaluationType}`;
              evaluationMap[key] = evaluation;
            }
          }
        });

        // 准备保存的评价列表
        const promises = [];
        const updatedUsers = [];

        validUsers.forEach(user => {
          // 确保用户ID是数字类型
          const userId = Number(user.userId);

          // 只处理有有效评分的用户（不包括空值和0分）
          if (!isNaN(userId) && user.newScore !== null && user.newScore !== undefined && user.newScore > 0) {
            // 标记该用户已处理
            updatedUsers.push(userId);

            // 使用用户所在的部门ID
            const userDeptId = user.deptId || this.currentDeptId;

            // 根据当前登录用户的身份确定评价类型
            let evaluationType = "manager"; // 默认为manager类型

            // 如果当前用户是父部门负责人，且被评价用户是子部门成员，则使用parent_manager类型
            if (!this.isSubDeptLeader() && user.isSubDeptMember) {
              evaluationType = "parent_manager";
            }

            // 准备评价数据
            const evaluation = {
              deptId: userDeptId,
              evaluateeId: userId,
              evaluatorId: evaluatorId,
              score: user.newScore,
              evaluationMonth: this.queryParams.evaluationMonth,
              evaluationType: evaluationType,
              comments: user.comments || "系统默认评分",
              projectId: 0 // 默认项目ID
            };

            // 检查是否已有评价记录 - 根据用户类型和评价类型查找
            const evaluationKey = `${userId}_${evaluationType}`;
            const existingEval = evaluationMap[evaluationKey];

            if (existingEval) {
              // 更新已有评价
              evaluation.id = existingEval.id;
              promises.push(updateEvaluation(evaluation));
            } else {
              // 新增评价
              promises.push(addEvaluation(evaluation));
            }
          }
        });

        // 如果没有要保存的评价，直接返回
        if (promises.length === 0) {
          this.$message.warning("没有需要保存的评价");
          this.loading = false;
          return;
        }

        // 批量保存评价
        Promise.all(promises)
          .then(results => {
            const successMsg = deptName ?
              `部门 ${deptName} 成功保存 ${promises.length} 条评价记录` :
              `成功保存 ${promises.length} 条评价记录`;
            this.$message.success(successMsg);
            this.hasUnsavedChanges = false;

            // 直接刷新页面上的评分显示
            this.refreshEvaluationDisplay(updatedUsers);

            // 立即刷新配额信息，确保用户能看到最新状态
            this.refreshQuotaInfoAfterSave();

            // 自动计算评价结果（针对提交的用户）
            this.autoCalculateSubmittedUsersResults(validUsers);

            // 重新加载数据
            setTimeout(() => {
              this.getExistingEvaluations();
            }, 300);
          })
          .catch(error => {
            this.$message.error("提交失败：" + error);
            this.loading = false;
            this.isSubmitting = false; // 重置提交状态
          });
      }).catch(error => {
        this.$message.error("获取评价记录失败：" + error);
        this.loading = false;
        this.isSubmitting = false; // 重置提交状态
      });
    },

    // 获取部门成员
    getDeptUsers() {
      return new Promise((resolve, reject) => {
        if (!this.currentDeptId) {
          this.loading = false;
          reject(new Error("没有部门ID"));
          return;
        }
        
        // 获取部门所有用户
        listUser({
          deptId: this.currentDeptId
        }).then(response => {
          const users = response.rows || [];
          // 排除部门负责人和特聘专家
          const filteredUsers = users.filter(user => {
            // 排除特聘专家
            if (user.remark === '特聘专家') {
              return false;
            }
            // 排除部门负责人
            if (this.deptInfo && this.deptInfo.leader) {
              return user.userName !== this.deptInfo.leader;
            }
            return true;
          });
          
          this.deptUsers = filteredUsers.map(user => ({
            ...user,
            newScore: 0,
            comments: "",
            _uid: user.userId,  // 用于table的key
            projectEvaluations: [],  // 初始化项目评价列表
            dataLoaded: false  // 标记项目数据是否已加载
          }));
          
          // 获取已有评价记录
          this.getExistingEvaluations();
          
          // 标记初始化完成
          this.initialized = true;
          resolve();
        }).catch(error => {
          this.$message.error("获取部门用户失败：" + error);
          this.loading = false;
          reject(error);
        });
      });
    },

    // 导出部门数据（只导出当前表格中显示的用户）
    async exportDeptData(deptGroup) {
      if (!this.queryParams.evaluationMonth) {
        this.$message.warning("请先选择评价月份");
        return;
      }

      if (!deptGroup || !deptGroup.deptId) {
        this.$message.warning("部门信息不完整");
        return;
      }

      // 检查是否有显示的用户数据
      if (!deptGroup.users || deptGroup.users.length === 0) {
        this.$message.warning("当前部门没有显示的用户数据");
        return;
      }

      this.loading = true;

      try {
        // 收集当前表格中显示的用户ID
        const displayedUserIds = deptGroup.users.map(user => user.userId);

        console.log(`导出部门 ${deptGroup.deptName} 的数据，包含 ${displayedUserIds.length} 个用户:`, displayedUserIds);

        // 调用后端导出接口，传入用户ID列表
        const response = await exportDeptEvaluationData(
          deptGroup.deptId,
          this.queryParams.evaluationMonth,
          deptGroup.deptName,
          displayedUserIds
        );

        // 创建下载链接
        const blob = new Blob([response], { type: 'text/csv;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${deptGroup.deptName}_评分数据_${this.queryParams.evaluationMonth}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success(`数据导出成功，共导出 ${displayedUserIds.length} 个用户的数据`);

      } catch (error) {
        this.$message.error('导出数据失败：' + (error.message || '未知错误'));
      } finally {
        this.loading = false;
      }
    },



    // 获取用户的已有评分
    getExistingScore(userId) {
      // 从已有评价记录中获取分数
      const evaluationKey = `${userId}_manager`;
      const existingEval = this.existingEvaluations[evaluationKey];
      return existingEval ? existingEval.score : null;
    },

    // 下载Excel文件
    downloadExcel(data, filename) {
      if (!data || data.length === 0) {
        this.$message.warning("没有数据可导出");
        return;
      }

      // 创建工作表
      const worksheet = [];

      // 添加表头
      const headers = Object.keys(data[0]);
      worksheet.push(headers);

      // 添加数据行
      data.forEach(row => {
        const rowData = headers.map(header => row[header] || '');
        worksheet.push(rowData);
      });

      // 转换为CSV格式
      const csvContent = worksheet.map(row =>
        row.map(cell => `"${cell}"`).join(',')
      ).join('\n');

      // 添加BOM以支持中文
      const BOM = '\uFEFF';
      const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${filename}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      this.$message.success('数据导出成功');
    },
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.member-card {
  flex: 1;
  
  .header-month {
    margin-left: 20px;
    color: #606266;
    font-size: 14px;
  }
  
  .placeholder-info {
    text-align: center;
    padding: 40px 0;
    color: #909399;
    font-size: 14px;
  }
  
  .table-footer {
    margin-top: 20px;
    text-align: center;
  }
}

.score-unit {
  margin-left: 5px;
  color: #606266;
}

.no-data {
  padding: 20px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.loading-data {
  padding: 20px;
  text-align: center;
  color: #409EFF;
  font-size: 14px;
}

.project-expand-form {
  margin: 0;
  padding: 20px;
}

.project-item {
  margin-bottom: 15px;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  flex-wrap: wrap;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.project-item:last-child {
  border-bottom: none;
}

.project-item .el-form-item {
  margin-right: 30px;
  margin-bottom: 0;
}

.score-value {
  font-weight: bold;
  color: #409EFF;
  font-size: 16px;
}

.loading-data {
  padding: 20px;
  text-align: center;
  color: #409EFF;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-data i {
  margin-right: 5px;
}

.no-data {
  padding: 20px;
  text-align: center;
  color: #909399;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.no-data i {
  font-size: 24px;
  margin-bottom: 10px;
  color: #E6A23C;
}

.project-header {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #dcdfe6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-title {
  font-weight: bold;
  color: #303133;
  font-size: 15px;
}

.project-note {
  color: #909399;
  font-size: 12px;
}

.project-note i {
  margin-right: 4px;
  color: #E6A23C;
}

.refresh-action {
  margin-top: 15px;
  text-align: right;
}

/* 奖金分配相关样式 */
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.bonus-card {
  min-height: 400px;
}

.bonus-unit {
  margin-left: 5px;
  color: #606266;
}

.bonus-summary {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.summary-label {
  font-weight: bold;
  margin-right: 8px;
  color: #606266;
}

.summary-value {
  font-size: 16px;
  font-weight: bold;
}

.text-success {
  color: #67C23A;
}

.text-danger {
  color: #F56C6C;
}

.text-info {
  color: #409EFF;
}

.text-muted {
  color: #909399;
}

/* 评分样式 */
.score-excellent {
  color: #67C23A;
  font-weight: bold;
}

.score-good {
  color: #409EFF;
  font-weight: bold;
}

.score-average {
  color: #E6A23C;
  font-weight: bold;
}

.score-poor {
  color: #F56C6C;
  font-weight: bold;
}



.score-input-container {
  position: relative;
  display: inline-block;
  text-align: center;
}

.high-score {
  border-color: #E6A23C !important;
}

.high-score-tip {
  font-size: 12px;
  color: #E6A23C;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  margin-left: 8px;
}

.high-score-tip i {
  margin-right: 2px;
}

.disabled-input {
  opacity: 0.6;
}

.disabled-input:hover {
  cursor: not-allowed;
}

/* 禁用评分输入框的tooltip样式 */
.disabled-score-tooltip {
  max-width: 300px;
}

.disabled-score-tooltip .el-tooltip__popper {
  max-width: 300px;
  white-space: normal;
  line-height: 1.4;
}

.disabled-score-tooltip .el-tooltip__popper[x-placement^="top"] .el-popper__arrow::after {
  border-top-color: #303133;
}

/* 最终评分显示样式 */
.final-score-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.final-score-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.final-score-title {
  font-weight: bold;
  color: #409EFF;
  font-size: 14px;
}

.final-score-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.score-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.score-label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 100px;
}

.score-value {
  font-weight: bold;
  color: #409EFF;
}

.effort-value {
  font-weight: bold;
  color: #67C23A;
}

.evaluation-status {
  font-size: 12px;
}

.status-evaluated {
  color: #67C23A;
  font-weight: bold;
}

.status-leader {
  color: #1890FF;
  font-weight: bold;
}

.status-pending {
  color: #F56C6C;
}

.score-display {
  color: #409EFF;
  font-weight: bold;
  margin-left: 4px;
}

.score-value.final {
  font-size: 16px;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #fff;
}

.role-tag {
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #e1f3d8;
  color: #67c23a;
  font-size: 12px;
}

.no-project-info {
  text-align: center;
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

/* 评分等级样式 */
.score-excellent {
  color: #67C23A !important;
  background-color: #f0f9ff !important;
}

.score-good {
  color: #409EFF !important;
  background-color: #ecf5ff !important;
}

.score-average {
  color: #E6A23C !important;
  background-color: #fdf6ec !important;
}

.score-poor {
  color: #F56C6C !important;
  background-color: #fef0f0 !important;
}



.el-divider--horizontal {
  margin: 15px 0;
}

/* 部门选项卡样式 */
.dept-tabs-card {
  border: none;
}

.dept-tabs {
  margin-top: -10px;
}

.dept-tabs .el-tabs__header {
  margin-bottom: 15px;
}

.dept-tabs .el-tabs__item {
  font-weight: 500;
}

.dept-tabs .el-tabs__item.is-active {
  color: #409EFF;
  font-weight: bold;
}





/* 表格和配额信息容器 */
.table-with-quota-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

/* 表格和奖金信息容器 */
.table-with-bonus-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.table-area {
  flex: 1;
  min-width: 0; /* 防止flex子项溢出 */
}

/* 右侧配额信息竖条样式 */
.quota-sidebar {
  width: 180px;
  flex-shrink: 0;
}

.quota-bar-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.08);
  position: sticky;
  top: 20px;
}

.quota-bar-header {
  text-align: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #dee2e6;
}

.quota-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  display: block;
  margin-bottom: 8px;
}

.quota-bar-header .quota-group-tag {
  margin-top: 4px;
}

.quota-bar-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quota-bar-item {
  text-align: center;
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(0,0,0,0.05);
}

.quota-bar-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.quota-bar-value {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}

.quota-bar-value.primary {
  color: #409EFF;
}

.quota-bar-value.used {
  color: #E6A23C;
}

.quota-bar-value.success {
  color: #67C23A;
}

.quota-bar-value.warning {
  color: #F56C6C;
}



/* 响应式设计 */
@media (max-width: 1200px) {
  .quota-sidebar {
    width: 160px;
  }

  .quota-bar-value {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .table-with-quota-container {
    flex-direction: column;
    gap: 16px;
  }

  .quota-sidebar {
    width: 100%;
    order: -1; /* 移动端时配额信息显示在表格上方 */
  }

  .quota-bar-container {
    position: static;
  }

  .quota-bar-items {
    flex-direction: row;
    justify-content: space-around;
    gap: 8px;
  }

  .quota-bar-item {
    flex: 1;
    padding: 8px 4px;
  }

  .quota-bar-value {
    font-size: 18px;
  }
}

/* 右侧奖金信息竖条样式 */
.bonus-sidebar {
  width: 180px;
  flex-shrink: 0;
}

.bonus-bar-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.08);
  position: fixed;
  top: 421px;
  right: 20px;
  z-index: 1000;
}

.bonus-bar-header {
  text-align: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #dee2e6;
}

.bonus-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  display: block;
  margin-bottom: 8px;
}

.bonus-bar-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.bonus-bar-item {
  text-align: center;
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(0,0,0,0.05);
}

.bonus-bar-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 4px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bonus-bar-value {
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
}

.bonus-bar-value.primary {
  color: #ff9800;
}

.bonus-bar-value.used {
  color: #f57c00;
}

.bonus-bar-value.success {
  color: #4caf50;
}

.bonus-bar-value.warning {
  color: #f44336;
}

.bonus-status {
  margin-top: 8px;
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(0,0,0,0.05);
  text-align: center;
}

.status-label {
  font-size: 11px;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 响应式设计 - 奖金分配 */
@media (max-width: 1200px) {
  .bonus-sidebar {
    width: 160px;
  }

  .bonus-bar-value {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .table-with-bonus-container {
    flex-direction: column;
    gap: 16px;
    margin-right: 200px; /* 为固定定位的竖条预留空间 */
  }

  .bonus-sidebar {
    width: 180px;
  }

  .bonus-bar-container {
    position: fixed;
    top: 260px;
    right: 10px;
    width: 160px;
    z-index: 1001;
  }

  .bonus-bar-items {
    flex-direction: column;
    gap: 12px;
  }

  .bonus-bar-item {
    padding: 8px 4px;
  }

  .bonus-bar-value {
    font-size: 14px;
  }

  .bonus-status {
    margin-top: 8px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .table-with-bonus-container {
    margin-right: 0;
  }

  .bonus-bar-container {
    position: static;
    width: 100%;
    margin-bottom: 16px;
  }

  .bonus-bar-items {
    flex-direction: row;
    justify-content: space-around;
    gap: 8px;
  }

  .bonus-bar-item {
    flex: 1;
    padding: 6px 4px;
  }

  .bonus-bar-value {
    font-size: 12px;
  }
}

/* 部门成员列表样式 */
.member-card {
  background: white;
  border-radius: 4px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
}

.header-month {
  color: #909399;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quota-summary .el-col {
    margin-bottom: 10px;
  }

  .dept-quota-row .el-col {
    margin-bottom: 8px;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-month {
    margin-top: 5px;
  }
}

/* 评价类型子标签页样式 */
.evaluation-sub-tabs {
  margin-top: 10px;
  padding: 0 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.evaluation-sub-tabs .el-tabs__header {
  margin-bottom: 15px;
  padding-top: 15px;
  background-color: transparent;
}

.evaluation-sub-tabs .el-tabs__nav-wrap::after {
  background-color: #d3d4d6;
}

.evaluation-sub-tabs .el-tabs__item {
  font-weight: 500;
  font-size: 14px;
  color: #606266;
  border-left: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
  border-top: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  margin-right: 5px;
  padding: 0 15px;
  height: 35px;
  line-height: 35px;
}

.evaluation-sub-tabs .el-tabs__item:first-child {
  margin-left: 0;
}

.evaluation-sub-tabs .el-tabs__item.is-active {
  color: #409EFF;
  font-weight: bold;
  background-color: #ffffff;
  border-bottom-color: #ffffff;
  position: relative;
  z-index: 1;
}

.evaluation-sub-tabs .el-tabs__item:hover {
  color: #409EFF;
  background-color: #ecf5ff;
}

.evaluation-sub-tabs .el-tabs__content {
  padding: 15px;
  background-color: #ffffff;
  border-radius: 6px;
  margin: 0 -20px -1px -20px;
  border: 1px solid #e4e7ed;
  border-top: none;
}

/* 确保部门标签页在子标签页内容区域内正常显示 */
.evaluation-sub-tabs .dept-tabs-card {
  border: none;
  background-color: transparent;
  box-shadow: none;
}

.evaluation-sub-tabs .dept-tabs {
  margin-top: 0;
}

.evaluation-sub-tabs .dept-tabs .el-tabs__header {
  margin-bottom: 15px;
  padding-top: 0;
}
</style>