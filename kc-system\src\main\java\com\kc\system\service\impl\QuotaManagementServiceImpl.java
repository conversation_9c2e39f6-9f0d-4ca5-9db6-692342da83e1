package com.kc.system.service.impl;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.kc.common.utils.SecurityUtils;
import com.kc.common.core.domain.entity.SysDept;
import com.kc.system.domain.QuotaManagement;
import com.kc.system.mapper.QuotaManagementMapper;
import com.kc.system.mapper.HighScoreRecordMapper;
import com.kc.system.service.IQuotaManagementService;
import com.kc.system.service.ISysDeptService;
import com.kc.system.service.IQuotaGroupService;

/**
 * 配额管理Service业务层处理（简化版）
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Service
public class QuotaManagementServiceImpl implements IQuotaManagementService
{
    private static final Logger log = LoggerFactory.getLogger(QuotaManagementServiceImpl.class);

    @Autowired
    private QuotaManagementMapper quotaManagementMapper;

    @Autowired
    private HighScoreRecordMapper highScoreRecordMapper;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IQuotaGroupService quotaGroupService;

    /**
     * 查询配额管理
     */
    @Override
    public QuotaManagement selectQuotaManagementById(Long id)
    {
        return quotaManagementMapper.selectQuotaManagementById(id);
    }

    /**
     * 查询配额管理列表
     */
    @Override
    public List<QuotaManagement> selectQuotaManagementList(QuotaManagement quotaManagement)
    {
        return quotaManagementMapper.selectQuotaManagementList(quotaManagement);
    }

    /**
     * 新增配额管理
     */
    @Override
    public int insertQuotaManagement(QuotaManagement quotaManagement)
    {
        // 验证部门是否存在
        SysDept dept = deptService.selectDeptById(quotaManagement.getDeptId());
        if (dept == null) {
            throw new RuntimeException("部门不存在：" + quotaManagement.getDeptId());
        }

        // 检查是否已存在该部门该年度的配额
        QuotaManagement existing = quotaManagementMapper.selectByDeptAndYear(
            quotaManagement.getDeptId(), quotaManagement.getEvaluationYear());
        if (existing != null) {
            throw new RuntimeException("该部门该年度的配额已存在");
        }

        quotaManagement.setCreateBy(SecurityUtils.getUsername());
        return quotaManagementMapper.insertQuotaManagement(quotaManagement);
    }

    /**
     * 修改配额管理
     */
    @Override
    public int updateQuotaManagement(QuotaManagement quotaManagement)
    {
        quotaManagement.setUpdateBy(SecurityUtils.getUsername());
        return quotaManagementMapper.updateQuotaManagement(quotaManagement);
    }

    /**
     * 批量删除配额管理
     */
    @Override
    public int deleteQuotaManagementByIds(Long[] ids)
    {
        return quotaManagementMapper.deleteQuotaManagementByIds(ids);
    }

    /**
     * 删除配额管理信息
     */
    @Override
    public int deleteQuotaManagementById(Long id)
    {
        return quotaManagementMapper.deleteQuotaManagementById(id);
    }

    /**
     * 根据部门ID和年度查询配额管理
     */
    @Override
    public QuotaManagement getQuotaByDeptAndYear(Long deptId, String year)
    {
        // 首先检查是否属于配额组
        if (quotaGroupService.isDeptInQuotaGroup(deptId)) {
            // 配额组由配额组服务处理，这里返回null
            return null;
        }

        // 查询配额管理表
        QuotaManagement quota = quotaManagementMapper.selectByDeptAndYear(deptId, year);
        if (quota == null) {
            // 如果不存在，自动初始化一个默认配额
            quota = initDeptQuota(deptId, year);
        }
        
        return quota;
    }

    /**
     * 检查配额是否可用
     */
    @Override
    public boolean checkQuotaAvailable(Long deptId, String year, int requestCount)
    {
        // 首先检查是否属于配额组
        if (quotaGroupService.isDeptInQuotaGroup(deptId)) {
            return quotaGroupService.checkQuotaGroupQuotaAvailable(deptId, year, requestCount);
        }

        // 获取部门配额
        QuotaManagement quota = getQuotaByDeptAndYear(deptId, year);
        if (quota == null || quota.getHighScoreQuota() == null) {
            return false;
        }

        // 计算已使用配额
        int usedCount = highScoreRecordMapper.countByDeptAndYear(deptId, year);
        int remainingQuota = quota.getHighScoreQuota() - usedCount;
        
        return remainingQuota >= requestCount;
    }

    /**
     * 使用配额
     */
    @Override
    public boolean useQuota(Long deptId, String year, int count)
    {
        // 首先检查是否属于配额组
        if (quotaGroupService.isDeptInQuotaGroup(deptId)) {
            return quotaGroupService.useQuotaGroupQuota(deptId, year, count);
        }

        // 检查配额是否可用
        return checkQuotaAvailable(deptId, year, count);
    }

    /**
     * 释放配额
     */
    @Override
    public boolean releaseQuota(Long deptId, String year, int count)
    {
        // 首先检查是否属于配额组
        if (quotaGroupService.isDeptInQuotaGroup(deptId)) {
            return quotaGroupService.releaseQuotaGroupQuota(deptId, year, count);
        }

        // 对于普通部门，不需要特殊处理，配额检查基于实时计算
        return true;
    }

    /**
     * 批量设置配额
     */
    @Override
    @Transactional
    public int batchSetQuota(List<QuotaManagement> quotaManagementList)
    {
        int result = 0;
        for (QuotaManagement quota : quotaManagementList) {
            quota.setUpdateBy(SecurityUtils.getUsername());
            
            QuotaManagement existing = quotaManagementMapper.selectByDeptAndYear(
                quota.getDeptId(), quota.getEvaluationYear());
            
            if (existing != null) {
                quota.setId(existing.getId());
                result += quotaManagementMapper.updateQuotaManagement(quota);
            } else {
                quota.setCreateBy(SecurityUtils.getUsername());
                result += quotaManagementMapper.insertQuotaManagement(quota);
            }
        }
        return result;
    }

    /**
     * 重置年度配额使用情况（简化版，不需要实际操作）
     */
    @Override
    public int resetYearQuotaUsage(String year)
    {
        log.info("重置年度[{}]配额使用情况（配额使用基于实时计算）", year);
        return 0;
    }

    /**
     * 查询配额统计
     */
    @Override
    public List<QuotaManagement> getQuotaStatistics(String year)
    {
        return quotaManagementMapper.selectQuotaStatistics(year);
    }

    /**
     * 初始化年度配额
     */
    @Override
    @Transactional
    public int initYearQuota(String year)
    {
        // 查询需要初始化配额的部门
        List<QuotaManagement> needInitDepts = quotaManagementMapper.selectDeptsNeedInitQuota(year);
        
        if (needInitDepts.isEmpty()) {
            return 0;
        }

        // 为每个部门设置默认值
        for (QuotaManagement quota : needInitDepts) {
            quota.setHighScoreQuota(0);
            quota.setQuotaType("MANUAL");
            quota.setCreateBy(SecurityUtils.getUsername());
        }

        return quotaManagementMapper.batchInitDeptQuota(needInitDepts);
    }

    /**
     * 更新部门人数（已废弃）
     */
    @Override
    public int updateDeptEmployeeCount(Long deptId, String year)
    {
        log.warn("updateDeptEmployeeCount方法已废弃");
        return 0;
    }

    /**
     * 批量更新部门人数（已废弃）
     */
    @Override
    @Transactional
    public int batchUpdateDeptEmployeeCount(String year)
    {
        log.warn("batchUpdateDeptEmployeeCount方法已废弃");
        return 0;
    }

    /**
     * 获取配额使用详情
     */
    @Override
    public QuotaManagement getQuotaDetail(Long deptId, String year)
    {
        QuotaManagement quota = getQuotaByDeptAndYear(deptId, year);
        if (quota != null) {
            // 获取部门信息
            SysDept dept = deptService.selectDeptById(deptId);
            if (dept != null) {
                quota.setDeptName(dept.getDeptName());
                quota.setDeptLeader(dept.getLeader());
            }

            // 实时计算已使用配额
            int actualUsedQuota = highScoreRecordMapper.countByDeptAndYear(deptId, year);
            try {
                quota.setUsedQuota(actualUsedQuota);
                int remainingQuota = (quota.getHighScoreQuota() != null ? quota.getHighScoreQuota() : 0) - actualUsedQuota;
                quota.setRemainingQuota(remainingQuota);

                log.debug("部门[{}]年度[{}]配额详情: 总配额={}, 已使用={}, 剩余={}",
                         deptId, year, quota.getHighScoreQuota(), actualUsedQuota, remainingQuota);
            } catch (NoSuchMethodError e) {
                log.error("QuotaManagement类缺少usedQuota相关方法，请重新编译项目", e);
                // 临时解决方案：在日志中记录信息，但不设置字段
                log.info("部门[{}]年度[{}]配额详情: 总配额={}, 已使用={}, 剩余={}",
                         deptId, year, quota.getHighScoreQuota(), actualUsedQuota,
                         (quota.getHighScoreQuota() != null ? quota.getHighScoreQuota() : 0) - actualUsedQuota);
            }
        }
        return quota;
    }

    /**
     * 初始化部门配额
     */
    private QuotaManagement initDeptQuota(Long deptId, String year)
    {
        SysDept dept = deptService.selectDeptById(deptId);
        if (dept == null) {
            throw new RuntimeException("部门不存在：" + deptId);
        }
        
        QuotaManagement quota = new QuotaManagement();
        quota.setDeptId(deptId);
        quota.setEvaluationYear(year);
        quota.setHighScoreQuota(0); // 需要手动设置
        quota.setQuotaType("MANUAL");
        quota.setCreateBy(SecurityUtils.getUsername());

        quotaManagementMapper.insertQuotaManagement(quota);
        log.info("初始化部门[{}]年度[{}]配额，需要手动设置", deptId, year);
        
        return quota;
    }
}
