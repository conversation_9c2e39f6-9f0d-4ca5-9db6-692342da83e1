package com.kc.system.domain.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 部门奖金分配DTO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class DeptBonusAllocationDTO {
    
    /** 分配月份 */
    private String allocationMonth;
    
    /** 部门奖金分配列表 */
    private List<DeptBonusItem> deptBonusList;
    
    public static class DeptBonusItem {
        /** 部门ID */
        private Long deptId;
        
        /** 部门名称 */
        private String deptName;
        
        /** 绩效排名 */
        private Integer performanceRank;
        
        /** 部门总奖金 */
        private BigDecimal totalBonus;
        
        /** 备注 */
        private String remark;

        // getter和setter方法
        public Long getDeptId() {
            return deptId;
        }

        public void setDeptId(Long deptId) {
            this.deptId = deptId;
        }

        public String getDeptName() {
            return deptName;
        }

        public void setDeptName(String deptName) {
            this.deptName = deptName;
        }

        public Integer getPerformanceRank() {
            return performanceRank;
        }

        public void setPerformanceRank(Integer performanceRank) {
            this.performanceRank = performanceRank;
        }

        public BigDecimal getTotalBonus() {
            return totalBonus;
        }

        public void setTotalBonus(BigDecimal totalBonus) {
            this.totalBonus = totalBonus;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }

    // getter和setter方法
    public String getAllocationMonth() {
        return allocationMonth;
    }

    public void setAllocationMonth(String allocationMonth) {
        this.allocationMonth = allocationMonth;
    }

    public List<DeptBonusItem> getDeptBonusList() {
        return deptBonusList;
    }

    public void setDeptBonusList(List<DeptBonusItem> deptBonusList) {
        this.deptBonusList = deptBonusList;
    }
}
