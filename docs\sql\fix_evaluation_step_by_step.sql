-- ===== 分步修复项目评价数据脚本 =====
-- 执行前请仔细阅读每个步骤的说明

-- ===== 第一步：数据备份和分析 =====
-- 1.1 创建备份表
CREATE TABLE project_evaluation_backup_20250715 AS 
SELECT * FROM project_evaluation WHERE evaluation_month IN ('2025-06', '2025-07');

-- 1.2 查看当前数据分布
SELECT 
    '当前数据分布' as analysis_type,
    evaluation_month, 
    evaluation_type, 
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
GROUP BY evaluation_month, evaluation_type 
ORDER BY evaluation_month, evaluation_type;

-- 1.3 分析可疑的2025-06数据（6月11日12:00之后创建的）
SELECT 
    '可疑2025-06数据' as analysis_type,
    evaluation_type,
    COUNT(*) as count,
    MIN(created_at) as earliest,
    MAX(created_at) as latest
FROM project_evaluation 
WHERE evaluation_month = '2025-06' 
AND created_at >= '2025-06-11 12:00:00'
GROUP BY evaluation_type;

-- ===== 第二步：识别重复数据 =====
-- 2.1 查找所有重复评分
SELECT 
    '重复评分详情' as analysis_type,
    evaluator_id, 
    evaluatee_id, 
    project_id, 
    evaluation_type,
    evaluation_month,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id ORDER BY created_at) as record_ids,
    GROUP_CONCAT(CONCAT(score, '@', created_at) ORDER BY created_at SEPARATOR ' | ') as score_timeline
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07')
GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, evaluation_month;

-- ===== 第三步：执行修复（请逐步执行） =====

-- 3.1 第一阶段：修复明显错误的2025-06数据
-- 将6月11日12:00之后创建的2025-06数据更新为2025-07
-- 执行前请确认这些数据确实应该是2025-07
/*
-- 预览要修复的数据
SELECT 
    '即将修复的数据预览' as step,
    id, evaluator_id, evaluatee_id, project_id, evaluation_type, score, created_at
FROM project_evaluation 
WHERE evaluation_month = '2025-06' 
AND created_at >= '2025-06-11 12:00:00'
ORDER BY created_at;

-- 执行修复
UPDATE project_evaluation 
SET evaluation_month = '2025-07'
WHERE evaluation_month = '2025-06' 
AND created_at >= '2025-06-11 12:00:00';

-- 验证修复结果
SELECT 
    '第一阶段修复后统计' as step,
    evaluation_month, 
    evaluation_type, 
    COUNT(*) as count 
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
GROUP BY evaluation_month, evaluation_type;
*/

-- 3.2 第二阶段：处理重复数据
-- 创建临时表存储要保留的记录（保留最新的记录）
/*
CREATE TEMPORARY TABLE records_to_keep AS
SELECT 
    evaluator_id, 
    evaluatee_id, 
    project_id, 
    evaluation_type, 
    evaluation_month,
    MAX(id) as keep_id,
    MAX(created_at) as latest_created
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07')
GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month;

-- 预览要删除的重复记录
SELECT 
    '即将删除的重复记录' as step,
    pe.id, pe.evaluator_id, pe.evaluatee_id, pe.project_id, 
    pe.evaluation_type, pe.evaluation_month, pe.score, pe.created_at
FROM project_evaluation pe
LEFT JOIN records_to_keep rtk ON 
    pe.evaluator_id = rtk.evaluator_id 
    AND pe.evaluatee_id = rtk.evaluatee_id 
    AND pe.project_id = rtk.project_id 
    AND pe.evaluation_type = rtk.evaluation_type 
    AND pe.evaluation_month = rtk.evaluation_month
    AND pe.id = rtk.keep_id
WHERE pe.evaluation_month IN ('2025-06', '2025-07')
AND rtk.keep_id IS NULL
ORDER BY pe.evaluator_id, pe.evaluatee_id, pe.project_id, pe.created_at;

-- 删除重复记录
DELETE pe FROM project_evaluation pe
LEFT JOIN records_to_keep rtk ON 
    pe.evaluator_id = rtk.evaluator_id 
    AND pe.evaluatee_id = rtk.evaluatee_id 
    AND pe.project_id = rtk.project_id 
    AND pe.evaluation_type = rtk.evaluation_type 
    AND pe.evaluation_month = rtk.evaluation_month
    AND pe.id = rtk.keep_id
WHERE pe.evaluation_month IN ('2025-06', '2025-07')
AND rtk.keep_id IS NULL;

DROP TEMPORARY TABLE records_to_keep;
*/

-- ===== 第四步：最终验证 =====
-- 4.1 验证修复后的数据分布
SELECT 
    '最终数据分布' as verification_type,
    evaluation_month, 
    evaluation_type, 
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
GROUP BY evaluation_month, evaluation_type 
ORDER BY evaluation_month, evaluation_type;

-- 4.2 确认没有重复数据
SELECT 
    '重复数据检查' as verification_type,
    CASE WHEN COUNT(*) = 0 THEN '无重复数据' ELSE CONCAT('仍有', COUNT(*), '组重复数据') END as result
FROM (
    SELECT 
        evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month,
        COUNT(*) as cnt
    FROM project_evaluation 
    WHERE evaluation_month IN ('2025-06', '2025-07')
    GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month
    HAVING COUNT(*) > 1
) duplicates;

-- ===== 第五步：回滚方案（如果需要） =====
/*
-- 如果修复出现问题，可以使用以下语句回滚
DELETE FROM project_evaluation WHERE evaluation_month IN ('2025-06', '2025-07');
INSERT INTO project_evaluation SELECT * FROM project_evaluation_backup_20250715;
*/

-- ===== 第六步：清理（确认修复成功后执行） =====
-- DROP TABLE project_evaluation_backup_20250715;
