package com.kc.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Arrays;
import java.util.Date;
import java.util.Calendar;
import java.math.BigDecimal;

import com.kc.common.core.domain.model.LoginUser;
import com.kc.system.domain.ProjectWorkload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kc.system.mapper.ProjectInfoMapper;
import com.kc.system.domain.ProjectInfo;
import com.kc.system.service.IProjectInfoService;
import com.kc.system.mapper.ProjectMembersMapper;
import com.kc.system.domain.ProjectMembers;
import com.kc.system.mapper.ProjectWorkloadMapper;
import org.springframework.transaction.annotation.Transactional;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;
import com.kc.common.utils.DateUtils;
import com.kc.common.utils.StringUtils;
import com.kc.common.exception.ServiceException;
import com.kc.common.utils.SecurityUtils;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.system.mapper.ProjectParticipationMapper;
import com.kc.system.mapper.ProjectEvaluationMapper;


/**
 * 项目基础信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-14
 */
@Service
public class ProjectInfoServiceImpl implements IProjectInfoService 
{
    private static final Logger log = LoggerFactory.getLogger(ProjectInfoServiceImpl.class);
    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ProjectMembersMapper projectMembersMapper;

    @Autowired
    private ProjectWorkloadMapper projectWorkloadMapper;

    @Autowired
    private ProjectParticipationMapper projectParticipationMapper;
    
    @Autowired
    private ProjectEvaluationMapper projectEvaluationMapper;

    /**
     * 查询项目基础信息
     * 
     * @param id 项目基础信息主键
     * @return 项目基础信息
     */
    @Override
    public ProjectInfo selectProjectInfoById(Long id)
    {
        ProjectInfo projectInfo = projectInfoMapper.selectProjectInfoById(id);
        if (projectInfo != null) {
            // 查询项目成员
            List<Map<String, Object>> members = projectMembersMapper.selectProjectMembersByProjectId(id);
            projectInfo.setMemberList(members);
        }
        return projectInfo;
    }

    /**
     * 查询项目基础信息列表
     * 
     * @param projectInfo 项目基础信息
     * @return 项目基础信息
     */
    @Override
    public List<ProjectInfo> selectProjectInfoList(ProjectInfo projectInfo)
    {
        List<ProjectInfo> projectList = projectInfoMapper.selectProjectInfoList(projectInfo);
        
        // 为每个项目组装成员信息
        for (ProjectInfo project : projectList) {
            // 查询项目成员
            List<Map<String, Object>> members = projectMembersMapper.selectProjectMembersByProjectId(project.getId());
            if (members != null) {
                for (Map<String, Object> member : members) {
                    String userName = (String) member.get("userName");
                    String nickName = (String) member.get("nickName");
                    String role = (String) member.get("role");
                    
                    // 使用昵称或用户名
                    String displayName = nickName != null ? nickName : userName;
                    
                    // 转换角色格式
                    if ("负责人".equals(role)) {
                        role = "负责人";
                    } else if ("参与".equals(role)) {
                        role = "参与";
                    }
                    
                    // 更新成员信息
                    member.put("nickName", displayName);
                    member.put("role", role);
                }
            }
            project.setMemberList(members);
        }
        
        return projectList;
    }

    /**
     * 新增项目基础信息
     * 
     * @param projectInfo 项目基础信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertProjectInfo(ProjectInfo projectInfo) {
        // 插入项目基本信息
        int rows = projectInfoMapper.insertProjectInfo(projectInfo);
        // projectInfo 的 id 字段会被 MyBatis 自动设置为自增的 ID
        
        // 如果有负责人信息，添加到 project_members 表
        if (StringUtils.isNotEmpty(projectInfo.getLeaderName())) {
            ProjectMembers leader = new ProjectMembers();
            leader.setProjectId(projectInfo.getId());  // 使用新生成的项目ID
            leader.setUserName(projectInfo.getLeaderName());
            leader.setRole("负责人");
            leader.setCreatedAt(new Date());
            leader.setUpdatedAt(new Date());
            projectMembersMapper.insertProjectMembers(leader);
        }
        
        // 处理配合人员
        if (StringUtils.isNotEmpty(projectInfo.getMemberNames())) {
            String[] memberNames = projectInfo.getMemberNames().split(",");
            for (String userName : memberNames) {
                if (StringUtils.isNotEmpty(userName)) {
                    ProjectMembers assistant = new ProjectMembers();
                    assistant.setProjectId(projectInfo.getId());
                    assistant.setUserName(userName.trim());
                    assistant.setRole("配合人员");
                    assistant.setCreatedAt(new Date());
                    assistant.setUpdatedAt(new Date());
                    projectMembersMapper.insertProjectMembers(assistant);
                }
            }
        }
        
        return rows;
    }

    /**
     * 修改项目基础信息
     * 
     * @param projectInfo 项目基础信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateProjectInfo(ProjectInfo projectInfo) {
        // 1. 更新项目基本信息
        int rows = projectInfoMapper.updateProjectInfo(projectInfo);
        
        // 2. 删除所有现有成员关联
        projectMembersMapper.deleteProjectMembersByProjectId(projectInfo.getId());
        
        // 3. 添加项目负责人
        if (StringUtils.isNotEmpty(projectInfo.getLeaderName())) {
            ProjectMembers leader = new ProjectMembers();
            leader.setProjectId(projectInfo.getId());
            leader.setUserName(projectInfo.getLeaderName());
            leader.setRole("负责人");
            leader.setCreatedAt(new Date());
            leader.setUpdatedAt(new Date());
            projectMembersMapper.insertProjectMembers(leader);
        }
        
        // 4. 添加配合人员
        if (StringUtils.isNotEmpty(projectInfo.getMemberNames())) {
            String[] memberNames = projectInfo.getMemberNames().split(",");
            for (String userName : memberNames) {
                if (StringUtils.isNotEmpty(userName)) {
                    ProjectMembers assistant = new ProjectMembers();
                    assistant.setProjectId(projectInfo.getId());
                    assistant.setUserName(userName.trim());
                    assistant.setRole("配合人员");
                    assistant.setCreatedAt(new Date());
                    assistant.setUpdatedAt(new Date());
                    projectMembersMapper.insertProjectMembers(assistant);
                }
            }
        }

        // 5. 添加项目成员（参与角色）
        if (StringUtils.isNotEmpty(projectInfo.getProjectMembers())) {
            String[] projectMembers = projectInfo.getProjectMembers().split(",");
            for (String userName : projectMembers) {
                if (StringUtils.isNotEmpty(userName)) {
                    ProjectMembers member = new ProjectMembers();
                    member.setProjectId(projectInfo.getId());
                    member.setUserName(userName.trim());
                    member.setRole("参与");
                    member.setCreatedAt(new Date());
                    member.setUpdatedAt(new Date());
                    projectMembersMapper.insertProjectMembers(member);
                }
            }
        }
        
        return rows;
    }

    /**
     * 批量删除项目基础信息
     * 
     * @param ids 需要删除的项目基础信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteProjectInfoByIds(Long[] ids)
    {
        for (Long id : ids)
        {
            // 1. 删除项目成员关联
            projectMembersMapper.deleteProjectMembersByProjectId(id);
            // 2. 删除项目工时记录
            projectWorkloadMapper.deleteProjectWorkloadByProjectId(id);
            // 3. 删除项目参与度分配记录
            projectParticipationMapper.deleteProjectParticipationByProjectId(id);
            // 4. 删除项目评价记录
            projectEvaluationMapper.deleteProjectEvaluationByProjectId(id);
        }
        // 5. 删除项目信息
        return projectInfoMapper.deleteProjectInfoByIds(ids);
    }

    /**
     * 删除项目基础信息信息
     * 
     * @param id 项目基础信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteProjectInfoById(Long id)
    {
        // 1. 删除项目成员关联
        projectMembersMapper.deleteProjectMembersByProjectId(id);
        // 2. 删除项目工时记录
        projectWorkloadMapper.deleteProjectWorkloadByProjectId(id);
        // 3. 删除项目参与度分配记录
        projectParticipationMapper.deleteProjectParticipationByProjectId(id);
        // 4. 删除项目评价记录
        projectEvaluationMapper.deleteProjectEvaluationByProjectId(id);
        // 5. 删除项目信息
        return projectInfoMapper.deleteProjectInfoById(id);
    }

    @Override
    public List<ProjectInfo> selectUserLeadProjectList(String userName) {
        List<ProjectInfo> projectList = projectInfoMapper.selectUserLeadProjectList(userName);
        // 为每个项目组装成员信息
        for (ProjectInfo project : projectList) {
            List<Map<String, Object>> members = projectMembersMapper.selectProjectMembersByProjectId(project.getId());
            if (members != null) {
                for (Map<String, Object> member : members) {
                    String memberUserName = (String) member.get("userName");
                    String nickName = (String) member.get("nickName");
                    String role = (String) member.get("role");
                    
                    // 使用昵称或用户名，并直接组合角色
                    String displayName = (nickName != null ? nickName : memberUserName) + "-" + role;
                    
                    // 直接设置组合后的显示名称
                    member.put("nickName", displayName);
                }
            }
            project.setMemberList(members);
        }
        return projectList;
    }

    /**
     * 查询用户所有项目工时
     */
    @Override
    public List<Map<String, Object>> selectUserProjectWorkloads(String userName, String month) {
        return projectInfoMapper.selectUserProjectWorkloads(userName, month);
    }

    /**
     * 查询用户负责的项目列表
     * 
     * @param userName 用户名
     * @return 项目列表
     */
    @Override
    public List<ProjectInfo> getUserLeadProjects(String userName)
    {
        return projectInfoMapper.selectUserLeadProjectList(userName);
    }

    /**
     * 导入项目数据
     * 
     * @param projectList 项目数据列表
     * @param updateSupport 是否更新已存在的项目数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importProjectInfo(List<ProjectInfo> projectList, Boolean updateSupport, String operName) {
        if (StringUtils.isNull(projectList) || projectList.size() == 0) {
            throw new ServiceException("导入项目数据不能为空！");
        }
        
        // 添加调试日志
        log.info("开始导入项目数据，共 {} 条记录", projectList.size());
        for (ProjectInfo p : projectList) {
            log.info("导入数据: 项目名称=[{}], 部门ID=[{}], 简称=[{}]", 
                p.getProjectName(), p.getDeptId(), p.getProjectShortName());
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ProjectInfo project : projectList) {
            try {
                // 添加调试日志
                log.info("正在处理项目数据: {}", project);
                
                // 验证项目名称
                if (project == null || StringUtils.isEmpty(project.getProjectName())) {
                    failureNum++;
                    String errorMsg = "<br/>" + failureNum + "、导入失败：项目名称不能为空";
                    log.error(errorMsg);
                    failureMsg.append(errorMsg);
                    continue;
                }

                // 验证部门ID
                if (project.getDeptId() == null) {
                    failureNum++;
                    String errorMsg = "<br/>" + failureNum + "、项目 [" + project.getProjectName() 
                        + "] 导入失败：部门ID不能为空（请检查Excel中'部门ID'列是否正确填写，必须是数字格式）";
                    log.error(errorMsg);
                    failureMsg.append(errorMsg);
                    continue;
                }

                // 验证部门ID是否有效
                if (!isValidDeptId(project.getDeptId())) {
                    failureNum++;
                    String errorMsg = "<br/>" + failureNum + "、项目 [" + project.getProjectName() 
                        + "] 导入失败：部门ID [" + project.getDeptId() 
                        + "] 无效（有效的部门ID：101,102,200,201,202,203,204）";
                    log.error(errorMsg);
                    failureMsg.append(errorMsg);
                    continue;
                }

                // 检查重复
                ProjectInfo existingProject = projectInfoMapper.selectProjectInfoByNameAndDept(
                    project.getProjectName(), 
                    project.getDeptId()
                );
                
                if (StringUtils.isNull(existingProject)) {
                    this.insertProjectInfo(project);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、项目 [" + project.getProjectName() + "] 导入成功");
                } else if (updateSupport) {
                    project.setId(existingProject.getId());
                    this.updateProjectInfo(project);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、项目 [" + project.getProjectName() + "] 更新成功");
                } else {
                    failureNum++;
                    String errorMsg = "<br/>" + failureNum + "、项目 [" + project.getProjectName() 
                        + "] 在部门 [" + project.getDeptId() + "] 下已存在";
                    log.error(errorMsg);
                    failureMsg.append(errorMsg);
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、项目 [" + project.getProjectName() + "] 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error("导入项目 [{}] 失败: {}", project.getProjectName(), e.getMessage(), e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 验证部门ID是否有效
     */
    private boolean isValidDeptId(Long deptId) {
        // 这里添加有效的部门ID列表
        Long[] validDeptIds = {101L, 102L, 200L, 201L, 202L, 203L, 204L};
        return Arrays.asList(validDeptIds).contains(deptId);
    }

    @Override
    public boolean checkProjectNameExists(String projectName, Long deptId) {
        ProjectInfo existingProject = projectInfoMapper.selectProjectInfoByNameAndDept(projectName, deptId);
        return existingProject != null;
    }

    @Override
    public List<ProjectInfo> selectProjectsByDeptId(Long deptId) {
        List<ProjectInfo> projectList = projectInfoMapper.selectProjectsByDeptId(deptId);
        // 为每个项目组装成员信息
        for (ProjectInfo project : projectList) {
            List<Map<String, Object>> members = projectMembersMapper.selectProjectMembersByProjectId(project.getId());
            if (members != null) {
                for (Map<String, Object> member : members) {
                    String memberUserName = (String) member.get("userName");
                    String nickName = (String) member.get("nickName");
                    String role = (String) member.get("role");
                    
                    // 使用昵称或用户名，并直接组合角色
                    String displayName = (nickName != null ? nickName : memberUserName) + "-" + role;
                    
                    // 直接设置组合后的显示名称
                    member.put("nickName", displayName);
                }
            }
            project.setMemberList(members);
        }
        return projectList;
    }

    @Override
    @Transactional
    public String batchAddWorkload() {
        // 获取当前月份
        String currentMonth = DateUtils.parseDateToStr("yyyy-MM", new Date());
        
        // 获取上个月
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        String lastMonth = DateUtils.parseDateToStr("yyyy-MM", calendar.getTime());
        
        log.info("开始批量填报工时，当前月份: {}, 上个月: {}", currentMonth, lastMonth);
        
        // 获取当前用户部门ID
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long deptId = loginUser.getUser().getDeptId();
        
        log.info("当前用户部门ID: {}", deptId);
        
        // 获取部门下所有项目
        List<ProjectInfo> projectList = projectInfoMapper.selectProjectsByDeptId(deptId);
        
        log.info("部门下的项目数量: {}", projectList.size());
        
        int successCount = 0;
        int failureCount = 0;
        StringBuilder message = new StringBuilder();
        
        for (ProjectInfo project : projectList) {
            log.info("处理项目: {}, ID: {}", project.getProjectName(), project.getId());
            
            // 获取项目所有成员，注意更改为直接从mapper获取完整的成员信息
            List<Map<String, Object>> members = projectMembersMapper.selectMemberDetailsByProjectId(project.getId());
            
            if (members == null || members.isEmpty()) {
                log.info("项目{}没有成员，跳过", project.getProjectName());
                continue;
            }
            
            log.info("项目 {} 的成员数量: {}", project.getProjectName(), members.size());
            
            for (Map<String, Object> member : members) {
                String userName = (String) member.get("userName");
                String nickName = (String) member.get("nickName");
                
                if (StringUtils.isEmpty(userName)) {
                    log.warn("项目{}的成员缺少用户名，跳过", project.getProjectName());
                    continue;
                }
                
                log.info("处理成员: {}, 用户名: {}", nickName, userName);
                
                try {
                    // 获取用户上个月的所有项目工时记录
                    List<Map<String, Object>> lastMonthWorkloads = projectInfoMapper.selectUserProjectWorkloads(userName, lastMonth);
                    
                    log.info("用户 {} 上个月的工时记录数量: {}", userName, lastMonthWorkloads.size());
                    
                    // 打印出所有工时记录，用于调试
                    for (Map<String, Object> record : lastMonthWorkloads) {
                        log.info("工时记录详情: 项目ID={}, 项目名称={}, 参与度={}", 
                            record.get("projectId"), record.get("projectName"), record.get("involvement"));
                    }
                    
                    // 查找当前项目的上个月工时记录
                    Double lastMonthInvolvement = null;
                    Long projectId = project.getId();
                    
                    for (Map<String, Object> workload : lastMonthWorkloads) {
                        Object projectIdObj = workload.get("projectId");
                        Long workloadProjectId = null;
                        
                        if (projectIdObj instanceof Long) {
                            workloadProjectId = (Long) projectIdObj;
                        } else if (projectIdObj instanceof Integer) {
                            workloadProjectId = ((Integer) projectIdObj).longValue();
                        } else if (projectIdObj instanceof String) {
                            try {
                                workloadProjectId = Long.parseLong((String) projectIdObj);
                            } catch (NumberFormatException e) {
                                log.warn("无法将 {} 转换为Long类型", projectIdObj);
                            }
                        }
                        
                        // 比较当前项目ID和工时记录中的项目ID
                        if (workloadProjectId != null && workloadProjectId.equals(projectId)) {
                            Object involvementObj = workload.get("involvement");
                            log.info("找到匹配的项目工时记录: 项目ID={}, 参与度类型={}, 值={}", 
                                workloadProjectId, involvementObj != null ? involvementObj.getClass().getName() : "null", involvementObj);
                            
                            if (involvementObj != null) {
                                if (involvementObj instanceof Double) {
                                    lastMonthInvolvement = (Double) involvementObj;
                                } else if (involvementObj instanceof Integer) {
                                    lastMonthInvolvement = ((Integer) involvementObj).doubleValue();
                                } else if (involvementObj instanceof BigDecimal) {
                                    lastMonthInvolvement = ((BigDecimal) involvementObj).doubleValue();
                                } else if (involvementObj instanceof String) {
                                    try {
                                        lastMonthInvolvement = Double.parseDouble((String) involvementObj);
                                    } catch (NumberFormatException e) {
                                        log.warn("无法将 {} 转换为Double类型", involvementObj);
                                    }
                                } else if (involvementObj instanceof Number) {
                                    lastMonthInvolvement = ((Number) involvementObj).doubleValue();
                                }
                                
                                // 检查值是否有效
                                if (lastMonthInvolvement != null && lastMonthInvolvement < 0) {
                                    lastMonthInvolvement = 0.0; // 如果是负数，设为0
                                }
                                
                                log.info("成功提取上个月参与度: {}", lastMonthInvolvement);
                            }
                            break;
                        }
                    }
                    
                    // 如果上个月有记录，则使用上个月的值，否则使用0
                    Double involvement = (lastMonthInvolvement != null && lastMonthInvolvement >= 0) ? lastMonthInvolvement : 0.0;
                    
                    log.info("最终决定的参与度值: {}", involvement);
                    
                    // 检查本月是否已有记录
                    List<Map<String, Object>> currentMonthWorkloads = projectInfoMapper.selectUserProjectWorkloads(userName, currentMonth);
                    boolean hasCurrentMonthRecord = false;
                    
                    for (Map<String, Object> workload : currentMonthWorkloads) {
                        Object projectIdObj = workload.get("projectId");
                        Long workloadProjectId = null;
                        
                        if (projectIdObj instanceof Long) {
                            workloadProjectId = (Long) projectIdObj;
                        } else if (projectIdObj instanceof Integer) {
                            workloadProjectId = ((Integer) projectIdObj).longValue();
                        } else if (projectIdObj instanceof String) {
                            try {
                                workloadProjectId = Long.parseLong((String) projectIdObj);
                            } catch (NumberFormatException e) {
                                log.warn("无法将 {} 转换为Long类型", projectIdObj);
                            }
                        }
                        
                        // 获取参与度值
                        Object involvementObj = workload.get("involvement");
                        double workloadInvolvement = -1;
                        if (involvementObj instanceof Number) {
                            workloadInvolvement = ((Number) involvementObj).doubleValue();
                        }
                        
                        // 只有当项目ID匹配且有有效的参与度值（大于或等于0）时才认为有记录
                        if (workloadProjectId != null && workloadProjectId.equals(projectId) && workloadInvolvement >= 0) {
                            hasCurrentMonthRecord = true;
                            log.info("本月已有工时记录，跳过: 用户={}, 项目={}, 参与度={}", userName, project.getProjectName(), workloadInvolvement);
                            break;
                        }
                    }
                    
                    if (!hasCurrentMonthRecord) {
                        // 创建新的工时记录，使用正确的对象类型
                        ProjectWorkload workload = new ProjectWorkload();
                        workload.setProjectId(projectId);
                        workload.setUserName(userName);
                        workload.setWorkMonth(currentMonth);
                        workload.setInvolvement(new BigDecimal(involvement.toString()));
                        workload.setCreatedAt(new Date());
                        workload.setUpdatedAt(new Date());
                        
                        log.info("准备插入工时记录: 项目ID={}, 用户名={}, 参与度={}", 
                            projectId, userName, involvement);
                            
                        projectWorkloadMapper.insertProjectWorkload(workload);
                        successCount++;
                        log.info("成功添加工时记录: 项目={}, 用户={}, 月份={}, 参与度={}", 
                            project.getProjectName(), userName, currentMonth, involvement);
                    } else {
                        log.info("用户{}在项目{}的{}月工时记录已存在，跳过", 
                            userName, project.getProjectName(), currentMonth);
                    }
                } catch (Exception e) {
                    failureCount++;
                    log.error("添加工时记录失败: 项目={}, 用户={}, 月份={}, 错误={}", 
                        project.getProjectName(), userName, currentMonth, e.getMessage(), e);
                }
            }
        }
        
        message.append("批量填报完成：成功 ").append(successCount).append(" 条");
        if (failureCount > 0) {
            message.append("，失败 ").append(failureCount).append(" 条");
        }
        
        return message.toString();
    }

    /**
     * 检查项目是否有依赖关系（精力分配和评分记录）
     * 
     * @param projectId 项目ID
     * @return 依赖关系信息
     */
    @Override
    public Map<String, Object> checkProjectDependencies(Long projectId) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查是否有精力分配记录
        int participationCount = projectParticipationMapper.countProjectParticipationByProjectId(projectId);
        result.put("hasParticipation", participationCount > 0);
        
        // 检查是否有评分记录
        int evaluationCount = projectEvaluationMapper.countProjectEvaluationByProjectId(projectId);
        result.put("hasEvaluation", evaluationCount > 0);
        
        // 是否可以删除
        boolean canDelete = participationCount == 0 && evaluationCount == 0;
        result.put("canDelete", canDelete);
        
        // 添加详细信息
        if (participationCount > 0) {
            result.put("participationCount", participationCount);
        }
        if (evaluationCount > 0) {
            result.put("evaluationCount", evaluationCount);
        }
        
        return result;
    }

    /**
     * 获取用户可选择的项目列表（排除承揽项目）
     *
     * @return 可选择的项目列表
     */
    @Override
    public List<ProjectInfo> selectAvailableProjects() {
        ProjectInfo queryParam = new ProjectInfo();
        List<ProjectInfo> allProjects = projectInfoMapper.selectProjectInfoList(queryParam);

        // 过滤掉承揽项目
        return allProjects.stream()
            .filter(project -> !"承揽项目".equals(project.getRemarks()))
            .collect(Collectors.toList());
    }

}
