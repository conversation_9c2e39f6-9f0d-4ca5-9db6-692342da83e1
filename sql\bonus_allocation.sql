-- 奖金分配相关表结构

-- 1. 部门奖金分配表
DROP TABLE IF EXISTS `dept_bonus_allocation`;
CREATE TABLE `dept_bonus_allocation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `dept_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '部门名称(冗余)',
  `allocation_month` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分配月份 格式：yyyy-MM',
  `performance_rank` int(11) NOT NULL COMMENT '绩效排名(1为最好)',
  `total_bonus` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '部门总奖金(可为负数)',
  `allocated_bonus` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '已分配奖金',
  `remaining_bonus` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '剩余奖金',
  `allocation_status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '分配状态(0-未分配,1-部分分配,2-已完成)',
  `allocator_id` bigint(20) NULL DEFAULT NULL COMMENT '分配人ID(薪酬考核人员)',
  `allocator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分配人姓名',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_dept_month`(`dept_id`, `allocation_month`) USING BTREE COMMENT '部门月份唯一索引',
  INDEX `idx_allocation_month`(`allocation_month`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门奖金分配表' ROW_FORMAT = Dynamic;

-- 2. 员工奖金分配表
DROP TABLE IF EXISTS `employee_bonus_allocation`;
CREATE TABLE `employee_bonus_allocation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dept_bonus_id` bigint(20) NOT NULL COMMENT '部门奖金分配ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
  `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `allocation_month` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分配月份 格式：yyyy-MM',
  `bonus_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '分配奖金金额(可为负数)',
  `allocation_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分配原因/备注',
  `allocator_id` bigint(20) NOT NULL COMMENT '分配人ID(部门负责人)',
  `allocator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分配人姓名',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_month`(`user_id`, `allocation_month`) USING BTREE COMMENT '用户月份唯一索引',
  INDEX `idx_dept_bonus_id`(`dept_bonus_id`) USING BTREE,
  INDEX `idx_allocation_month`(`allocation_month`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE,
  CONSTRAINT `fk_employee_bonus_dept` FOREIGN KEY (`dept_bonus_id`) REFERENCES `dept_bonus_allocation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工奖金分配表' ROW_FORMAT = Dynamic;

-- 3. 奖金分配日志表
DROP TABLE IF EXISTS `bonus_allocation_log`;
CREATE TABLE `bonus_allocation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型(DEPT_ALLOCATE-部门分配,EMPLOYEE_ALLOCATE-员工分配,MODIFY-修改,DELETE-删除)',
  `target_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '目标类型(DEPT-部门,EMPLOYEE-员工)',
  `target_id` bigint(20) NOT NULL COMMENT '目标ID',
  `allocation_month` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分配月份',
  `amount` decimal(12,2) NOT NULL COMMENT '金额',
  `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人姓名',
  `operation_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_allocation_month`(`allocation_month`) USING BTREE,
  INDEX `idx_operator_id`(`operator_id`) USING BTREE,
  INDEX `idx_target`(`target_type`, `target_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '奖金分配日志表' ROW_FORMAT = Dynamic;

-- 插入示例数据
INSERT INTO `dept_bonus_allocation` VALUES
(1, 200, '研发部', '2025-01', 1, 50000.00, 0.00, 50000.00, '0', 1, 'admin', 'admin', '2025-01-01 10:00:00', '', NULL, '绩效排名第一'),
(2, 201, '市场部', '2025-01', 2, 30000.00, 0.00, 30000.00, '0', 1, 'admin', 'admin', '2025-01-01 10:00:00', '', NULL, '绩效排名第二'),
(3, 202, '财务部', '2025-01', 3, 10000.00, 0.00, 10000.00, '0', 1, 'admin', 'admin', '2025-01-01 10:00:00', '', NULL, '绩效排名第三'),
(4, 204, '人事部', '2025-01', 4, -20000.00, 0.00, -20000.00, '0', 1, 'admin', 'admin', '2025-01-01 10:00:00', '', NULL, '绩效排名倒数第一，扣款');

-- 菜单权限配置
-- 奖金管理主菜单
INSERT INTO sys_menu VALUES(2100, '奖金管理', 0, 6, 'bonus', NULL, NULL, '', 1, 0, 'M', '0', '0', '', 'money', 'admin', sysdate(), '', null, '奖金分配管理菜单');

-- 部门奖金分配菜单
INSERT INTO sys_menu VALUES(2101, '部门奖金分配', 2100, 1, 'dept-bonus', 'system/bonus/dept-bonus-allocation', NULL, '', 1, 0, 'C', '0', '0', 'system:deptBonus:list', 'peoples', 'admin', sysdate(), '', null, '部门奖金分配菜单');

-- 部门奖金分配按钮
INSERT INTO sys_menu VALUES(2102, '部门奖金分配查询', 2101, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:deptBonus:query', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES(2103, '部门奖金分配新增', 2101, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:deptBonus:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES(2104, '部门奖金分配修改', 2101, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:deptBonus:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES(2105, '部门奖金分配删除', 2101, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:deptBonus:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES(2106, '部门奖金分配导出', 2101, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:deptBonus:export', '#', 'admin', sysdate(), '', null, '');

-- 员工奖金分配菜单
INSERT INTO sys_menu VALUES(2107, '员工奖金分配', 2100, 2, 'employee-bonus', 'system/bonus/employee-bonus-allocation', NULL, '', 1, 0, 'C', '0', '0', 'system:employeeBonus:list', 'user', 'admin', sysdate(), '', null, '员工奖金分配菜单');

-- 员工奖金分配按钮
INSERT INTO sys_menu VALUES(2108, '员工奖金分配查询', 2107, 1, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:employeeBonus:query', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES(2109, '员工奖金分配新增', 2107, 2, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:employeeBonus:add', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES(2110, '员工奖金分配修改', 2107, 3, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:employeeBonus:edit', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES(2111, '员工奖金分配删除', 2107, 4, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:employeeBonus:remove', '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu VALUES(2112, '员工奖金分配导出', 2107, 5, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:employeeBonus:export', '#', 'admin', sysdate(), '', null, '');

-- 机构负责人评价权限（在现有评价菜单下添加）
INSERT INTO sys_menu VALUES(2113, '机构负责人评价', 1061, 6, '', '', NULL, '', 1, 0, 'F', '0', '0', 'system:evaluation:manager', '#', 'admin', sysdate(), '', null, '机构负责人评价权限');

-- 字典数据：分配状态
INSERT INTO sys_dict_type VALUES(100, '分配状态', 'allocation_status', '0', 'admin', sysdate(), '', null, '奖金分配状态列表');

INSERT INTO sys_dict_data VALUES(100, 1, '未分配', '0', 'allocation_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '未分配状态');
INSERT INTO sys_dict_data VALUES(101, 2, '部分分配', '1', 'allocation_status', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '部分分配状态');
INSERT INTO sys_dict_data VALUES(102, 3, '已完成', '2', 'allocation_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '已完成状态');
