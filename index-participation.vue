<template>
  <div class="app-container">
    <div class="kc-content-layout">
      <!-- 左侧部门成员列表 -->
      <div class="kc-sidebar">
        <div class="kc-sidebar-header">
          <h3>部门成员列表</h3>
          <div class="kc-dept-name">{{ currentDeptName || '部门负责人管理的所有部门' }}</div>
        </div>
        <div class="kc-sidebar-search">
          <el-input
            v-model="memberSearchKeyword"
            placeholder="搜索成员"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter.native="filterMembers"
          />
        </div>
        <div class="kc-sidebar-list">
          <el-scrollbar style="height: calc(100vh - 220px);">
            <div
              v-for="member in filteredDeptMembers"
              :key="member.userId"
              class="kc-member-item"
              :class="{ 'kc-member-active': currentMemberId === member.userId }"
              @click="selectMember(member)"
            >
              <div class="kc-member-info">
                <div class="kc-member-name">{{ member.nickName || member.userName }}</div>
                <div class="kc-member-post">{{ member.deptName }}</div>
              </div>
            </div>
            <div v-if="filteredDeptMembers.length === 0" class="kc-empty-text">
              暂无成员数据
            </div>
          </el-scrollbar>
        </div>
      </div>

      <!-- 右侧项目参与度分配信息 -->
      <div class="kc-main-content">
        <div class="kc-content-header">
          <div class="kc-header-title">
            <span v-if="currentMember">{{ currentMember.nickName || currentMember.userName }} 的项目参与情况</span>
            <span v-else>请选择左侧成员查看项目参与情况</span>
          </div>
          <div class="kc-header-tools">
            <div class="kc-month-selector">
              <el-date-picker
                v-model="selectedMonth"
                type="month"
                placeholder="选择月份"
                value-format="yyyy-MM"
                size="mini"
                @change="handleMonthChange"
                style="width: 150px; margin-right: 10px;"
              />
            </div>
            <el-button
              type="primary"
              plain
              icon="el-icon-refresh"
              size="mini"
              @click="refreshAllData"
              :loading="loading"
              v-if="currentMember"
            >刷新数据</el-button>
            <!-- <el-button
              type="success"
              plain
              icon="el-icon-check"
              size="mini"
              @click="saveAllEffortData"
              :loading="saving"
              v-if="currentMember && userProjects.length > 0"
            >保存精力分配</el-button> -->
          </div>
        </div>

        <div v-loading="loading" class="kc-participation-content">
          <!-- 没有选择成员时的提示 -->
          <div v-if="!currentMember" class="kc-empty-content">
            <i class="el-icon-user-solid"></i>
            <p>请从左侧选择成员查看项目参与情况</p>
          </div>

          <!-- 有选择成员时的内容 -->
          <div v-else>
            <!-- 项目表格 -->
            <div class="kc-section">
              <div class="kc-section-header">
                <h3>参与的项目 <span v-if="userProjects && userProjects.length > 0">({{ userProjects.length }})</span></h3>
                <!-- <div v-if="hasSubmittedThisMonth" class="kc-submitted-info">
                  <el-tag type="success" size="small">本月已提交</el-tag>
                  <el-tooltip effect="dark" content="本月已提交精力分配数据，您可以查看但不能再次提交" placement="top">
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                </div> -->
                <div v-if="userProjects && userProjects.length > 0" class="kc-effort-summary">
                  <span>精力分配总和: </span>
                  <span :class="{ 'kc-effort-valid': isEffortValid, 'kc-effort-invalid': !isEffortValid }">
                    {{ totalDisplayEffort }}%
                  </span>
                  <el-tooltip v-if="!isEffortValid" effect="dark" content="精力分配总和应为100%" placement="top">
                    <i class="el-icon-warning-outline"></i>
                  </el-tooltip>
                </div>
              </div>

              <!-- 项目数据表格 -->
              <div v-if="userProjects && userProjects.length > 0" class="kc-section-content">
                <el-table
                  :data="userProjects"
                  border
                  stripe
                  style="width: 100%;"
                >
                  <el-table-column label="项目名称" prop="project_name" min-width="180" />
                  <!-- <el-table-column label="项目简称" prop="project_short_name" min-width="120" /> -->
                  <!-- <el-table-column label="角色" prop="role" width="100" /> -->
                  <el-table-column label="所属部门" prop="dept_name" min-width="150" />
                  <el-table-column label="精力分配" min-width="280">
                    <template slot-scope="scope">
                      <div class="effort-allocation-wrapper">
                        <div class="percent-input-wrapper">
                          <el-input
                            v-model="scope.row.displayEffort"
                            size="mini"
                            @change="handleDisplayEffortChange(scope.row)"
                            @keyup.native="handleInputKeyup($event, scope.row)"
                            @blur="handleInputBlur(scope.row)"
                            class="percent-input"
                            placeholder="请输入"
                          >
                            <template slot="append">%</template>
                          </el-input>
                        </div>
                        <div class="arrow-wrapper">
                          <div class="arrow-label">工作量归集</div>
                          <div class="arrow-icon">→</div>
                        </div>
                        <div class="project-select-wrapper">
                          <el-select
                            v-model="scope.row.selectedProjectId"
                            size="mini"
                            placeholder="请选择归集项目"
                            @change="handleProjectSelect(scope.row)"
                            class="project-select"
                            filterable
                            clearable
                            default-first-option
                          >
                            <el-option
                              v-for="project in availableProjects"
                              :key="project.id"
                              :label="project.projectName"
                              :value="project.id"
                            />
                          </el-select>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="备注" prop="remarks" min-width="150" />
                </el-table>

                <!-- 提交按钮区域 -->
                <div class="kc-submit-area">
                  <div class="kc-submit-container">
                    <el-button
                      type="primary"
                      :loading="submitting"
                      @click="submitEffortData"
                    >
                      {{ getSubmitButtonText }}
                    </el-button>
                    <span class="kc-submit-tip" v-if="hasSubmittedThisMonth || hasWorkloadThisMonth">
                      {{ getSubmitTipText }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 没有项目数据时的提示 -->
              <div v-else class="kc-empty-section">
                <i class="el-icon-document"></i>
                <p>暂无项目参与数据</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { batchAddParticipation, getUserMonthlyEffort, batchUpdateParticipation } from "@/api/system/participation";
import { getUserProfile } from "@/api/system/user";
import { listUser } from "@/api/system/user";
import { getDept } from "@/api/system/dept";
import { getAvailableProjects } from "@/api/system/info";
import { getUserProjects, getDeptMembersWithProjects } from "@/api/system/members";
import { listDept } from "@/api/system/dept";
import { addWorkload, updateWorkload, getUserMonthlyWorkload } from "@/api/system/workload";

export default {
  name: "Participation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 部门成员列表
      deptMembers: [],
      // 有项目的成员列表
      membersWithProjects: [],
      // 成员项目映射 - 用于判断用户是否有项目
      memberProjectsMap: {},
      // 当前选中的成员ID
      currentMemberId: null,
      // 当前选中的成员信息
      currentMember: null,
      // 成员搜索关键词
      memberSearchKeyword: "",
      // 用户参与的项目列表
      userProjects: [],
      // 项目参与度数据
      participationList: [],
      // 保存状态
      saving: false,
      // 提交状态
      submitting: false,
      // 当月已提交的精力分配数据
      submittedEffort: [],
      // 是否已提交本月数据
      hasSubmittedThisMonth: false,
      // 加载已提交数据状态
      loadingSubmitted: false,
      // 当前部门名称
      currentDeptName: "",
      // 当前部门ID
      currentDeptId: null,
      // 部门信息
      deptInfo: null,
      // 部门负责人
      deptLeader: null,
      // 部门负责人管理的所有部门
      leaderDepts: [],
      // 数据初始化状态
      initialized: false,
      // 可选择的项目列表（排除承揽项目）
      availableProjects: [],
      // 已提交的工作量归集
      submittedWorkload: [],
      // 是否已有工作量归集
      hasWorkloadThisMonth: false,
      // 选择的月份
      selectedMonth: '',
    };
  },
  computed: {
    // 筛选后的部门成员列表
    filteredDeptMembers() {
      if (!this.membersWithProjects || !this.membersWithProjects.length) {
        return [];
      }
      if (!this.memberSearchKeyword) {
        return this.membersWithProjects;
      }
      return this.membersWithProjects.filter(member =>
        (member.nickName && member.nickName.toLowerCase().includes(this.memberSearchKeyword.toLowerCase())) ||
        (member.userName && member.userName.toLowerCase().includes(this.memberSearchKeyword.toLowerCase()))
      );
    },

    // 计算精力分配总和
    totalEffort() {
      if (!this.userProjects || this.userProjects.length === 0) {
        return 0;
      }
      return this.userProjects.reduce((sum, project) => {
        return sum + (parseFloat(project.effort) || 0);
      }, 0).toFixed(2);
    },

    // 显示用的百分比总和
    totalDisplayEffort() {
      if (!this.userProjects || this.userProjects.length === 0) {
        return 0;
      }
      
      const total = this.userProjects.reduce((sum, project) => {
        // 确保转换为数字类型，处理空值情况
        const value = project.displayEffort === '' || project.displayEffort === null || project.displayEffort === undefined 
          ? 0 
          : Number(project.displayEffort) || 0;
        return sum + value;
      }, 0);
      
      return total;
    },

    // 判断精力分配总和是否有效
    isEffortValid() {
      const total = this.totalDisplayEffort;
      return Math.abs(total - 100) <= 1; // 允许1%的误差
    },

    // 判断是否有精力分配数据
    hasEffortData() {
      if (!this.userProjects || this.userProjects.length === 0) {
        return false;
      }
      // 只要有项目，就允许提交，不再检查effort值
      return true;
    },

    // 获取提交按钮文本
    getSubmitButtonText() {
      if (this.hasSubmittedThisMonth && this.hasWorkloadThisMonth) {
        return '更新精力分配和工作量归集';
      } else if (this.hasSubmittedThisMonth) {
        return '更新精力分配';
      } else if (this.hasWorkloadThisMonth) {
        return '提交精力分配（更新工作量归集）';
      } else {
        return '提交精力分配';
      }
    },

    // 获取提交提示文本
    getSubmitTipText() {
      if (this.hasSubmittedThisMonth && this.hasWorkloadThisMonth) {
        return '本月已提交精力分配和工作量归集，可以修改后更新';
      } else if (this.hasSubmittedThisMonth) {
        return '本月已提交精力分配，可以修改后更新';
      } else if (this.hasWorkloadThisMonth) {
        return '本月已有工作量归集，提交时将同步更新';
      } else {
        return '首次提交，将同时创建工作量归集';
      }
    }
  },
  created() {
    // 初始化默认月份为当前月份
    this.initDefaultMonth();
    this.initData();
  },
  methods: {
    // 初始化默认月份
    initDefaultMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      this.selectedMonth = `${year}-${month}`;
    },

    // 处理月份变化
    handleMonthChange(month) {
      if (month) {
        this.selectedMonth = month;
        // 如果已经选择了成员，重新加载该成员的数据
        if (this.currentMember) {
          this.loadMemberData(this.currentMember);
        }
      }
    },

    // 初始化数据
    initData() {
      // 设置初始化标志
      this.initialized = false;
      this.loading = true;
      
      // 检查当前用户信息
      if (!this.$store.state.user || !this.$store.state.user.id) {
        this.$message.error("无法获取当前用户信息，请重新登录");
        this.loading = false;
        return;
      }
      
      // 获取当前用户详细信息
      this.getCurrentUserInfo()
        .catch(error => {
          this.$message.error("初始化数据失败: " + (error.message || error));
          this.loading = false;
        });
    },
    
    // 获取当前用户信息
    getCurrentUserInfo() {
      return new Promise((resolve, reject) => {
        getUserProfile().then(response => {
          const userInfo = response.data;
          this.currentDeptId = userInfo.deptId;
          this.currentDeptName = userInfo.dept?.deptName || "当前部门";
          
          // 获取部门详细信息
          this.getDeptInfo()
            .then(resolve)
            .catch(reject);
        }).catch(error => {
          this.$message.error("获取用户信息失败：" + error);
          reject(error);
        });
      });
    },
    
    // 获取部门详细信息
    getDeptInfo() {
      return new Promise((resolve, reject) => {
        if (!this.currentDeptId) {
          this.loading = false;
          reject(new Error("没有部门ID"));
          return;
        }
        
        getDept(this.currentDeptId).then(response => {
          this.deptInfo = response.data;
          // 使用当前登录用户作为部门负责人（用于权限判断）
          const currentUser = this.$store.state.user.userName ||
                             this.$store.state.user.name ||
                             this.$store.getters.name ||
                             this.$store.getters.userName;

          // 如果能获取到当前用户，使用当前用户；否则使用当前部门负责人
          this.deptLeader = currentUser || this.deptInfo.leader;
          
          // 获取负责人管理的所有部门
          this.getLeaderDepts()
            .then(() => {
              // 获取所有相关部门的成员
              this.getAllDeptUsers()
                .then(resolve)
                .catch(reject);
            })
            .catch(error => {
              this.$message.error("获取部门负责人的部门列表失败：" + error);
              // 退回到只获取当前部门成员
              this.getDeptUsers()
                .then(resolve)
                .catch(reject);
            });
        }).catch(error => {
          this.$message.error("获取部门信息失败：" + error);
          // 仍然尝试获取部门成员
          this.getDeptUsers()
            .then(resolve)
            .catch(reject);
        });
      });
    },
    
    // 获取部门负责人管理的所有部门（包括子部门）
    getLeaderDepts() {
      return new Promise((resolve, reject) => {
        if (!this.deptLeader) {
          this.$message.warning("未找到部门负责人信息，将只显示当前部门成员");
          this.leaderDepts = [this.currentDeptId]; // 只包含当前部门
          resolve();
          return;
        }

        // 查询所有部门，然后在前端过滤
        listDept({}).then(response => {
          const allDepts = response.data || [];

          // 在前端过滤出负责人是当前登录用户的部门
          const directDepts = allDepts.filter(dept => dept.leader === this.deptLeader);

          if (directDepts.length === 0) {
            this.$message.warning("未找到有权限管理的部门，将只显示当前部门成员");
            this.leaderDepts = [this.currentDeptId]; // 只包含当前部门
          } else {
            // 判断用户是否为子部门负责人
            const isSubDeptLeader = this.isUserSubDeptLeader(directDepts, allDepts);

            if (isSubDeptLeader) {
              // 子部门负责人只能看到直接负责的部门
              this.leaderDepts = directDepts.map(dept => dept.deptId);
              this.currentDeptName = directDepts.map(dept => dept.deptName).join('、');
            } else {
              // 父部门负责人可以看到负责的部门及其子部门

              // 获取直接管理的部门ID
              this.leaderDepts = directDepts.map(dept => dept.deptId);

              // 查找所有子部门（基于ancestors字段）
              const allManagedDepts = [...this.leaderDepts];

              this.leaderDepts.forEach(parentDeptId => {
                const childDepts = allDepts.filter(dept => {
                  // 检查ancestors字段是否包含父部门ID
                  return dept.ancestors && dept.ancestors.split(',').includes(parentDeptId.toString());
                });

                childDepts.forEach(childDept => {
                  if (!allManagedDepts.includes(childDept.deptId)) {
                    allManagedDepts.push(childDept.deptId);
                  }
                });
              });

              this.leaderDepts = allManagedDepts;

              // 如果当前部门不在列表中且负责人与当前部门负责人一致，添加进去
              if (!this.leaderDepts.includes(this.currentDeptId) &&
                  this.deptInfo && this.deptInfo.leader === this.deptLeader) {
                this.leaderDepts.push(this.currentDeptId);
              }

              // 更新显示的部门名称为相关部门
              this.currentDeptName = directDepts.map(dept => dept.deptName).join('、');
              if (allManagedDepts.length > directDepts.length) {
                this.currentDeptName += '（含子部门）';
              }
            }
          }
          resolve();
        }).catch(error => {
          this.$message.error("获取部门列表失败：" + error);
          this.leaderDepts = [this.currentDeptId]; // 只包含当前部门
          reject(error);
        });
      });
    },

    // 判断用户是否为子部门负责人
    isUserSubDeptLeader(directDepts, allDepts) {
      // 如果没有负责任何部门，返回false
      if (!directDepts || directDepts.length === 0) {
        return false;
      }

      // 统计部门类型
      let topLevelDepts = 0;  // 顶级部门数量
      let subDepts = 0;       // 子部门数量
      let subDeptsWithOtherParentLeader = 0; // 有其他父部门负责人的子部门数量

      for (let dept of directDepts) {
        if (!dept.parentId || dept.parentId === 0) {
          // 顶级部门
          topLevelDepts++;
        } else {
          // 子部门
          subDepts++;

          // 检查父部门是否有其他负责人
          const parentDept = allDepts.find(d => d.deptId === dept.parentId);
          if (parentDept && parentDept.leader && parentDept.leader !== this.deptLeader) {
            // 有其他人负责父部门
            subDeptsWithOtherParentLeader++;
          }
        }
      }

      // 判断逻辑：
      // 1. 如果有任何顶级部门，则不是纯子部门负责人
      // 2. 如果所有部门都是子部门，且都有其他父部门负责人，则是纯子部门负责人
      const isPureSubDeptLeader = topLevelDepts === 0 && subDepts > 0 && subDeptsWithOtherParentLeader === subDepts;

      return isPureSubDeptLeader;
    },
    
    // 获取所有相关部门的成员
    getAllDeptUsers() {
      return new Promise((resolve, reject) => {
        if (this.leaderDepts.length === 0) {
          this.loading = false;
          reject(new Error("没有部门ID列表"));
          return;
        }
        
        // 使用新的API获取有项目的部门成员
        getDeptMembersWithProjects(this.leaderDepts).then(response => {
          if (response.code === 200 && response.data) {
            // 数据已经经过后端处理，只返回有项目的用户
            const membersWithProjects = response.data;
            
            // 将数据处理为前端需要的格式
            const formattedMembers = membersWithProjects.map(member => ({
              userId: member.user_id,
              userName: member.user_name,
              nickName: member.nick_name || member.user_name,
              deptId: member.dept_id,
              deptName: member.dept_name,
              projectCount: member.project_count || 0
            }));
            
            // 直接设置有项目的成员列表
            this.membersWithProjects = formattedMembers;
            
            // 同时保留原始的成员列表，以保持兼容性
            this.deptMembers = formattedMembers;
            
            // 记录成员项目映射关系
            this.memberProjectsMap = {};
            formattedMembers.forEach(member => {
              this.memberProjectsMap[member.userId] = { count: member.projectCount };
            });
            
            // 标记初始化完成
            this.initialized = true;
            this.loading = false;
            
            // 如果有成员，选择第一个
            if (this.membersWithProjects.length > 0) {
              this.selectMember(this.membersWithProjects[0]);
            } else {
              this.$message.info("没有找到有项目的部门成员");
            }
            
            resolve();
          } else {
            this.$message.error("获取部门成员数据失败");
            this.loading = false;
            reject(new Error("获取部门成员数据失败"));
          }
        }).catch(error => {
          this.$message.error("获取部门成员项目信息失败：" + error);
          this.loading = false;
          
          // 使用原始方法作为备选
          this.fallbackGetAllDeptUsers()
            .then(resolve)
            .catch(reject);
        });
      });
    },
    
    // 保留原来的方法作为备选
    fallbackGetAllDeptUsers() {
      return new Promise((resolve, reject) => {
        if (this.leaderDepts.length === 0) {
          this.loading = false;
          reject(new Error("没有部门ID列表"));
          return;
        }
        
        // 所有用户的集合，用userId作为键避免重复
        const allUsers = {};
        // 完成的请求计数
        let completedRequests = 0;
        // 总请求数
        const totalRequests = this.leaderDepts.length;
        
        // 获取所有部门的信息，用于检查部门负责人
        listDept({}).then(deptResponse => {
          const allDepts = deptResponse.data || [];
          const deptLeaderMap = {};
          
          // 创建部门ID到部门负责人的映射
          allDepts.forEach(dept => {
            if (dept.leader) {
              deptLeaderMap[dept.deptId] = dept.leader;
            }
          });
          
          // 处理每个部门的用户
          this.leaderDepts.forEach(deptId => {
            listUser({ deptId: deptId }).then(response => {
              const users = response.rows || [];
              
              // 添加用户到集合中，避免重复，同时排除部门负责人
              users.forEach(user => {
                // 排除部门负责人 - 检查当前部门的负责人
                const currentDeptLeader = deptLeaderMap[user.deptId];
                if (!allUsers[user.userId] && user.userName !== currentDeptLeader && user.userName !== this.deptLeader) {
                  allUsers[user.userId] = user;
                }
              });
              
              // 检查是否所有请求都完成了
              completedRequests++;
              if (completedRequests === totalRequests) {
                // 将用户集合转为数组
                const filteredUsers = Object.values(allUsers);
                
                this.deptMembers = filteredUsers;
                
                // 获取每个成员的项目数据，筛选出有项目的成员
                this.fetchMembersProjects(filteredUsers)
                  .then(() => {
                    // 标记初始化完成
                    this.initialized = true;
                    this.loading = false;
                    
                    // 如果有成员，选择第一个
                    if (this.membersWithProjects.length > 0) {
                      this.selectMember(this.membersWithProjects[0]);
                    }
                    
                    resolve();
                  })
                  .catch(error => {
                    this.loading = false;
                    this.$message.error("获取成员项目信息失败：" + (error.message || error));
                    reject(error);
                  });
              }
            }).catch(error => {
              this.$message.error(`获取部门(ID:${deptId})用户失败：${error}`);
              
              // 即使出错也计数，确保流程能继续
              completedRequests++;
              if (completedRequests === totalRequests) {
                if (Object.keys(allUsers).length > 0) {
                  // 有一些用户数据
                  const filteredUsers = Object.values(allUsers);
                  
                  this.deptMembers = filteredUsers;
                  
                  // 获取每个成员的项目数据，筛选出有项目的成员
                  this.fetchMembersProjects(filteredUsers)
                    .then(() => {
                      this.initialized = true;
                      this.loading = false;
                      
                      if (this.membersWithProjects.length > 0) {
                        this.selectMember(this.membersWithProjects[0]);
                      }
                      
                      resolve();
                    })
                    .catch(error => {
                      this.loading = false;
                      reject(error);
                    });
                } else {
                  this.loading = false;
                  reject(error);
                }
              }
            });
          });
        }).catch(error => {
          this.$message.error("获取部门列表失败：" + error);
          this.loading = false;
          reject(error);
        });
      });
    },

    // 修改getDeptUsers方法，使用新的API
    getDeptUsers() {
      return new Promise((resolve, reject) => {
        if (!this.currentDeptId) {
          this.loading = false;
          reject(new Error("没有部门ID"));
          return;
        }
        
        // 使用新的API获取有项目的部门成员
        getDeptMembersWithProjects([this.currentDeptId]).then(response => {
          if (response.code === 200 && response.data) {
            // 数据已经经过后端处理，只返回有项目的用户
            const membersWithProjects = response.data;
            
            // 将数据处理为前端需要的格式
            const formattedMembers = membersWithProjects.map(member => ({
              userId: member.user_id,
              userName: member.user_name,
              nickName: member.nick_name || member.user_name,
              deptId: member.dept_id,
              deptName: member.dept_name,
              projectCount: member.project_count || 0
            }));
            
            // 直接设置有项目的成员列表
            this.membersWithProjects = formattedMembers;
            
            // 同时保留原始的成员列表，以保持兼容性
            this.deptMembers = formattedMembers;
            
            // 记录成员项目映射关系
            this.memberProjectsMap = {};
            formattedMembers.forEach(member => {
              this.memberProjectsMap[member.userId] = { count: member.projectCount };
            });
            
            // 标记初始化完成
            this.initialized = true;
            this.loading = false;
            
            // 如果有成员，选择第一个
            if (this.membersWithProjects.length > 0) {
              this.selectMember(this.membersWithProjects[0]);
            } else {
              this.$message.info("没有找到有项目的部门成员");
            }
            
            resolve();
          } else {
            this.$message.error("获取部门成员数据失败");
            this.loading = false;
            reject(new Error("获取部门成员数据失败"));
          }
        }).catch(error => {
          this.$message.error("获取部门成员项目信息失败：" + error);
          this.loading = false;
          
          // 使用原始的方法作为备选
          this.fallbackGetDeptUsers()
            .then(resolve)
            .catch(reject);
        });
      });
    },

    // 保留原始方法作为备选
    fallbackGetDeptUsers() {
      return new Promise((resolve, reject) => {
        if (!this.currentDeptId) {
          this.loading = false;
          reject(new Error("没有部门ID"));
          return;
        }
        
        // 获取部门信息，确定部门负责人
        getDept(this.currentDeptId).then(deptResponse => {
          const deptInfo = deptResponse.data;
          const deptLeader = deptInfo?.leader || null;
          
          // 获取部门所有用户
          listUser({
            deptId: this.currentDeptId
          }).then(response => {
            const users = response.rows || [];
            // 排除部门负责人
            const filteredUsers = users.filter(user => {
              // 排除当前部门的负责人
              return user.userName !== deptLeader && user.userName !== this.deptLeader;
            });
            
            this.deptMembers = filteredUsers;
            
            // 获取每个成员的项目数据，筛选出有项目的成员
            this.fetchMembersProjects(filteredUsers)
              .then(() => {
                this.loading = false;
                
                if (this.membersWithProjects.length > 0) {
                  this.selectMember(this.membersWithProjects[0]);
                }
                
                // 标记初始化完成
                this.initialized = true;
                resolve();
              })
              .catch(error => {
                this.loading = false;
                reject(error);
              });
          }).catch(error => {
            this.$message.error("获取部门用户失败：" + error);
            this.loading = false;
            reject(error);
          });
        }).catch(error => {
          this.$message.error("获取部门信息失败：" + error);
          this.loading = false;
          reject(error);
        });
      });
    },

    // 选择成员
    selectMember(member) {
      this.currentMemberId = member.userId;
      this.currentMember = member;
      this.loadMemberData(member);
    },

    // 加载成员数据
    loadMemberData(member) {
      // 清空旧数据
      this.userProjects = [];
      this.participationList = [];
      this.submittedEffort = [];
      this.hasSubmittedThisMonth = false;

      // 设置加载状态
      this.loading = true;

      // 调用API获取用户项目数据
      getUserProjects(member.userName).then(response => {
        if (response.data && Array.isArray(response.data)) {
          // 过滤掉角色为"负责人"的项目
          this.userProjects = response.data.filter(project => project.role !== '负责人');
          // 添加显示用的百分比字段
          this.userProjects.forEach(project => {
            // 确保effort字段有初始值
            if (!project.effort) project.effort = 0;
            
            // 设置初始displayEffort为空字符串，而不是数字
            // 这样当用户点击输入框时，会显示空白，而不是默认值
            this.$set(project, 'displayEffort', '');
            // 初始化工时项目选择为空，不设置默认值
            this.$set(project, 'selectedProjectId', null);
          });
        } else if (response.data) {
          // 尝试处理其他可能的数据格式
          try {
            let projectData = [];
            if (typeof response.data === 'string') {
              projectData = JSON.parse(response.data);
            } else if (response.data.rows) {
              projectData = response.data.rows;
            } else if (response.data.data) {
              projectData = response.data.data;
            } else {
              throw new Error('无法识别的数据格式');
            }
            // 过滤掉角色为"负责人"的项目
            this.userProjects = projectData.filter(project => project.role !== '负责人');
            // 添加显示用的百分比字段
            this.userProjects.forEach(project => {
              // 确保effort字段有初始值
              if (!project.effort) project.effort = 0;

              // 设置初始displayEffort为空字符串，而不是数字
              this.$set(project, 'displayEffort', '');
              // 初始化工时项目选择为空，不设置默认值
              this.$set(project, 'selectedProjectId', null);
            });
          } catch (e) {
            console.error('数据解析错误:', e);
            this.userProjects = [];
            this.$message.error('数据解析失败: ' + e.message);
          }
        } else {
          console.error('API返回的数据格式不正确');
          this.userProjects = [];
          this.$message.warning('API返回的数据格式不正确');
        }

        // 先加载已提交的精力分配数据，然后再加载工作量归集数据
        this.loadSubmittedEffort().then(() => {
          // 精力分配数据加载完成后，再加载工作量归集数据
          this.loadSubmittedWorkload();
        });

        // 加载可选择的项目列表
        this.loadAvailableProjects();
      }).catch(error => {
        console.error('获取用户项目失败:', error);
        this.userProjects = [];
        this.loading = false;
      });
    },

    // 加载已提交的精力分配数据
    loadSubmittedEffort() {
      return new Promise((resolve, reject) => {
        if (!this.currentMember) {
          this.loading = false;
          resolve();
          return;
        }

        this.loadingSubmitted = true;

        // 使用选择的月份，如果没有选择则使用当前月份
        const targetMonth = this.selectedMonth || (() => {
          const currentDate = new Date();
          const year = currentDate.getFullYear();
          const month = String(currentDate.getMonth() + 1).padStart(2, '0');
          return `${year}-${month}`;
        })();

        // 调用API获取用户指定月份已提交的精力分配数据
        getUserMonthlyEffort(this.currentMember.userName, targetMonth).then(response => {
          if (response.data && Array.isArray(response.data)) {
            this.submittedEffort = response.data;
            this.hasSubmittedThisMonth = this.submittedEffort.length > 0;

            if (this.hasSubmittedThisMonth) {
              // 如果已提交，将已提交的数据填充到项目列表中
              this.fillSubmittedEffortToProjects();
            }
          }
          resolve();
        }).catch(error => {
          console.error('获取已提交精力分配数据失败:', error);
          reject(error);
        }).finally(() => {
          this.loadingSubmitted = false;
          this.loading = false;
        });
      });
    },

    // 将已提交的精力分配数据填充到项目列表中
    fillSubmittedEffortToProjects() {
      if (!this.submittedEffort || !this.userProjects) return;

      // 创建项目ID到已提交数据的映射
      const effortMap = {};
      this.submittedEffort.forEach(item => {
        effortMap[item.projectId] = item;
      });

      // 填充数据
      this.userProjects.forEach(project => {
        const submittedData = effortMap[project.project_id];
        if (submittedData) {
          // 设置原始值
          const effortValue = parseFloat(submittedData.participationRate);
          this.$set(project, 'effort', effortValue);

          // 设置显示值（百分比）- 对已提交的数据使用数字显示
          const displayValue = Math.round(effortValue * 100);
          this.$set(project, 'displayEffort', displayValue.toString());

          // 重要：这里不设置selectedProjectId，因为精力分配的项目ID和工作量归集的项目ID可能不同
          // selectedProjectId应该由工作量归集的回显来设置
        }
      });
    },

    // 加载模拟数据（保留作为备用）
    loadMockData(userName) {
      // 模拟的项目数据
      const mockProjects = [
        {
          member_id: 406,
          role: "参与",
          project_id: 271,
          user_name: userName,
          dept_name: "氢能制备及耦合技术研究所",
          project_short_name: "二氧化碳原位转化与高值利用",
          project_name: "二氧化碳提纯和高值利用关键技术研究",
          dept_id: 201,
          remarks: "清华联合院"
        },
        {
          member_id: 562,
          role: "参与",
          project_id: 257,
          user_name: userName,
          dept_name: "氢能制备及耦合技术研究所",
          project_short_name: "化学链燃烧",
          project_name: "化学链燃烧发电关键技术",
          dept_id: 201,
          remarks: "国家/省部级项目"
        },
        {
          member_id: 559,
          role: "参与",
          project_id: 258,
          user_name: userName,
          dept_name: "氢能制备及耦合技术研究所",
          project_short_name: "天然气管道掺氢",
          project_name: "在役天然气长输管道掺氢关键技术研究",
          dept_id: 201,
          remarks: "集团重点项目"
        }
      ];

      // 直接赋值，过滤掉角色为"负责人"的项目
      this.userProjects = mockProjects.filter(project => project.role !== '负责人');
      // 添加显示用的百分比字段和原始值字段
      this.userProjects.forEach((project, index) => {
        const baseEffort = 0.33;
        this.$set(project, 'effort', baseEffort);
        this.$set(project, 'displayEffort', '');
      });
    },

    // 刷新按钮操作
    refreshUserProjects() {
      if (!this.currentMember) return;

      this.loading = true;
      this.submittedEffort = [];
      this.hasSubmittedThisMonth = false;

      // 调用API获取用户项目数据
      getUserProjects(this.currentMember.userName).then(response => {
        if (response.data && Array.isArray(response.data)) {
          // 过滤掉角色为"负责人"的项目
          this.userProjects = response.data.filter(project => project.role !== '负责人');
          // 添加显示用的百分比字段
          this.userProjects.forEach(project => {
            // 确保effort字段有初始值
            if (!project.effort) project.effort = 0;
            
            // 设置初始displayEffort为空字符串
            this.$set(project, 'displayEffort', '');
            // 初始化工时项目选择为空，不设置默认值
            this.$set(project, 'selectedProjectId', null);
          });
        } else if (response.data) {
          // 尝试处理其他可能的数据格式
          try {
            let projectData = [];
            if (typeof response.data === 'string') {
              projectData = JSON.parse(response.data);
            } else if (response.data.rows) {
              projectData = response.data.rows;
            } else if (response.data.data) {
              projectData = response.data.data;
            } else {
              throw new Error('无法识别的数据格式');
            }
            // 过滤掉角色为"负责人"的项目
            this.userProjects = projectData.filter(project => project.role !== '负责人');
            // 添加显示用的百分比字段
            this.userProjects.forEach(project => {
              // 确保effort字段有初始值
              if (!project.effort) project.effort = 0;
              
              // 设置初始displayEffort为空字符串
              this.$set(project, 'displayEffort', '');
              // 初始化工时项目选择为空，不设置默认值
              this.$set(project, 'selectedProjectId', null);
            });
          } catch (e) {
            console.error('数据解析错误:', e);
            this.userProjects = [];
            this.$message.error('数据解析失败: ' + e.message);
          }
        } else {
          console.error('API返回的数据格式不正确');
          this.userProjects = [];
          this.$message.warning('API返回的数据格式不正确');
        }

        // 先加载已提交的精力分配数据，然后再加载工作量归集数据
        this.loadSubmittedEffort().then(() => {
          // 精力分配数据加载完成后，再加载工作量归集数据
          this.loadSubmittedWorkload();
        }).catch(error => {
          console.error('加载精力分配数据失败:', error);
          // 即使精力分配数据加载失败，也尝试加载工作量归集数据
          this.loadSubmittedWorkload();
        });
      }).catch(error => {
        console.error('获取用户项目失败:', error);
        this.userProjects = [];
        this.loading = false;
      });
    },

    // 刷新所有数据
    refreshAllData() {
      this.$confirm('确定要刷新数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        this.initialized = false;
        this.deptMembers = [];
        this.membersWithProjects = [];
        this.memberProjectsMap = {};
        this.userProjects = [];
        this.submittedEffort = [];
        this.hasSubmittedThisMonth = false;
        this.submittedWorkload = [];
        this.hasWorkloadThisMonth = false;
        this.currentMember = null;
        this.currentMemberId = null;
        
        // 重新初始化所有数据
        setTimeout(() => {
          this.initData();
        }, 100);
      }).catch(() => {
        // 用户取消操作
      });
    },

    // 筛选成员
    filterMembers() {
      // 关键字筛选通过计算属性实现
    },

    // 处理显示精力分配变化（百分比形式）
    handleDisplayEffortChange(project) {
      // 如果是空值，允许保持空值
      if (project.displayEffort === '' || project.displayEffort === null || project.displayEffort === undefined) {
        return;
      }
      
      // 确保displayEffort是数字类型
      project.displayEffort = Number(project.displayEffort);
      
      if (project.displayEffort < 1) {
        project.displayEffort = 1;
        this.$message.warning('精力分配不能小于1%，已自动调整为1%');
      } else if (project.displayEffort > 100) {
        project.displayEffort = 100;
        this.$message.warning('精力分配不能超过100%，已自动调整为100%');
      }

      // 将百分比转换为小数存储
      project.effort = project.displayEffort / 100;

      // 保存精力分配数据
      this.saveEffortData(project);
      
      // 强制更新计算属性
      this.$forceUpdate();
    },

    // 处理精力分配变化
    handleEffortChange(project) {
      if (project.effort < 0.01) {
        project.effort = 0.01;
        project.displayEffort = 1;
        this.$message.warning('精力分配不能小于0.01，已自动调整为0.01');
      } else if (project.effort > 1) {
        project.effort = 1;
        project.displayEffort = 100;
        this.$message.warning('精力分配不能超过1，已自动调整为1');
      }

      // 保存精力分配数据
      this.saveEffortData(project);
    },

    // 保存精力分配数据
    saveEffortData(project) {
      // 这里可以调用API保存数据到后端
      // 例如：
      /*
      updateProjectEffort({
        projectId: project.project_id,
        userId: this.currentMember.userId,
        effort: project.effort
      }).then(() => {
        this.$message.success('精力分配已保存');
      }).catch(error => {
        console.error('保存精力分配失败:', error);
        this.$message.error('保存精力分配失败');
      });
      */

      // 暂时不执行实际保存操作
    },

    // 保存所有精力分配数据
    saveAllEffortData() {
      // 验证精力分配总和
      if (!this.isEffortValid) {
        this.$message.error(`精力分配总和(${this.totalEffort})必须在0.5到1之间`);
        return;
      }

      this.saving = true;
      const effortData = this.userProjects.map(project => ({
        projectId: project.project_id,
        userId: this.currentMember.userId,
        effort: project.effort
      }));

      // 这里可以调用API批量保存数据到后端
      // 例如：
      /*
      updateProjectEfforts({
        userId: this.currentMember.userId,
        efforts: effortData
      }).then(() => {
        this.$message.success('所有精力分配已保存');
      }).catch(error => {
        console.error('保存精力分配失败:', error);
        this.$message.error('保存精力分配失败');
      }).finally(() => {
        this.saving = false;
      });
      */

      // 模拟API调用
      setTimeout(() => {
        this.$message.success('所有精力分配已保存');
        this.saving = false;
      }, 500);
    },

    // 提交精力分配数据
    submitEffortData() {
      if (!this.currentMember || !this.userProjects || this.userProjects.length === 0) {
        this.$message.warning('没有可提交的数据');
        return;
      }

      // 先检查是否有空值的项目
      const projectsWithEmptyEffort = this.userProjects.filter(project => 
        project.displayEffort === '' || project.displayEffort === null || project.displayEffort === undefined
      );
      
      if (projectsWithEmptyEffort.length > 0) {
        this.$message.error(`有${projectsWithEmptyEffort.length}个项目未设置精力值，请为所有项目分配精力值`);
        return;
      }

      // 确保所有项目的effort值与displayEffort保持一致
      this.userProjects.forEach(project => {
        project.effort = Number(project.displayEffort) / 100;
      });

      // 检查是否有项目没有设置精力值
      const projectsWithoutEffort = this.userProjects.filter(project => !project.effort || project.effort <= 0);
      if (projectsWithoutEffort.length > 0) {
        this.$message.error(`有${projectsWithoutEffort.length}个项目未设置精力值，请为所有项目分配精力值`);
        return;
      }

      // 计算精力总和
      const totalEffort = this.userProjects.reduce((sum, project) => sum + (parseFloat(project.effort) || 0), 0).toFixed(2);
      const totalDisplayEffort = Math.round(totalEffort * 100);

      // 检查精力总和是否为1
      if (Math.abs(totalEffort - 1) > 0.01) {
        this.$message.error(`精力分配总和(${totalDisplayEffort}%)必须为100%，当前相差${Math.abs(totalDisplayEffort - 100)}%`);
        return;
      }

      // 检查工时项目是否都已选择
      const projectsWithoutWorkloadProject = this.userProjects.filter(project =>
        !project.selectedProjectId || project.selectedProjectId === null
      );

      if (projectsWithoutWorkloadProject.length > 0) {
        const projectNames = projectsWithoutWorkloadProject.map(p => p.project_name).join('、');
        this.$message.error(`请为：${projectNames} 匹配工作量归集`);
        return;
      }

      // 显示确认对话框
      const actionText = this.hasSubmittedThisMonth ? '更新' : '提交';
      this.$confirm(`确认为用户 ${this.currentMember.nickName || this.currentMember.userName} ${actionText}精力分配数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitting = true;

        // 使用选择的月份，如果没有选择则使用当前月份
        const targetMonth = this.selectedMonth || (() => {
          const currentDate = new Date();
          const year = currentDate.getFullYear();
          const month = String(currentDate.getMonth() + 1).padStart(2, '0');
          return `${year}-${month}`;
        })();

        // 准备批量提交的数据
        const participationData = this.userProjects.map(project => {
          // 基础数据 - 精力分配始终使用原始项目ID
          const data = {
            userName: this.currentMember.userName,
            projectId: project.project_id, // 精力分配使用原始项目ID
            projectName: project.project_name,
            participationRate: project.effort || 0, // 使用小数形式的值
            month: targetMonth,
            deptId: this.currentMember.deptId,
            assignerId: this.$store.getters.userId,
            assignerName: this.$store.getters.name,
            comments: ``,
            // 添加工作量归集的项目ID，用于同步工作量归集
            workloadProjectId: project.selectedProjectId || project.project_id
          };



          // 如果是更新操作，需要添加ID
          if (this.hasSubmittedThisMonth) {
            // 查找对应的已提交记录，获取ID
            const existingRecord = this.submittedEffort.find(
              item => Number(item.projectId) === Number(project.project_id)
            );
            if (existingRecord && existingRecord.id) {
              data.id = existingRecord.id;
            }
          }

          return data;
        });

        // 如果是更新操作，需要分离出需要更新和需要插入的数据
        if (this.hasSubmittedThisMonth) {
          const updateData = [];
          const insertData = [];
          
          // 分类数据：有ID的更新，无ID的插入
          participationData.forEach(item => {
            if (item.id) {
              updateData.push(item);
            } else {
              insertData.push(item);
            }
          });
          
          const promises = [];
          
          // 如果有需要更新的数据
          if (updateData.length > 0) {
            const updatePromise = batchUpdateParticipation(updateData)
              .then(response => {
                return { type: 'update', count: updateData.length };
              })
              .catch(error => {
                throw error;
              });
            promises.push(updatePromise);
          }
          
          // 如果有需要插入的数据
          if (insertData.length > 0) {
            const insertPromise = batchAddParticipation(insertData)
              .then(response => {
                return { type: 'insert', count: insertData.length };
              })
              .catch(error => {
                throw error;
              });
            promises.push(insertPromise);
          }
          
          // 如果没有任何操作，直接提示
          if (promises.length === 0) {
            this.$message.warning("没有数据需要更新或插入");
            this.submitting = false;
            return;
          }
          
          // 执行所有操作
          Promise.all(promises)
            .then(responses => {
              let updateCount = 0;
              let insertCount = 0;

              responses.forEach(res => {
                if (res.type === 'update') updateCount = res.count;
                if (res.type === 'insert') insertCount = res.count;
              });

              let message = '';
              if (updateCount > 0 && insertCount > 0) {
                message = `精力分配数据更新成功，更新了${updateCount}个项目，新增了${insertCount}个项目`;
              } else if (updateCount > 0) {
                message = `精力分配数据更新成功，更新了${updateCount}个项目`;
              } else if (insertCount > 0) {
                message = `精力分配数据更新成功，新增了${insertCount}个项目`;
              } else {
                message = '精力分配数据更新成功';
              }

              // 同步更新工作量归集表
              this.syncWorkloadRecords(participationData, targetMonth)
                .then(() => {
                  this.$message.success(message + '，工作量归集已同步更新');
                })
                .catch(error => {
                  this.$message.warning(message + '，但工作量归集同步失败: ' + error.message);
                });

              // 更新已提交状态
              this.hasSubmittedThisMonth = true;

              // 刷新数据以获取最新状态
              this.refreshUserProjects();
            })
            .catch(error => {
              this.$message.error('操作失败: ' + (error.message || '未知错误'));
            })
            .finally(() => {
              this.submitting = false;
            });
        } else {
          // 首次提交，直接使用批量添加API
          batchAddParticipation(participationData)
            .then(() => {
              // 同步更新工作量归集表
              this.syncWorkloadRecords(participationData, targetMonth)
                .then(() => {
                  this.$message.success('精力分配数据提交成功，工作量归集已同步更新');
                })
                .catch(error => {
                  this.$message.warning('精力分配数据提交成功，但工作量归集同步失败: ' + error.message);
                });

              // 更新已提交状态
              this.hasSubmittedThisMonth = true;
              this.submittedEffort = participationData;
              // 刷新数据
              this.refreshUserProjects();
            })
            .catch(error => {
              this.$message.error('操作失败: ' + (error.message || '未知错误'));
            })
            .finally(() => {
              this.submitting = false;
            });
        }
      }).catch(() => {
        this.$message.info('已取消操作');
      });
    },

    // 处理输入框按键事件
    handleInputKeyup(event, project) {
      // 获取输入值
      const input = event.target.value;
      
      // 如果不是数字，则替换为数字
      if (!/^\d*$/.test(input)) {
        project.displayEffort = input.replace(/\D/g, '');
      }
      
      // 不再强制将空值设为0，允许用户清空输入框
      
      // 如果超过100，则设为100
      if (Number(project.displayEffort) > 100) {
        project.displayEffort = '100';
      }
      
      // 将百分比转换为小数存储，如果是空值则不转换
      if (project.displayEffort !== '') {
        project.effort = Number(project.displayEffort) / 100;
      }
      
      // 强制更新计算属性
      this.$forceUpdate();
    },

    // 处理输入框失去焦点事件
    handleInputBlur(project) {
      // 确保值为数字
      let value = Number(project.displayEffort);
      
      // 如果用户没有输入任何内容，不做处理，保留原值
      if (project.displayEffort === '' || project.displayEffort === null || project.displayEffort === undefined) {
        return;
      }
      
      // 如果不是有效数字，设为0
      if (isNaN(value)) {
        value = 0;
      }
      
      // 限制最小值为1
      if (value < 1) {
        value = 1;
      }
      
      // 更新为整数
      value = Math.round(value);
      
      // 更新显示值和存储值
      project.displayEffort = value;
      project.effort = value / 100;
      
      // 保存精力分配数据
      this.saveEffortData(project);
      
      // 强制更新计算属性
      this.$forceUpdate();
    },

    // 加载可选择的项目列表（排除承揽项目）
    loadAvailableProjects() {
      // 获取可选择的项目列表（后端已过滤承揽项目）
      getAvailableProjects().then(response => {
        if (response.data && Array.isArray(response.data)) {
          this.availableProjects = response.data.map(project => ({
            id: project.id,
            projectName: project.projectName,
            projectShortName: project.projectShortName
          }));
        }
      }).catch(error => {
        console.error('获取可选项目列表失败:', error);
        this.availableProjects = [];
      });
    },

    // 处理项目选择变化
    handleProjectSelect(project) {
      // 当用户选择不同的项目时，更新项目选择
      // 这里可以添加额外的逻辑，比如验证选择的项目是否合法等
    },

    // 加载已提交的工作量归集数据
    loadSubmittedWorkload() {
      if (!this.currentMember) return;

      // 使用选择的月份，如果没有选择则使用当前月份
      const targetMonth = this.selectedMonth || (() => {
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        return `${year}-${month}`;
      })();

      // 调用API获取用户指定月份工作量归集
      getUserMonthlyWorkload(this.currentMember.userName, targetMonth)
        .then(response => {
          if (response.data && Array.isArray(response.data)) {
            this.submittedWorkload = response.data;
            this.hasWorkloadThisMonth = response.data.length > 0;

            // 将工作量归集回显到项目选择中
            this.fillWorkloadToProjects();
          } else {
            this.submittedWorkload = [];
            this.hasWorkloadThisMonth = false;
          }
        })
        .catch(error => {
          console.error('获取工作量归集失败:', error);
          this.submittedWorkload = [];
          this.hasWorkloadThisMonth = false;
        });
    },

    // 将已提交的工作量归集填充到项目选择中
    fillWorkloadToProjects() {
      if (!this.submittedWorkload || !this.userProjects) return;



      // 为每个精力分配项目查找对应的工作量归集
      this.userProjects.forEach(project => {
        // 通过来源精力分配项目ID查找对应的工时记录
        const workloadRecord = this.submittedWorkload.find(
          workload => workload.sourceEffortProjectId === project.project_id
        );

        if (workloadRecord) {
          // 设置选中的项目ID为工作量归集中实际的项目ID（用户选择的工作量归集项目）
          this.$set(project, 'selectedProjectId', workloadRecord.projectId);
        } else {
          // 向后兼容：通过备注字段查找（兼容旧数据）
          const targetRemarks = `精力分配项目ID:${project.project_id}`;
          const legacyWorkloadRecord = this.submittedWorkload.find(
            workload => workload.remarks === targetRemarks
          );

          if (legacyWorkloadRecord) {
            this.$set(project, 'selectedProjectId', legacyWorkloadRecord.projectId);
          } else {
            // 最后的兼容策略：查找用户在当月的工作量归集
            const userWorkloads = this.submittedWorkload.filter(
              item => item.userName === this.currentMember.userName
            );

            if (userWorkloads.length === 1) {
              // 如果用户只有一条工作量归集，使用这条记录
              this.$set(project, 'selectedProjectId', userWorkloads[0].projectId);
            } else {
              // 不设置默认选择，保持为空
              this.$set(project, 'selectedProjectId', null);
            }
          }
        }
      });
    },

    // 同步工作量归集表
    syncWorkloadRecords(participationData, workMonth) {
      return new Promise((resolve) => {
        // 检查是否有数据需要同步
        if (!participationData || participationData.length === 0) {
          resolve();
          return;
        }

        const workloadPromises = participationData.map(participation => {
          const workloadData = {
            projectId: participation.workloadProjectId || participation.projectId, // 使用工作量归集的项目ID
            userName: participation.userName,
            workMonth: workMonth,
            involvement: participation.participationRate,
            updatedAt: new Date(), // 添加更新时间
            // 使用新字段来标识对应的精力分配项目
            sourceEffortProjectId: participation.projectId
          };



          // 检查是否已有工作量归集
          // 策略：避免唯一约束冲突，优先查找现有记录进行更新

          let existingWorkload = null;
          if (this.submittedWorkload && this.submittedWorkload.length > 0) {
            // 策略1：通过来源精力分配项目ID查找对应的工作量归集（精确匹配）
            existingWorkload = this.submittedWorkload.find(
              item => item.userName === participation.userName &&
                     item.workMonth === workMonth &&
                     item.sourceEffortProjectId === participation.projectId
            );

            // 策略2：向后兼容，通过备注字段查找（兼容旧数据）
            if (!existingWorkload) {
              const targetRemarks = `精力分配项目ID:${participation.projectId}`;
              existingWorkload = this.submittedWorkload.find(
                item => item.userName === participation.userName &&
                       item.workMonth === workMonth &&
                       item.remarks === targetRemarks
              );

              if (existingWorkload) {
                existingWorkload._needUpdateSourceField = true;
              }
            }

            // 策略3：如果都没找到，查找没有来源字段的记录（历史数据兼容）
            if (!existingWorkload) {
              const userWorkloads = this.submittedWorkload.filter(
                item => item.userName === participation.userName &&
                       item.workMonth === workMonth &&
                       !item.sourceEffortProjectId
              );

              if (userWorkloads.length > 0) {
                existingWorkload = userWorkloads[0];
                existingWorkload._needUpdateSourceField = true;
              }
            }

            // 策略4：最后的安全检查 - 查找可能导致唯一约束冲突的记录
            if (!existingWorkload) {
              // 查找是否有其他记录会导致唯一约束冲突
              const conflictingRecord = this.submittedWorkload.find(
                item => item.userName === participation.userName &&
                       item.workMonth === workMonth &&
                       item.sourceEffortProjectId === participation.projectId
              );

              if (conflictingRecord) {
                existingWorkload = conflictingRecord;
              }
            }
          }



          if (existingWorkload && existingWorkload.id) {
            // 如果已有记录且有有效ID，添加ID进行更新
            workloadData.id = existingWorkload.id;



            return updateWorkload(workloadData).catch(error => {
              console.error('工作量归集更新失败:', error);
              return Promise.resolve();
            });
          } else {
            // 如果没有记录或记录无效，创建新记录
            return addWorkload(workloadData).catch(error => {
              console.error('工作量归集创建失败:', error);
              return Promise.resolve();
            });
          }
        });

        Promise.all(workloadPromises)
          .then(() => {
            // 重新加载工作量归集以获取最新状态
            this.loadSubmittedWorkload();
            resolve();
          })
          .catch(error => {
            // 即使有错误，也不完全失败，只是警告
            console.warn('部分工作量归集同步失败:', error);
            resolve(); // 改为resolve，避免阻断主流程
          });
      });
    },

    // 添加获取成员项目信息的方法
    fetchMembersProjects(members) {
      return new Promise((resolve) => {
        if (!members || members.length === 0) {
          this.membersWithProjects = [];
          this.memberProjectsMap = {};
          resolve();
          return;
        }
        
        // 重置成员项目映射
        this.memberProjectsMap = {};
        
        // 使用Promise.all并行获取所有成员的项目信息
        const promises = members.map(member => {
          return new Promise((resolveUser) => {
            // 获取用户项目数据
            getUserProjects(member.userName).then(response => {
              let projects = [];
              
              if (response.data && Array.isArray(response.data)) {
                projects = response.data.filter(project => project.role !== '负责人');
              } else if (response.data) {
                try {
                  let projectData = [];
                  if (typeof response.data === 'string') {
                    projectData = JSON.parse(response.data);
                  } else if (response.data.rows) {
                    projectData = response.data.rows;
                  } else if (response.data.data) {
                    projectData = response.data.data;
                  }
                  projects = projectData.filter(project => project.role !== '负责人');
                } catch (e) {
                  console.error('解析项目数据错误:', e);
                  projects = [];
                }
              }
              
              // 将项目信息保存到映射中
              this.memberProjectsMap[member.userId] = projects;
              resolveUser();
            }).catch(() => {
              // 出错时设置为空数组
              this.memberProjectsMap[member.userId] = [];
              resolveUser();
            });
          });
        });
        
        Promise.all(promises).then(() => {
          // 筛选出有项目的成员
          this.membersWithProjects = members.filter(member => {
            const projects = this.memberProjectsMap[member.userId] || [];
            return projects.length > 0;
          });
          
          resolve();
        });
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.kc-content-layout {
  display: flex;
  height: calc(100vh - 100px);
  background-color: #f0f2f5;
}

.kc-sidebar {
  width: 280px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
}

.kc-sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e6e6e6;
  h3 {
    margin: 0;
    font-size: 16px;
    margin-bottom: 5px;
  }
}

.kc-dept-name {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 260px;
}

.kc-sidebar-search {
  padding: 10px;
  border-bottom: 1px solid #e6e6e6;
}

.kc-sidebar-list {
  flex: 1;
  overflow: hidden;
}

.kc-member-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;

  &:hover {
    background-color: #f5f7fa;
  }

  &.kc-member-active {
    background-color: #ecf5ff;
    border-right: 3px solid #409eff;
  }
}

.kc-member-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
}

.kc-member-post {
  font-size: 12px;
  color: #909399;
}

.kc-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow: hidden;
}

.kc-content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.kc-header-title {
  font-size: 18px;
  font-weight: 500;
}

.kc-header-tools {
  display: flex;
  align-items: center;
  gap: 10px;
}

.kc-month-selector {
  display: flex;
  align-items: center;
}

.kc-participation-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

.kc-empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    font-size: 14px;
  }
}

.kc-empty-text {
  padding: 20px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.kc-section {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
  padding: 16px;
}

.kc-section-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.kc-empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: #909399;

  i {
    font-size: 32px;
    margin-bottom: 12px;
  }

  p {
    font-size: 14px;
  }
}

.kc-effort-summary {
  display: flex;
  align-items: center;
  margin-top: 8px;

  span {
    margin-right: 8px;
  }

  .kc-effort-valid {
    color: #67C23A;
  }

  .kc-effort-invalid {
    color: #F56C6C;
  }
}

.kc-submit-area {
  margin-top: 16px;
  text-align: center;
}

.kc-submit-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.kc-submit-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.kc-submitted-info {
  display: flex;
  align-items: center;

  .el-icon-info {
    margin-left: 5px;
    color: #909399;
    cursor: pointer;
  }
}

.effort-allocation-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.percent-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
}

.percent-input {
  width: 100%;
}

.arrow-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  padding: 4px 4px 0 4px;
}

.arrow-label {
  font-size: 10px;
  color: #909399;
  margin-bottom: 4px;
  margin-top: 4px;
  white-space: nowrap;
  line-height: 1;
}

.arrow-icon {
  font-size: 16px;
  color: #409EFF;
  font-weight: bold;
  margin-top: -6px;
  transform: translateY(-2px);
}

.project-select-wrapper {
  flex: 1;
}

.project-select {
  width: 100%;
}

.percent-input::v-deep .el-input-group__append {
  padding: 0 8px;
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
}
</style>
