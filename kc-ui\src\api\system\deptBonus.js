import request from '@/utils/request'

// 查询部门奖金分配列表
export function listDeptBonus(query) {
  return request({
    url: '/system/deptBonus/list',
    method: 'get',
    params: query
  })
}

// 根据月份查询部门奖金分配列表
export function listDeptBonusByMonth(month) {
  return request({
    url: '/system/deptBonus/listByMonth/' + month,
    method: 'get'
  })
}

// 检查指定月份是否已经分配过奖金
export function checkMonth(month) {
  return request({
    url: '/system/deptBonus/checkMonth/' + month,
    method: 'get'
  })
}

// 查询部门奖金分配详细
export function getDeptBonus(id) {
  return request({
    url: '/system/deptBonus/' + id,
    method: 'get'
  })
}

// 根据部门ID和月份获取部门奖金分配信息
export function getDeptBonusByDeptAndMonth(deptId, month) {
  return request({
    url: '/system/deptBonus/getDeptBonus/' + deptId + '/' + month,
    method: 'get'
  })
}

// 新增部门奖金分配
export function addDeptBonus(data) {
  return request({
    url: '/system/deptBonus',
    method: 'post',
    data: data
  })
}

// 批量分配部门奖金
export function batchAllocateDeptBonus(data) {
  return request({
    url: '/system/deptBonus/batchAllocate',
    method: 'post',
    data: data
  })
}

// 修改部门奖金分配
export function updateDeptBonus(data) {
  return request({
    url: '/system/deptBonus',
    method: 'put',
    data: data
  })
}

// 删除部门奖金分配
export function delDeptBonus(id) {
  return request({
    url: '/system/deptBonus/' + id,
    method: 'delete'
  })
}

// 根据月份删除部门奖金分配
export function deleteDeptBonusByMonth(month) {
  return request({
    url: '/system/deptBonus/deleteByMonth/' + month,
    method: 'delete'
  })
}
