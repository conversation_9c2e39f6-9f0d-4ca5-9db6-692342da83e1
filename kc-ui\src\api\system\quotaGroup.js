import request from '@/utils/request'

// 查询配额组列表
export function listQuotaGroup(query) {
  return request({
    url: '/system/quotaGroup/list',
    method: 'get',
    params: query
  })
}

// 查询配额组详细
export function getQuotaGroup(id) {
  return request({
    url: '/system/quotaGroup/' + id,
    method: 'get'
  })
}

// 新增配额组
export function addQuotaGroup(data) {
  return request({
    url: '/system/quotaGroup',
    method: 'post',
    data: data
  })
}

// 修改配额组
export function updateQuotaGroup(data) {
  return request({
    url: '/system/quotaGroup',
    method: 'put',
    data: data
  })
}

// 删除配额组
export function delQuotaGroup(id) {
  return request({
    url: '/system/quotaGroup/' + id,
    method: 'delete'
  })
}

// 查询配额组部门关系列表
export function listQuotaGroupDept(query) {
  return request({
    url: '/system/quotaGroup/dept/list',
    method: 'get',
    params: query
  })
}

// 查询配额组配额列表
export function listQuotaGroupQuota(query) {
  return request({
    url: '/system/quotaGroup/quota/list',
    method: 'get',
    params: query
  })
}

// 新增配额组配额
export function addQuotaGroupQuota(data) {
  return request({
    url: '/system/quotaGroup/quota',
    method: 'post',
    data: data
  })
}

// 修改配额组配额
export function updateQuotaGroupQuota(data) {
  return request({
    url: '/system/quotaGroup/quota',
    method: 'put',
    data: data
  })
}

// 根据部门ID查询配额组信息
export function getQuotaGroupByDeptId(deptId) {
  return request({
    url: '/system/quotaGroup/dept/' + deptId,
    method: 'get'
  })
}

// 检查部门是否属于配额组
export function checkDeptInQuotaGroup(deptId) {
  return request({
    url: '/system/quotaGroup/check/' + deptId,
    method: 'get'
  })
}

// 获取部门的配额组配额信息
export function getQuotaGroupQuotaByDept(deptId, year) {
  return request({
    url: `/system/quotaGroup/quota/${deptId}/${year}`,
    method: 'get'
  })
}

// 初始化配额组年度配额
export function initQuotaGroupQuota(groupId, year) {
  return request({
    url: `/system/quotaGroup/quota/init/${groupId}/${year}`,
    method: 'post'
  })
}
