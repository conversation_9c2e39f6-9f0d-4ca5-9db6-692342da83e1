<template>
  <div>
    <!-- 项目工作量统计表格 -->
    <el-table 
      v-loading="loading" 
      :data="projectStatsList"
      style="width: 100%"
    >
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column 
        label="工作月份" 
        align="center" 
        prop="workMonth"
        width="120"
      />
      <el-table-column 
        label="工作量分配" 
        align="center"
        min-width="200"
      >
        <template slot-scope="scope">
          <div>{{ formatWorkload(scope.row.workloadValue) }}</div>
        </template>
      </el-table-column>
      <el-table-column 
        label="涉及人数" 
        align="center" 
        prop="memberCount" 
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.memberCount || '0' }}
        </template>
      </el-table-column>
      <!-- <el-table-column 
        label="整体劳务费(万元)" 
        align="center" 
        prop="totalSalary" 
        width="150"
      >
        <template slot-scope="scope">
          <el-tooltip 
            v-if="scope.row.missingData"
            effect="dark" 
            :content="scope.row.missingData"
            placement="top"
          >
            <span>-</span>
          </el-tooltip>
          <span v-else>
            {{ scope.row.totalSalary ? formatSalary(scope.row.totalSalary) : '-' }}
          </span>
        </template>
      </el-table-column> -->
    </el-table>
  </div>
</template>

<script>
import { getProjectStats } from "@/api/system/workload";

export default {
  name: "ProjectStatistics",
  props: {
    projectId: {
      type: [Number, String],
      required: true
    },
    workMonth: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      projectStatsList: []
    };
  },
  watch: {
    projectId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getProjectStats();
        }
      }
    },
    workMonth: {
      handler() {
        if (this.projectId) {
          this.getProjectStats();
        }
      }
    }
  },
  methods: {
    // 获取项目统计数据
    getProjectStats() {
      this.loading = true;
      getProjectStats(this.projectId, this.workMonth).then(response => {
        this.projectStatsList = response.data;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 格式化工作量显示
    formatWorkload(value) {
      if (!value) return '0.00';
      return Number(value).toFixed(2);
    },
    // 格式化薪资显示
    formatSalary(value) {
      return Number(value).toFixed(4);
    }
  }
};
</script>

<style scoped>
.search-form {
  margin-bottom: 18px;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
}
</style>