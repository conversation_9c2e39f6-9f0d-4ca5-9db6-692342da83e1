package com.kc.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 配额组部门关系对象 quota_group_dept
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public class QuotaGroupDept extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 配额组ID */
    @Excel(name = "配额组ID")
    private Long groupId;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 是否主部门（0否 1是） */
    @Excel(name = "是否主部门", readConverterExp = "0=否,1=是")
    private String isPrimary;

    /** 部门名称（关联查询字段） */
    private String deptName;

    /** 配额组名称（关联查询字段） */
    private String groupName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setGroupId(Long groupId) 
    {
        this.groupId = groupId;
    }

    public Long getGroupId() 
    {
        return groupId;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setIsPrimary(String isPrimary) 
    {
        this.isPrimary = isPrimary;
    }

    public String getIsPrimary() 
    {
        return isPrimary;
    }

    public String getDeptName() 
    {
        return deptName;
    }

    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getGroupName() 
    {
        return groupName;
    }

    public void setGroupName(String groupName) 
    {
        this.groupName = groupName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("groupId", getGroupId())
            .append("deptId", getDeptId())
            .append("isPrimary", getIsPrimary())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
