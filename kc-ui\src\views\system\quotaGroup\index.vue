<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="配额组名称" prop="groupName">
        <el-input
          v-model="queryParams.groupName"
          placeholder="请输入配额组名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="配额组编码" prop="groupCode">
        <el-input
          v-model="queryParams.groupCode"
          placeholder="请输入配额组编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="0" />
          <el-option label="停用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:quotaGroup:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:quotaGroup:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:quotaGroup:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:quotaGroup:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="quotaGroupList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="配额组ID" align="center" prop="id" />
      <el-table-column label="配额组名称" align="center" prop="groupName" />
      <el-table-column label="配额组编码" align="center" prop="groupCode" />
      <el-table-column label="描述" align="center" prop="description" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:quotaGroup:edit']"
          >修改</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:quotaGroup:query']"
          >详情</el-button> -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-setting"
            @click="handleQuotaSetting(scope.row)"
            v-hasPermi="['system:quotaGroup:edit']"
          >配额设置</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:quotaGroup:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改配额组对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="配额组名称" prop="groupName">
          <el-input v-model="form.groupName" placeholder="请输入配额组名称" />
        </el-form-item>
        <el-form-item label="配额组编码" prop="groupCode">
          <el-input v-model="form.groupCode" placeholder="请输入配额组编码" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 配额组详情对话框 -->
    <el-dialog title="配额组详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="配额组名称">{{ detailForm.groupName }}</el-descriptions-item>
        <el-descriptions-item label="配额组编码">{{ detailForm.groupCode }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="dict.type.sys_normal_disable" :value="detailForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ detailForm.description }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider content-position="left">关联部门</el-divider>
      <el-table :data="detailForm.deptList" style="width: 100%">
        <el-table-column prop="deptName" label="部门名称" />
        <el-table-column prop="isPrimary" label="是否主部门">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isPrimary === '1' ? 'success' : 'info'">
              {{ scope.row.isPrimary === '1' ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <el-divider content-position="left">配额信息</el-divider>
      <el-table :data="detailForm.quotaList" style="width: 100%">
        <el-table-column prop="evaluationYear" label="评价年度" />
        <el-table-column prop="totalEmployees" label="总人数" />
        <el-table-column prop="highScoreQuota" label="高分配额" />
        <el-table-column prop="usedQuota" label="已使用" />
        <el-table-column prop="remainingQuota" label="剩余配额" />
      </el-table>
    </el-dialog>

    <!-- 配额设置对话框 -->
    <el-dialog title="配额设置" :visible.sync="quotaSettingOpen" width="800px" append-to-body>
      <el-form :model="quotaSettingForm" ref="quotaSettingForm" label-width="100px">
        <el-form-item label="配额组名称">
          <el-input v-model="quotaSettingForm.groupName" disabled />
        </el-form-item>
        <el-form-item label="评价年度">
          <el-input v-model="quotaSettingForm.evaluationYear" disabled />
        </el-form-item>
      </el-form>

      <el-divider content-position="left">配额信息</el-divider>
      <el-table :data="quotaSettingForm.quotaList" style="width: 100%">
        <el-table-column prop="evaluationYear" label="评价年度" width="120" />
        <!-- <el-table-column prop="totalEmployees" label="总人数" width="100" /> -->
        <el-table-column label="高分配额" width="150">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.highScoreQuota"
              :min="0"
              size="mini"
              style="width: 120px"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column prop="usedQuota" label="已使用" width="100" /> -->
        <!-- <el-table-column prop="remainingQuota" label="剩余配额" width="100" /> -->
        <!-- <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.remainingQuota >= 0 ? 'success' : 'danger'">
              {{ scope.row.remainingQuota >= 0 ? '正常' : '超额' }}
            </el-tag>
          </template>
        </el-table-column> -->
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitQuotaSetting">保存配额</el-button>
        <el-button @click="quotaSettingOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listQuotaGroup, getQuotaGroup, delQuotaGroup, addQuotaGroup, updateQuotaGroup, listQuotaGroupQuota, updateQuotaGroupQuota, initQuotaGroupQuota } from "@/api/system/quotaGroup";

export default {
  name: "QuotaGroup",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 配额组表格数据
      quotaGroupList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 配额设置弹出层
      quotaSettingOpen: false,
      // 配额设置表单
      quotaSettingForm: {
        groupId: null,
        groupName: '',
        evaluationYear: new Date().getFullYear().toString(),
        quotaList: []
      },

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        groupName: null,
        groupCode: null,
        status: null
      },
      // 表单参数
      form: {},
      // 详情表单参数
      detailForm: {},
      // 表单校验
      rules: {
        groupName: [
          { required: true, message: "配额组名称不能为空", trigger: "blur" }
        ],
        groupCode: [
          { required: true, message: "配额组编码不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询配额组列表 */
    getList() {
      this.loading = true;
      listQuotaGroup(this.queryParams).then(response => {
        this.quotaGroupList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        groupName: null,
        groupCode: null,
        description: null,
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加配额组";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getQuotaGroup(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改配额组";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const id = row.id;
      getQuotaGroup(id).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateQuotaGroup(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQuotaGroup(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除配额组编号为"' + ids + '"的数据项？').then(function() {
        return delQuotaGroup(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/quotaGroup/export', {
        ...this.queryParams
      }, `quotaGroup_${new Date().getTime()}.xlsx`)
    },
    /** 配额设置按钮操作 */
    handleQuotaSetting(row) {
      const currentYear = new Date().getFullYear().toString();
      this.quotaSettingForm = {
        groupId: row.id,
        groupName: row.groupName,
        evaluationYear: currentYear,
        quotaList: []
      };
      this.loadQuotaData();
      this.quotaSettingOpen = true;
    },
    /** 加载配额数据 */
    loadQuotaData() {
      if (!this.quotaSettingForm.groupId || !this.quotaSettingForm.evaluationYear) {
        return;
      }

      const queryParams = {
        groupId: this.quotaSettingForm.groupId,
        evaluationYear: this.quotaSettingForm.evaluationYear
      };

      listQuotaGroupQuota(queryParams).then(response => {
        if (response.rows && response.rows.length > 0) {
          this.quotaSettingForm.quotaList = response.rows;
        } else {
          // 如果没有数据，初始化一条记录
          this.initQuotaData();
        }
      });
    },
    /** 初始化配额数据 */
    initQuotaData() {
      initQuotaGroupQuota(this.quotaSettingForm.groupId, this.quotaSettingForm.evaluationYear).then(response => {
        this.$modal.msgSuccess("配额数据初始化成功");
        this.loadQuotaData();
      });
    },
    /** 提交配额设置 */
    submitQuotaSetting() {
      if (!this.quotaSettingForm.quotaList || this.quotaSettingForm.quotaList.length === 0) {
        this.$modal.msgError("请先加载配额数据");
        return;
      }

      // 更新配额数据
      const promises = this.quotaSettingForm.quotaList.map(quota => {
        return updateQuotaGroupQuota(quota);
      });

      Promise.all(promises).then(() => {
        this.$modal.msgSuccess("配额设置保存成功");
        this.quotaSettingOpen = false;
        this.getList();
      }).catch(() => {
        this.$modal.msgError("配额设置保存失败");
      });
    }
  }
};
</script>
