package com.kc.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 项目工时记录对象 project_workload
 * 
 * <AUTHOR>
 * @date 2025-02-15
 */
public class ProjectWorkload extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** 工作月份(格式：YYYY-MM) */
    @Excel(name = "工作月份(格式：YYYY-MM)")
    private String workMonth;

    /** 项目参与度(0-1之间) */
    @Excel(name = "项目参与度(0-1之间)")
    private BigDecimal involvement;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 项目角色 */
    @Excel(name = "项目角色")
    private String role;

    /** 来源项目考核系数项目ID */
    @Excel(name = "来源项目考核系数项目ID")
    private Long sourceEffortProjectId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    public void setWorkMonth(String workMonth) 
    {
        this.workMonth = workMonth;
    }

    public String getWorkMonth() 
    {
        return workMonth;
    }

    public void setInvolvement(BigDecimal involvement) 
    {
        this.involvement = involvement;
    }

    public BigDecimal getInvolvement() 
    {
        return involvement;
    }

    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }

    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }

    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }

    public void setRole(String role)
    {
        this.role = role;
    }

    public String getRole()
    {
        return role;
    }

    public void setSourceEffortProjectId(Long sourceEffortProjectId)
    {
        this.sourceEffortProjectId = sourceEffortProjectId;
    }

    public Long getSourceEffortProjectId()
    {
        return sourceEffortProjectId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("userName", getUserName())
            .append("workMonth", getWorkMonth())
            .append("involvement", getInvolvement())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .append("projectName", getProjectName())
            .append("nickName", getNickName())
            .append("role", getRole())
            .append("sourceEffortProjectId", getSourceEffortProjectId())
            .toString();
    }
}
