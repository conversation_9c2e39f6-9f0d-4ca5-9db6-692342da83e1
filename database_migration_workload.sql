-- 工时记录表结构优化
-- 解决精力分配与工时记录的关联问题

-- 1. 添加新字段：来源精力分配项目ID
ALTER TABLE project_workload 
ADD COLUMN source_effort_project_id BIGINT COMMENT '来源精力分配项目ID';

-- 2. 移除原有的唯一约束（如果存在）
-- 注意：请根据实际的约束名称调整
ALTER TABLE project_workload DROP INDEX IF EXISTS uk_project_user_month;

-- 3. 添加新的唯一约束：每个精力分配项目对应一条工时记录
ALTER TABLE project_workload 
ADD UNIQUE INDEX uk_user_month_source_effort (user_name, work_month, source_effort_project_id);

-- 4. 为现有数据填充 source_effort_project_id 字段
-- 这个脚本假设现有的工时记录与精力分配项目是一对一的关系
-- 如果有复杂的历史数据，可能需要手动处理

-- 示例：如果现有数据中工时项目ID等于精力分配项目ID
-- UPDATE project_workload 
-- SET source_effort_project_id = project_id 
-- WHERE source_effort_project_id IS NULL;

-- 5. 添加索引以提高查询性能
CREATE INDEX idx_source_effort_project ON project_workload(source_effort_project_id);
CREATE INDEX idx_user_month ON project_workload(user_name, work_month);

-- 6. 验证数据完整性
-- SELECT COUNT(*) as total_records FROM project_workload;
-- SELECT COUNT(*) as records_with_source FROM project_workload WHERE source_effort_project_id IS NOT NULL;

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 在生产环境执行前，请先在测试环境验证
-- 3. 如果有大量历史数据，可能需要分批处理
-- 4. 执行后需要重启应用以确保新字段生效
