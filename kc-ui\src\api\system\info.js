import request from '@/utils/request'

// 查询项目基础信息列表
export function listInfo(query) {
  return request({
    url: '/system/info/list',
    method: 'get',
    params: query
  })
}

// 查询项目基础信息详细
export function getInfo(id) {
  return request({
    url: '/system/info/' + id,
    method: 'get'
  })
}

// 新增项目基础信息
export function addInfo(data) {
  return request({
    url: '/system/info',
    method: 'post',
    data: data
  })
}

// 修改项目基础信息
export function updateInfo(data) {
  return request({
    url: '/system/info',
    method: 'put',
    data: data
  })
}

// 删除项目基础信息
export function delInfo(id) {
  return request({
    url: '/system/info/' + id,
    method: 'delete'
  })
}

// 查询所有项目列表（用于统计页面）
export function listAllProjects() {
  return request({
    url: '/system/info/list',
    method: 'get'
  })
}

// 查询用户负责的项目列表（用于工时页面）
export function listUserLeadProjects() {
  return request({
    url: '/system/info/userLeadProjects',
    method: 'get'
  })
}

// 查询用户所有项目工时
export function getUserProjectWorkloads(userName, month) {
  return request({
    url: `/system/info/userProjectWorkloads/${userName}/${month}`,
    method: 'get'
  })
}

// 查询部门下的项目列表
export function listDeptProjects() {
  return request({
    url: '/system/info/dept/projects',
    method: 'get'
  })
}

// 批量填报所有项目成员工时
export function batchAddWorkload() {
  return request({
    url: '/system/info/batchAddWorkload',
    method: 'post'
  })
}

// 查询承揽项目信息列表
export function listContractedInfo(query) {
  return request({
    url: '/system/info/contracted',
    method: 'get',
    params: query
  })
}

// 检查项目是否有精力分配和评分记录
export function checkProjectDependencies(projectId) {
  return request({
    url: `/system/info/checkDependencies/${projectId}`,
    method: 'get'
  })
}

// 获取用户可选择的项目列表（排除承揽项目）
export function getAvailableProjects() {
  return request({
    url: '/system/info/availableProjects',
    method: 'get'
  })
}
