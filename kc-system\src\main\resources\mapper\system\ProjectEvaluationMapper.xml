<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.ProjectEvaluationMapper">
    
    <resultMap type="ProjectEvaluation" id="ProjectEvaluationResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="evaluatorId"    column="evaluator_id"    />
        <result property="evaluateeId"    column="evaluatee_id"    />
        <result property="score"    column="score"    />
        <result property="evaluationMonth"    column="evaluation_month"    />
        <result property="evaluationType"    column="evaluation_type"    />
        <result property="comments"    column="comments"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="projectName"    column="project_name"    />
    </resultMap>

    <sql id="selectProjectEvaluationVo">
        select pe.id, pe.project_id, pe.evaluator_id, pe.evaluatee_id, pe.score, pe.evaluation_month, 
               pe.evaluation_type, pe.comments, pe.created_at, pe.updated_at, pi.project_name
        from project_evaluation pe
        left join project_info pi on pe.project_id = pi.id
    </sql>

    <select id="selectProjectEvaluationList" parameterType="ProjectEvaluation" resultMap="ProjectEvaluationResult">
        <include refid="selectProjectEvaluationVo"/>
        <where>
            <if test="projectId != null "> and pe.project_id = #{projectId}</if>
            <if test="projectName != null and projectName != ''"> and pi.project_name like concat('%', #{projectName}, '%')</if>
            <if test="evaluatorId != null "> and pe.evaluator_id = #{evaluatorId}</if>
            <if test="evaluatorName != null and evaluatorName != ''">
                and pe.evaluator_id in (
                    select user_id from sys_user where (nick_name like concat('%', #{evaluatorName}, '%') or user_name like concat('%', #{evaluatorName}, '%')) and del_flag = '0'
                )
            </if>
            <if test="evaluateeId != null "> and pe.evaluatee_id = #{evaluateeId}</if>
            <if test="evaluateeName != null and evaluateeName != ''">
                and pe.evaluatee_id in (
                    select user_id from sys_user where (nick_name like concat('%', #{evaluateeName}, '%') or user_name like concat('%', #{evaluateeName}, '%')) and del_flag = '0'
                )
            </if>
            <if test="score != null "> and pe.score = #{score}</if>
            <if test="evaluationMonth != null and evaluationMonth != ''"> and pe.evaluation_month = #{evaluationMonth}</if>
            <if test="evaluationType != null and evaluationType != ''"> and pe.evaluation_type = #{evaluationType}</if>
            <if test="comments != null and comments != ''"> and pe.comments = #{comments}</if>
            <if test="createdAt != null "> and pe.created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and pe.updated_at = #{updatedAt}</if>
            <if test="deptId != null">
                and pe.evaluatee_id in (
                    select user_id from sys_user where dept_id = #{deptId} and del_flag = '0'
                )
            </if>
        </where>
        order by pe.id desc
    </select>
    
    <select id="selectProjectEvaluationById" parameterType="Long" resultMap="ProjectEvaluationResult">
        <include refid="selectProjectEvaluationVo"/>
        where pe.id = #{id}
    </select>

    <insert id="insertProjectEvaluation" parameterType="ProjectEvaluation" useGeneratedKeys="true" keyProperty="id">
        insert into project_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="evaluatorId != null">evaluator_id,</if>
            <if test="evaluateeId != null">evaluatee_id,</if>
            <if test="score != null">score,</if>
            <if test="evaluationMonth != null and evaluationMonth != ''">evaluation_month,</if>
            <if test="evaluationType != null and evaluationType != ''">evaluation_type,</if>
            <if test="comments != null">comments,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="evaluatorId != null">#{evaluatorId},</if>
            <if test="evaluateeId != null">#{evaluateeId},</if>
            <if test="score != null">#{score},</if>
            <if test="evaluationMonth != null and evaluationMonth != ''">#{evaluationMonth},</if>
            <if test="evaluationType != null and evaluationType != ''">#{evaluationType},</if>
            <if test="comments != null">#{comments},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateProjectEvaluation" parameterType="ProjectEvaluation">
        update project_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="evaluatorId != null">evaluator_id = #{evaluatorId},</if>
            <if test="evaluateeId != null">evaluatee_id = #{evaluateeId},</if>
            <if test="score != null">score = #{score},</if>
            <if test="evaluationMonth != null and evaluationMonth != ''">evaluation_month = #{evaluationMonth},</if>
            <if test="evaluationType != null and evaluationType != ''">evaluation_type = #{evaluationType},</if>
            <if test="comments != null">comments = #{comments},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectEvaluationById" parameterType="Long">
        delete from project_evaluation where id = #{id}
    </delete>

    <delete id="deleteProjectEvaluationByIds" parameterType="String">
        delete from project_evaluation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询用户参与的项目评分列表 -->
    <select id="selectUserProjectEvaluations" resultMap="UserProjectEvaluationResult">
        SELECT DISTINCT pe.id, pe.project_id, pe.score, pe.evaluation_month, 
               pi.project_name
        FROM project_evaluation pe
        INNER JOIN project_info pi ON pe.project_id = pi.id
        INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
        WHERE su.user_name = #{userName}
        <if test="evaluationMonth != null and evaluationMonth != ''">
            AND pe.evaluation_month = #{evaluationMonth}
        </if>
        ORDER BY pe.evaluation_month DESC, pe.project_id
    </select>
    
    <!-- 通过用户ID查询项目评分列表 -->
    <select id="selectUserProjectEvaluationsByUserId" resultMap="UserProjectEvaluationResult">
        SELECT DISTINCT pe.id, pe.project_id, pe.score, pe.evaluation_month, 
               pi.project_name
        FROM project_evaluation pe
        INNER JOIN project_info pi ON pe.project_id = pi.id
        WHERE pe.evaluatee_id = #{userId}
        <if test="evaluationMonth != null and evaluationMonth != ''">
            AND pe.evaluation_month = #{evaluationMonth}
        </if>
        ORDER BY pe.evaluation_month DESC, pe.project_id
    </select>
    
    <!-- 用户项目评分结果映射 -->
    <resultMap type="ProjectEvaluation" id="UserProjectEvaluationResult">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="score" column="score"/>
        <result property="evaluationMonth" column="evaluation_month"/>
        <result property="projectName" column="project_name"/>
    </resultMap>
    
    <delete id="deleteProjectEvaluationByProjectId" parameterType="Long">
        delete from project_evaluation where project_id = #{projectId}
    </delete>
    
    <select id="countProjectEvaluationByProjectId" parameterType="Long" resultType="int">
        select count(1) from project_evaluation where project_id = #{projectId}
    </select>

    <!-- 检查用户是否有项目精力分配 -->
    <select id="checkUserHasProjectEffort" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM project_participation pp
        INNER JOIN sys_user su ON pp.user_name = su.user_name
        WHERE su.user_id = #{userId}
        AND pp.month = #{evaluationMonth}
        AND pp.participation_rate > 0
    </select>

    <!-- 检查项目负责人是否已对该用户进行评分 -->
    <select id="checkProjectLeaderEvaluationExists" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM project_evaluation pe
        INNER JOIN project_participation pp ON pe.project_id = pp.project_id
        INNER JOIN sys_user su ON pp.user_name = su.user_name
        WHERE su.user_id = #{userId}
        AND pe.evaluation_month = #{evaluationMonth}
        AND pe.evaluation_type = 'project_leader'
        AND pe.evaluatee_id = #{userId}
        AND pp.month = #{evaluationMonth}
        AND pp.participation_rate > 0
    </select>

    <!-- 获取用户的项目精力分配详情，包含项目负责人信息 -->
    <select id="getUserProjectEffortDetails" resultType="java.util.Map">
        SELECT DISTINCT
            pp.project_id,
            pi.project_name,
            pp.participation_rate,
            pp.month as effort_month,
            (SELECT su_leader.nick_name
             FROM project_members pm_leader
             INNER JOIN sys_user su_leader ON pm_leader.user_name = su_leader.user_name
             WHERE pm_leader.project_id = pp.project_id
             AND pm_leader.role = '负责人'
             LIMIT 1) as leader_name,
            (SELECT su_leader.user_name
             FROM project_members pm_leader
             INNER JOIN sys_user su_leader ON pm_leader.user_name = su_leader.user_name
             WHERE pm_leader.project_id = pp.project_id
             AND pm_leader.role = '负责人'
             LIMIT 1) as leader_user_name
        FROM project_participation pp
        INNER JOIN sys_user su ON pp.user_name = su.user_name
        INNER JOIN project_info pi ON pp.project_id = pi.id
        WHERE su.user_id = #{userId}
        AND pp.month = #{evaluationMonth}
        AND pp.participation_rate > 0
        ORDER BY pp.project_id
    </select>

    <!-- 检查特定项目的负责人是否已对该用户进行评分 -->
    <select id="checkProjectLeaderEvaluationForProject" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM project_evaluation pe
        WHERE pe.evaluatee_id = #{userId}
        AND pe.project_id = #{projectId}
        AND pe.evaluation_month = #{evaluationMonth}
        AND pe.evaluation_type = 'project_leader'
    </select>

    <!-- 获取项目负责人信息 -->
    <select id="getProjectLeaderInfo" resultType="java.util.Map">
        SELECT
            su.nick_name as leader_name,
            su.user_name as leader_user_name,
            su.user_id as leader_user_id
        FROM project_members pm
        INNER JOIN sys_user su ON pm.user_name = su.user_name
        WHERE pm.project_id = #{projectId}
        AND pm.role = '负责人'
        LIMIT 1
    </select>

    <!-- 获取用户负责的项目列表 -->
    <select id="getUserLeaderProjects" resultType="java.util.Map">
        SELECT
            pm.project_id,
            pi.project_name
        FROM project_members pm
        INNER JOIN sys_user su ON pm.user_name = su.user_name
        INNER JOIN project_info pi ON pm.project_id = pi.id
        WHERE su.user_id = #{userId}
        AND pm.role = '负责人'
        ORDER BY pm.project_id
    </select>

    <!-- 根据被评价人、项目、月份和评价类型查询评分记录 -->
    <select id="selectByEvaluateeProjectAndMonth" resultMap="ProjectEvaluationResult">
        <include refid="selectProjectEvaluationVo"/>
        WHERE pe.evaluatee_id = #{evaluateeId}
        AND pe.project_id = #{projectId}
        AND pe.evaluation_month = #{evaluationMonth}
        AND pe.evaluation_type = #{evaluationType}
        LIMIT 1
    </select>
</mapper>