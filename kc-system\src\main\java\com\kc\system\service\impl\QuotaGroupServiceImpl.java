package com.kc.system.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.kc.common.utils.SecurityUtils;
import com.kc.system.domain.QuotaGroup;
import com.kc.system.domain.QuotaGroupDept;
import com.kc.system.domain.QuotaGroupQuota;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.common.core.domain.entity.SysDept;
import com.kc.system.mapper.QuotaGroupMapper;
import com.kc.system.mapper.HighScoreRecordMapper;
import com.kc.system.service.IQuotaGroupService;
import com.kc.system.service.ISysUserService;
import com.kc.system.service.ISysDeptService;

/**
 * 配额组Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
@Service
public class QuotaGroupServiceImpl implements IQuotaGroupService 
{
    private static final Logger log = LoggerFactory.getLogger(QuotaGroupServiceImpl.class);
    
    /** 配额比例：30% */
    private static final BigDecimal QUOTA_PERCENTAGE = new BigDecimal("0.30");

    @Autowired
    private QuotaGroupMapper quotaGroupMapper;

    @Autowired
    private HighScoreRecordMapper highScoreRecordMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询配额组
     * 
     * @param id 配额组主键
     * @return 配额组
     */
    @Override
    public QuotaGroup selectQuotaGroupById(Long id)
    {
        return quotaGroupMapper.selectQuotaGroupById(id);
    }

    /**
     * 查询配额组列表
     * 
     * @param quotaGroup 配额组
     * @return 配额组
     */
    @Override
    public List<QuotaGroup> selectQuotaGroupList(QuotaGroup quotaGroup)
    {
        return quotaGroupMapper.selectQuotaGroupList(quotaGroup);
    }

    /**
     * 新增配额组
     *
     * @param quotaGroup 配额组
     * @return 结果
     */
    @Override
    @Transactional
    public int insertQuotaGroup(QuotaGroup quotaGroup)
    {
        // 验证配额组编码唯一性
        QuotaGroup existingGroup = new QuotaGroup();
        existingGroup.setGroupCode(quotaGroup.getGroupCode());
        List<QuotaGroup> existingGroups = quotaGroupMapper.selectQuotaGroupList(existingGroup);
        if (!existingGroups.isEmpty()) {
            throw new RuntimeException("配额组编码已存在：" + quotaGroup.getGroupCode());
        }

        quotaGroup.setCreateBy(SecurityUtils.getUsername());
        int result = quotaGroupMapper.insertQuotaGroup(quotaGroup);

        // 插入部门关系
        if (quotaGroup.getDeptList() != null && !quotaGroup.getDeptList().isEmpty()) {
            for (QuotaGroupDept dept : quotaGroup.getDeptList()) {
                // 验证部门是否存在
                SysDept sysDept = deptService.selectDeptById(dept.getDeptId());
                if (sysDept == null) {
                    throw new RuntimeException("部门不存在：" + dept.getDeptId());
                }

                // 验证部门是否已属于其他配额组
                QuotaGroup existingDeptGroup = getQuotaGroupByDeptId(dept.getDeptId());
                if (existingDeptGroup != null) {
                    throw new RuntimeException("部门已属于其他配额组：" + existingDeptGroup.getGroupName());
                }

                dept.setGroupId(quotaGroup.getId());
                dept.setCreateBy(SecurityUtils.getUsername());
                quotaGroupMapper.insertQuotaGroupDept(dept);
            }
        }

        return result;
    }

    /**
     * 修改配额组
     *
     * @param quotaGroup 配额组
     * @return 结果
     */
    @Override
    @Transactional
    public int updateQuotaGroup(QuotaGroup quotaGroup)
    {
        // 验证配额组是否存在
        QuotaGroup existingGroup = quotaGroupMapper.selectQuotaGroupById(quotaGroup.getId());
        if (existingGroup == null) {
            throw new RuntimeException("配额组不存在：" + quotaGroup.getId());
        }

        // 验证配额组编码唯一性（排除自己）
        QuotaGroup codeCheckGroup = new QuotaGroup();
        codeCheckGroup.setGroupCode(quotaGroup.getGroupCode());
        List<QuotaGroup> existingGroups = quotaGroupMapper.selectQuotaGroupList(codeCheckGroup);
        for (QuotaGroup group : existingGroups) {
            if (!group.getId().equals(quotaGroup.getId())) {
                throw new RuntimeException("配额组编码已存在：" + quotaGroup.getGroupCode());
            }
        }

        quotaGroup.setUpdateBy(SecurityUtils.getUsername());

        // 删除原有部门关系
        quotaGroupMapper.deleteQuotaGroupDeptByGroupId(quotaGroup.getId());

        // 插入新的部门关系
        if (quotaGroup.getDeptList() != null && !quotaGroup.getDeptList().isEmpty()) {
            for (QuotaGroupDept dept : quotaGroup.getDeptList()) {
                // 验证部门是否存在
                SysDept sysDept = deptService.selectDeptById(dept.getDeptId());
                if (sysDept == null) {
                    throw new RuntimeException("部门不存在：" + dept.getDeptId());
                }

                // 验证部门是否已属于其他配额组（排除当前配额组）
                QuotaGroup existingDeptGroup = getQuotaGroupByDeptId(dept.getDeptId());
                if (existingDeptGroup != null && !existingDeptGroup.getId().equals(quotaGroup.getId())) {
                    throw new RuntimeException("部门已属于其他配额组：" + existingDeptGroup.getGroupName());
                }

                dept.setGroupId(quotaGroup.getId());
                dept.setCreateBy(SecurityUtils.getUsername());
                quotaGroupMapper.insertQuotaGroupDept(dept);
            }
        }

        return quotaGroupMapper.updateQuotaGroup(quotaGroup);
    }

    /**
     * 批量删除配额组
     * 
     * @param ids 需要删除的配额组主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteQuotaGroupByIds(Long[] ids)
    {
        for (Long id : ids) {
            // 删除部门关系
            quotaGroupMapper.deleteQuotaGroupDeptByGroupId(id);
            // 删除配额信息
            quotaGroupMapper.deleteQuotaGroupQuotaByGroupId(id);
        }
        return quotaGroupMapper.deleteQuotaGroupByIds(ids);
    }

    /**
     * 删除配额组信息
     * 
     * @param id 配额组主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteQuotaGroupById(Long id)
    {
        // 删除部门关系
        quotaGroupMapper.deleteQuotaGroupDeptByGroupId(id);
        // 删除配额信息
        quotaGroupMapper.deleteQuotaGroupQuotaByGroupId(id);
        return quotaGroupMapper.deleteQuotaGroupById(id);
    }

    /**
     * 根据部门ID查询配额组
     * 
     * @param deptId 部门ID
     * @return 配额组
     */
    @Override
    public QuotaGroup getQuotaGroupByDeptId(Long deptId)
    {
        return quotaGroupMapper.selectQuotaGroupByDeptId(deptId);
    }

    /**
     * 检查部门是否属于配额组
     * 
     * @param deptId 部门ID
     * @return 是否属于配额组
     */
    @Override
    public boolean isDeptInQuotaGroup(Long deptId)
    {
        QuotaGroup group = getQuotaGroupByDeptId(deptId);
        return group != null && "0".equals(group.getStatus());
    }

    /**
     * 获取配额组的配额信息
     * 
     * @param deptId 部门ID
     * @param year 年度
     * @return 配额组配额信息
     */
    @Override
    public QuotaGroupQuota getQuotaGroupQuotaByDept(Long deptId, String year)
    {
        QuotaGroup group = getQuotaGroupByDeptId(deptId);
        if (group == null) {
            return null;
        }
        
        QuotaGroupQuota quota = quotaGroupMapper.selectQuotaGroupQuotaByGroupAndYear(group.getId(), year);
        if (quota == null) {
            // 初始化配额
            quota = initQuotaGroupQuota(group.getId(), year);
        }
        
        // 实时计算已使用配额
        int actualUsedQuota = getActualUsedQuotaCount(group.getId(), year);
        quota.setUsedQuota(actualUsedQuota);
        quota.setRemainingQuota(quota.getHighScoreQuota() - actualUsedQuota);
        
        // 更新数据库
        if (quota.getId() != null) {
            updateQuotaGroupQuota(quota);
        }
        
        return quota;
    }

    /**
     * 检查配额组配额是否可用
     * 
     * @param deptId 部门ID
     * @param year 年度
     * @param requestCount 请求数量
     * @return 是否可用
     */
    @Override
    public boolean checkQuotaGroupQuotaAvailable(Long deptId, String year, int requestCount)
    {
        QuotaGroupQuota quota = getQuotaGroupQuotaByDept(deptId, year);
        return quota != null && quota.getRemainingQuota() >= requestCount;
    }

    /**
     * 使用配额组配额
     * 
     * @param deptId 部门ID
     * @param year 年度
     * @param count 使用数量
     * @return 是否成功
     */
    @Override
    public boolean useQuotaGroupQuota(Long deptId, String year, int count)
    {
        QuotaGroupQuota quota = getQuotaGroupQuotaByDept(deptId, year);
        if (quota == null || quota.getRemainingQuota() < count) {
            return false;
        }
        
        quota.setUsedQuota(quota.getUsedQuota() + count);
        quota.setRemainingQuota(quota.getHighScoreQuota() - quota.getUsedQuota());
        quota.setUpdateBy(SecurityUtils.getUsername());
        
        return updateQuotaGroupQuota(quota) > 0;
    }

    /**
     * 释放配额组配额
     * 
     * @param deptId 部门ID
     * @param year 年度
     * @param count 释放数量
     * @return 是否成功
     */
    @Override
    public boolean releaseQuotaGroupQuota(Long deptId, String year, int count)
    {
        QuotaGroupQuota quota = getQuotaGroupQuotaByDept(deptId, year);
        if (quota == null) {
            return false;
        }
        
        quota.setUsedQuota(Math.max(0, quota.getUsedQuota() - count));
        quota.setRemainingQuota(quota.getHighScoreQuota() - quota.getUsedQuota());
        quota.setUpdateBy(SecurityUtils.getUsername());
        
        return updateQuotaGroupQuota(quota) > 0;
    }

    /**
     * 初始化配额组年度配额
     *
     * @param groupId 配额组ID
     * @param year 年度
     * @return 配额信息
     */
    @Override
    public QuotaGroupQuota initQuotaGroupQuota(Long groupId, String year)
    {
        // 获取配额组的所有部门
        QuotaGroupDept queryDept = new QuotaGroupDept();
        queryDept.setGroupId(groupId);
        List<QuotaGroupDept> deptList = quotaGroupMapper.selectQuotaGroupDeptList(queryDept);

        int totalEmployees = 0;

        // 计算配额组内所有部门的总人数（排除部门负责人）
        for (QuotaGroupDept groupDept : deptList) {
            SysUser queryUser = new SysUser();
            queryUser.setDeptId(groupDept.getDeptId());
            List<SysUser> deptUsers = userService.selectUserList(queryUser);

            // 排除部门负责人
            SysDept dept = deptService.selectDeptById(groupDept.getDeptId());
            int deptEmployeeCount = (int) deptUsers.stream()
                .filter(user -> dept.getLeader() == null || !user.getUserName().equals(dept.getLeader()))
                .count();

            totalEmployees += deptEmployeeCount;
        }

        // 计算高分配额（30%）
        int highScoreQuota = (int) Math.floor(totalEmployees * QUOTA_PERCENTAGE.doubleValue());

        QuotaGroupQuota quota = new QuotaGroupQuota();
        quota.setGroupId(groupId);
        quota.setEvaluationYear(year);
        quota.setTotalEmployees(totalEmployees);
        quota.setHighScoreQuota(highScoreQuota);
        quota.setUsedQuota(0);
        quota.setRemainingQuota(highScoreQuota);
        quota.setCreateBy(SecurityUtils.getUsername());

        quotaGroupMapper.insertQuotaGroupQuota(quota);
        log.info("初始化配额组[{}]年度[{}]高分配额，总人数[{}]，配额[{}]", groupId, year, totalEmployees, highScoreQuota);

        return quota;
    }

    /**
     * 查询配额组部门关系列表
     *
     * @param quotaGroupDept 配额组部门关系
     * @return 配额组部门关系集合
     */
    @Override
    public List<QuotaGroupDept> selectQuotaGroupDeptList(QuotaGroupDept quotaGroupDept)
    {
        return quotaGroupMapper.selectQuotaGroupDeptList(quotaGroupDept);
    }

    /**
     * 查询配额组配额列表
     *
     * @param quotaGroupQuota 配额组配额
     * @return 配额组配额集合
     */
    @Override
    public List<QuotaGroupQuota> selectQuotaGroupQuotaList(QuotaGroupQuota quotaGroupQuota)
    {
        return quotaGroupMapper.selectQuotaGroupQuotaList(quotaGroupQuota);
    }

    /**
     * 新增配额组配额
     *
     * @param quotaGroupQuota 配额组配额
     * @return 结果
     */
    @Override
    public int insertQuotaGroupQuota(QuotaGroupQuota quotaGroupQuota)
    {
        quotaGroupQuota.setCreateBy(SecurityUtils.getUsername());
        return quotaGroupMapper.insertQuotaGroupQuota(quotaGroupQuota);
    }

    /**
     * 修改配额组配额
     *
     * @param quotaGroupQuota 配额组配额
     * @return 结果
     */
    @Override
    public int updateQuotaGroupQuota(QuotaGroupQuota quotaGroupQuota)
    {
        quotaGroupQuota.setUpdateBy(SecurityUtils.getUsername());
        return quotaGroupMapper.updateQuotaGroupQuota(quotaGroupQuota);
    }

    /**
     * 获取配额组在指定年度的实际使用配额数量
     *
     * @param groupId 配额组ID
     * @param year 年度
     * @return 实际使用数量
     */
    @Override
    public int getActualUsedQuotaCount(Long groupId, String year)
    {
        // 获取配额组的所有部门
        QuotaGroupDept queryDept = new QuotaGroupDept();
        queryDept.setGroupId(groupId);
        List<QuotaGroupDept> deptList = quotaGroupMapper.selectQuotaGroupDeptList(queryDept);

        int totalUsedCount = 0;

        // 统计所有部门的高分记录数量
        for (QuotaGroupDept groupDept : deptList) {
            int deptUsedCount = highScoreRecordMapper.countByDeptAndYear(groupDept.getDeptId(), year);
            totalUsedCount += deptUsedCount;
        }

        return totalUsedCount;
    }
}
