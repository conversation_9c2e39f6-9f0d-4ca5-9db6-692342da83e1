<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="项目承担部门" prop="deptId">
        <el-select
          v-model="queryParams.deptId"
          placeholder="请选择项目承担部门"
          clearable
          style="width: 240px"
          @change="handleQuery"
        >
          <el-option
            v-for="dept in deptOptions"
            :key="dept.deptId"
            :label="dept.deptName || ''"
            :value="dept.deptId"
            v-if="dept && dept.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="项目简称" prop="projectShortName">
        <el-input
          v-model="queryParams.projectShortName"
          placeholder="请输入项目简称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-date-picker clearable
          v-model="queryParams.createdAt"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" prop="updatedAt">
        <el-date-picker clearable
          v-model="queryParams.updatedAt"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择更新时间">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:info:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdateById"
          v-hasPermi="['system:info:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:info:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:info:import']"
        >导入</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:info:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" label="序号" width="50" align="center" :index="getIndex" />
      <!-- <el-table-column label="主键ID" align="center" prop="id" /> -->
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="项目简称" align="center" prop="projectShortName" />
      <!-- <el-table-column label="项目负责人" align="center" prop="leaderName" /> -->
      <el-table-column label="承担部门" align="center" prop="deptName" />
      <!-- <el-table-column label="配合人员" align="center">
        <template slot-scope="scope">
          <el-tag
            v-for="assistant in scope.row.assistantList"
            :key="assistant.userName"
            size="mini"
            style="margin-right: 5px"
          >
            {{ assistant.nickName }} - {{ assistant.deptName }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="参与人员" align="center">
        <template slot-scope="scope">
          <el-popover
            placement="top"
            width="300"
            trigger="hover"
          >
            <div>
              <el-tag
                v-for="member in getAllMembers(scope.row)"
                :key="`${scope.row.id}-${member.userName}-${member.role}`"
                size="mini"
                :type="member.role === '负责人' ? 'success' : ''"
                style="margin: 2px 5px"
              >
                {{ member.nickName }} - {{ member.role }}
              </el-tag>
            </div>
            <div slot="reference" style="display: inline-block; max-width: 250px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              <el-tag
                v-for="member in getAllMembers(scope.row)"
                :key="`${scope.row.id}-${member.userName}-${member.role}`"
                size="mini"
                :type="member.role === '负责人' ? 'success' : ''"
                style="margin-right: 5px"
              >
                {{ member.nickName }} - {{ member.role }}
              </el-tag>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remarks" />
      <!-- <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updatedAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:info:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:info:remove']"
          >删除</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAddMember(scope.row)"
            v-hasPermi="['system:members:add']"
          >添加项目成员</el-button> -->
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDeleteLeader(scope.row)"
            v-hasPermi="['system:members:remove']"
          >删除负责人</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目基础信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目简称" prop="projectShortName">
          <el-input v-model="form.projectShortName" placeholder="请输入项目简称" />
        </el-form-item>
        <el-form-item label="项目承担部门" prop="deptId">
          <el-select
            v-model="form.deptId"
            placeholder="请选择项目承担部门"
            style="width: 100%"
          >
            <el-option
              v-for="dept in deptOptions"
              :key="dept.deptId"
              :label="dept.deptName || ''"
              :value="dept.deptId"
              v-if="dept && dept.deptId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="项目负责人" prop="leader">
          <el-select
            v-model="form.leader"
            placeholder="请选择项目负责人"
            style="width: 240px"
            @focus="handleLeaderFocus"
            v-loading="leaderLoading"
            filterable
            :reserve-keyword="true"
          >
            <el-option
              v-for="item in leaderOptions"
              :key="item.optionId"
              :label="`${item.nickName} - ${item.dept.deptName}`"
              :value="item.userName"
            >
              <span style="float: left">{{ item.nickName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.dept.deptName }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目成员" prop="projectMembers">
          <el-select
            v-model="form.projectMembers"
            multiple
            filterable
            placeholder="请选择项目成员"
            style="width: 100%"
            @focus="handleMembersFocus"
            v-loading="membersLoading"
            :reserve-keyword="true"
          >
            <el-option
              v-for="item in membersOptions"
              :key="item.optionId"
              :label="`${item.nickName} - ${item.dept.deptName}`"
              :value="item.userName"
              :disabled="item.userName === form.leader"
            >
              <span style="float: left">{{ item.nickName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.dept.deptName }}</span>
            </el-option>
          </el-select>
          <!-- <div class="el-form-item-tip">项目成员将以"参与"角色添加到项目中</div> -->
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks"  placeholder="请输入内容" />
        </el-form-item>
        <!-- <el-form-item label="创建时间" prop="createdAt">
          <el-date-picker clearable
            v-model="form.createdAt"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新时间" prop="updatedAt">
          <el-date-picker clearable
            v-model="form.updatedAt"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择更新时间">
          </el-date-picker>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 项目导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload 
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的项目数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link 
            type="primary" 
            :underline="false" 
            style="font-size: 12px; vertical-align: baseline" 
            @click="importTemplate"
          >下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加项目成员对话框 -->
    <el-dialog :title="memberDialogTitle" :visible.sync="memberDialogOpen" width="500px" append-to-body>
      <el-form ref="memberForm" :model="memberForm" :rules="memberRules" label-width="100px">
        <el-form-item label="当前项目">
          <el-input v-model="currentProjectName" disabled />
        </el-form-item>
        <el-form-item label="成员姓名" prop="userNames">
          <el-select
            v-model="memberForm.userNames"
            multiple
            placeholder="请选择成员"
            clearable
            filterable
            style="width: 100%"
            :loading="userLoading"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.userName"
              :label="user.nickName + ' - ' + (user.dept ? user.dept.deptName : '')"
              :value="user.userName"
              :disabled="existingMembers.includes(user.userName)"
            >
              <span style="float: left">{{ user.nickName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ user.dept ? user.dept.deptName : '' }}
                <span v-if="existingMembers.includes(user.userName)" style="color: #F56C6C; margin-left: 5px">
                  (已在项目中)
                </span>
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目角色" prop="role">
          <el-input v-model="memberForm.role" disabled />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitMemberForm">确 定</el-button>
        <el-button @click="cancelMember">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, getInfo, delInfo, addInfo, updateInfo } from "@/api/system/info";
import { listDeptByParentId } from "@/api/system/dept";
import { listUser, listAllUser } from "@/api/system/user";
import { addMembers, listMembers, delMembers } from "@/api/system/members";
import { getToken } from "@/utils/auth";
import store from "@/store";

export default {
  name: "Info",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目基础信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        projectName: null,
        projectShortName: null,
        remarks: null,
        createdAt: null,
        updatedAt: null
      },
      // 表单参数
      form: {
        id: null,
        deptId: null,
        projectName: null,
        projectShortName: null,
        remarks: null,
        leader: null,          // 存储负责人用户名
        assistantUserNames: [], // 存储配合人员用户名数组
        projectMembers: [], // 新增：项目成员
        createdAt: undefined,
        updatedAt: undefined
      },
      // 表单校验
      rules: {
        deptId: [
          { required: true, message: "项目承担部门不能为空", trigger: "blur" }
        ],
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
        leader: [
          { required: true, message: "项目负责人不能为空", trigger: "change" }
        ]
      },
      deptOptions: [], // 部门选项列表
      userList: [], // 用户列表
      leaderOptions: [],
      leaderLoading: false,
      assistantOptions: [],
      assistantLoading: false,
      // 添加上传相关数据
      upload: {
        // 是否显示弹出层（项目导入）
        open: false,
        // 弹出层标题（项目导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的项目数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/info/importData"
      },
      // 添加一个全局计数器用于生成唯一的 key
      uniqueIdCounter: 0,
      optionCounter: 0, // 添加一个选项计数器
      memberDialogOpen: false,
      memberDialogTitle: "添加项目成员",
      memberForm: {
        userNames: [],
        role: '参与'
      },
      memberRules: {
        userNames: [
          { required: true, message: "请选择成员", trigger: "change" },
          { type: 'array', min: 1, message: '请至少选择一个成员', trigger: 'change' }
        ]
      },
      userOptions: [],
      userLoading: false,
      currentProjectName: '',
      currentProjectId: null,
      existingMembers: [],
      originalMembers: null,
      membersOptions: [],
      membersLoading: false
    };
  },
  created() {
    this.getList();
    this.getDeptList(); // 获取部门列表
    this.getUserList(); // 获取用户列表
    // 输出当前用户权限信息
    // console.log('当前用户权限列表:', store.getters.permissions);
  },
  watch: {
    'form.leader': {
      handler(newLeader, oldLeader) {
        // 当负责人发生变化时
        if (newLeader !== oldLeader) {
          // 如果新的负责人已经在项目成员中，需要从项目成员中移除
          if (newLeader && this.form.projectMembers && this.form.projectMembers.includes(newLeader)) {
            this.form.projectMembers = this.form.projectMembers.filter(userName => userName !== newLeader);
          }
          
          // 重新加载所有用户作为项目成员选项
          this.loadAllUsers().then(allUsers => {
            // 设置项目成员选项 - 标记负责人为禁用状态
            this.membersOptions = allUsers.map(user => ({
              ...user,
              optionId: this.getOptionId(),
              disabled: user.userName === newLeader
            }));
          });
        }
      }
    }
  },
  methods: {
    /** 获取部门列表 */
    getDeptList() {
      listDeptByParentId(100).then(response => {
        this.deptOptions = response.data;
      });
    },
    /** 获取用户列表 */
    getUserList() {
      listUser().then(response => {
        this.userList = response.rows;
      });
    },
    /** 查询项目基础信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        // console.log(this.infoList);
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        deptId: null,
        projectName: null,
        projectShortName: null,
        remarks: null,
        leader: null,
        assistantUserNames: [],
        projectMembers: [], // 新增：重置项目成员
        createdAt: undefined,
        updatedAt: undefined
      };
      this.resetForm("form");
      this.leaderOptions = [];
      this.assistantOptions = [];
      this.membersOptions = []; // 重置项目成员选项
      this.optionCounter = 0;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 加载用户列表数据
      this.userLoading = true;
      this.loadAllUsers().then(allUsers => {
        // 设置项目负责人选项
        this.leaderOptions = allUsers.map(user => ({
          ...user,
          optionId: this.getOptionId()
        }));

        this.userOptions = allUsers;
        this.userLoading = false;
      });
      
      this.open = true;
      this.title = "添加项目信息";
    },
    /** 通过ID修改按钮操作 */
    handleUpdateById() {
      if (!this.ids || this.ids.length !== 1) {
        this.$modal.msgError("请选择一条记录");
        return;
      }
      
      // 从选中的行数据中找到对应的记录
      const row = this.infoList.find(item => item.id === this.ids[0]);
      if (row) {
        this.handleUpdate(row);
      } else {
        this.$modal.msgError("获取项目信息失败");
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      if (!row || !row.memberList) {
        this.$modal.msgError("获取项目信息失败");
        return;
      }

      this.reset();
      
      // 从 memberList 中获取负责人和配合人员
      const leader = row.memberList.find(m => m.role === '负责人');
      
      // 获取现有项目成员
      const members = [...new Set(
        row.memberList
          .filter(m => m.role === '参与')
          .map(m => m.userName)
      )];

      // 保存原始成员信息
      this.originalMembers = {
        leader: leader?.userName || '',
        members: members
      };

      this.form = {
        id: row.id,
        deptId: row.deptId,
        projectName: row.projectName,
        projectShortName: row.projectShortName,
        remarks: row.remarks,
        leader: leader?.userName || '',
        assistantUserNames: [], // 保留空数组，防止引用错误
        projectMembers: members,
      };

      // 加载所有用户列表
      this.userLoading = true;
      this.loadAllUsers().then(allUsers => {
        // 设置项目负责人选项 - 显示所有用户
        this.leaderOptions = allUsers.map(user => ({
          ...user,
          optionId: this.getOptionId()
        }));
        
        // 设置项目成员选项
        this.userOptions = allUsers;
        this.userLoading = false;
      });
      
      this.open = true;
      this.title = "修改项目信息";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查成员身份重复
          const checkDuplicateMembers = () => {
            // 获取所有成员
            const allMembers = new Set([
              this.form.leader, // 负责人
              ...(this.form.projectMembers || []) // 参与人员
            ]);

            // 如果去重后的数量小于原始数量，说明有重复
            const totalCount = 1 + // 负责人
              (this.form.projectMembers?.length || 0);

            if (allMembers.size < totalCount) {
              // 找出重复的成员
              const duplicates = [];
              const seen = new Set();
              
              // 检查负责人
              if (this.form.leader) {
                seen.add(this.form.leader);
              }
              
              // 检查参与人员
              (this.form.projectMembers || []).forEach(userName => {
                if (seen.has(userName)) {
                  duplicates.push(userName);
                }
                seen.add(userName);
              });

              // 获取重复成员的昵称
              const duplicateNames = duplicates.map(userName => {
                const user = this.userOptions.find(u => u.userName === userName);
                return user ? user.nickName : userName;
              });

              this.$modal.msgError(`以下人员在项目中存在多个身份，请检查：${duplicateNames.join(', ')}`);
              return true; // 有重复
            }
            return false; // 无重复
          };

          // 如果有重复成员，终止提交
          if (checkDuplicateMembers()) {
            return;
          }

          // 构造提交的数据
          const submitData = {
            id: this.form.id,                    
            deptId: this.form.deptId,
            projectName: this.form.projectName,
            projectShortName: this.form.projectShortName,
            remarks: this.form.remarks,
            leaderName: this.form.leader,        // 项目负责人
          };

          if (submitData.id != null) {
            // 修改项目基本信息
            updateInfo(submitData).then(response => {
              if (response.code === 200) {
                // 添加所有成员
                const allMembers = [
                  // 项目成员
                  ...(this.form.projectMembers || []).map(userName => ({
                    projectId: submitData.id,
                    userName: userName,
                    role: '参与'
                  }))
                ];

                // 批量添加成员
                Promise.all(allMembers.map(member => addMembers(member)))
                  .then(() => {
                    this.$modal.msgSuccess("修改成功");
                    this.open = false;
                    this.getList();
                  })
                  .catch(error => {
                    console.error("添加成员失败:", error);
                    this.$modal.msgError("添加成员失败：" + (error.message || "未知错误"));
                  });
              } else {
                this.$modal.msgError(response.msg || "修改失败");
              }
            });
          } else {
            // 新增项目
            addInfo(submitData).then(response => {
              if (response.code === 200) {
                // 检查 response.data 和 response.data.id 是否存在
                if (!response.data || !response.data.id) {
                  this.$modal.msgError("新增失败：未获取到项目ID");
                  return;
                }

                const projectId = response.data.id;
                
                // 添加所有成员
                const allMembers = [
                  // 项目成员
                  ...(this.form.projectMembers || []).map(userName => ({
                    projectId: projectId,
                    userName: userName,
                    role: '参与'
                  }))
                ];

                // 如果没有成员需要添加，直接返回成功
                if (allMembers.length === 0) {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                  return;
                }

                // 批量添加成员
                Promise.all(allMembers.map(member => addMembers(member)))
                  .then(() => {
                    this.$modal.msgSuccess("新增成功");
                    this.open = false;
                    this.getList();
                  })
                  .catch(error => {
                    console.error("添加成员失败:", error);
                    this.$modal.msgError("添加成员失败：" + (error.message || "未知错误"));
                  });
              } else {
                this.$modal.msgError(response.msg || "新增失败");
              }
            }).catch(error => {
              console.error("新增项目失败:", error);
              this.$modal.msgError("新增失败：" + (error.message || "未知错误"));
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const projectIds = row.id || this.ids;
      this.$modal.confirm('确定要删除吗？').then(() => {
        return delInfo(projectIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/info/export', {
        ...this.queryParams
      }, `info_${new Date().getTime()}.xlsx`)
    },
    // 获取序号
    getIndex(index) {
      return (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1;
    },
    /** 项目负责人选择框获得焦点时触发 */
    handleLeaderFocus() {
      if (!this.leaderLoading && this.leaderOptions.length === 0) {
        this.leaderLoading = true;
        this.loadAllUsers().then(users => {
          this.leaderOptions = users;
          this.leaderLoading = false;
        }).catch(() => {
          this.leaderLoading = false;
        });
      }
    },
    /** 配合人员选择框获得焦点时触发 */
    handleAssistantFocus() {
      if (!this.assistantLoading && this.assistantOptions.length === 0) {
        this.assistantLoading = true;
        this.loadAllUsers().then(users => {
          // 使用 Set 来确保用户名的唯一性
          const seenUserNames = new Set();
          this.assistantOptions = users
            .filter(user => {
              if (user.userName !== this.form.leader && !seenUserNames.has(user.userName)) {
                seenUserNames.add(user.userName);
                return true;
              }
              return false;
            })
            .map(user => ({
              ...user,
              key: `${user.userId}-${Date.now()}` // 添加一个唯一的key属性
            }));
          this.assistantLoading = false;
        }).catch(() => {
          this.assistantLoading = false;
        });
      }
    },
    /** 获取所有成员（包括负责人和配合人员） */
    getAllMembers(row) {
      let members = [];
      
      // 使用 memberList 作为唯一数据源
      if (row.memberList && row.memberList.length > 0) {
        members = row.memberList.map((member, index) => ({
          nickName: member.nickName || member.userName,
          role: member.role,
          userName: member.userName,
        }));
      }
      
      // 去重处理，以 userName 和 role 为唯一标识
      const uniqueMembers = [];
      const seen = new Set();
      
      members.forEach(member => {
        const key = `${member.userName}-${member.role}`;
        if (!seen.has(key)) {
          seen.add(key);
          uniqueMembers.push(member);
        }
      });
      
      return uniqueMembers;
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "项目导入";
      this.upload.open = true;
    },
    
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/info/importTemplate', {
      }, `project_template_${new Date().getTime()}.xlsx`)
    },
    
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 生成唯一ID的方法
    getUniqueId() {
      this.uniqueIdCounter++;
      return `${Date.now()}-${this.uniqueIdCounter}-${Math.random().toString(36).substr(2)}`;
    },
    // 新增加载所有用户的方法
    loadAllUsers() {
      return new Promise((resolve, reject) => {
        listAllUser().then(response => {
          if (!response || typeof response !== 'object') {
            this.$modal.msgError('获取用户列表失败：无效的响应数据');
            reject(new Error('无效的响应数据'));
            return;
          }
          
          const userList = response.data || [];
          const users = userList.map(user => ({
            userId: user.userId,
            userName: user.userName,
            nickName: user.nickName || user.userName,
            dept: {
              deptId: user.dept?.deptId,
              deptName: user.dept?.deptName || '未分配部门'
            }
          }));
          
          resolve(users);
        }).catch(error => {
          console.error('获取用户列表错误:', error);
          this.$modal.msgError('获取用户列表失败：' + (error.message || '未知错误'));
          reject(error);
        });
      });
    },
    /** 生成唯一的选项ID */
    getOptionId() {
      this.optionCounter += 1;
      return this.optionCounter;
    },
    handleAddMember(row) {
      // 先重置表单
      this.resetMemberForm();
      
      // 设置当前项目信息
      this.currentProjectId = row.id;
      this.currentProjectName = row.projectName;
      
      // 获取当前项目的已有成员
      this.existingMembers = row.memberList ? row.memberList.map(member => member.userName) : [];
      
      // 获取用户列表
      this.getUsers();
      
      // 打开对话框
      this.memberDialogOpen = true;
    },
    getUsers() {
      this.userLoading = true;
      listUser().then(response => {
        this.userOptions = response.rows;
        this.userLoading = false;
      });
    },
    submitMemberForm() {
      this.$refs["memberForm"].validate(valid => {
        if (valid) {
          if (!this.memberForm.userNames || this.memberForm.userNames.length === 0) {
            this.$modal.msgError("请选择要添加的成员");
            return;
          }

          // 检查是否有重复成员
          const duplicates = this.memberForm.userNames.filter(userName => 
            this.existingMembers.includes(userName)
          );

          if (duplicates.length > 0) {
            this.$modal.msgError(`以下成员已在项目中，无法重复添加：${duplicates.join(', ')}`);
            return;
          }

          const addRequests = this.memberForm.userNames.map(userName => {
            return {
              projectId: this.currentProjectId,
              userName: userName,
              role: this.memberForm.role
            };
          });

          Promise.all(addRequests.map(request => addMembers(request)))
            .then(() => {
              this.$modal.msgSuccess("添加成功");
              this.memberDialogOpen = false;
              // 重置表单
              this.resetMemberForm();
              // 刷新数据
              this.getList();
            })
            .catch(() => {
              this.$modal.msgError("添加成员失败");
            });
        }
      });
    },
    cancelMember() {
      this.memberDialogOpen = false;
      this.resetMemberForm();
    },
    resetMemberForm() {
      // 重置表单数据
      this.memberForm = {
        userNames: [],
        role: '参与'
      };
      // 重置已有成员列表
      this.existingMembers = [];
      // 清空当前项目信息
      this.currentProjectId = null;
      this.currentProjectName = '';
      
      // 如果表单引用存在，重置验证状态
      if (this.$refs.memberForm) {
        this.$refs.memberForm.resetFields();
      }
    },
    /** 判断用户是否是原始项目成员（修改前就存在的成员） */
    isOriginalMember(userName) {
      // 检查是否是原始负责人
      if (this.originalMembers?.leader === userName) {
        return true;
      }
      // 检查是否是原始项目成员
      return this.originalMembers?.members?.includes(userName);
    },
    /** 判断用户是否已经是项目成员（包括新选择的） */
    isUserAlreadyMember(userName) {
      // 检查是否是负责人
      if (userName === this.form.leader) {
        return true;
      }
      // 检查是否已经在项目成员中
      return this.form.projectMembers.includes(userName);
    },
    handleDeleteLeader(row) {
      if (!row.memberList || !row.memberList.find(m => m.role === '负责人')) {
        this.$modal.msgError("该项目暂无负责人");
        return;
      }
      
      this.$modal.confirm('是否确认删除该项目的负责人？').then(() => {
        // 找到负责人成员
        const leader = row.memberList.find(m => m.role === '负责人');
        if (leader) {
          // 使用成员关联的 id 进行删除
          return delMembers(leader.id);  // leader.id 应该是项目成员关联表的主键id
        }
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        this.getList();
      }).catch(() => {});
    },
    /** 项目成员选择框获得焦点时触发 */
    handleMembersFocus() {
      if (!this.membersLoading && this.membersOptions.length === 0) {
        this.membersLoading = true;
        this.loadAllUsers().then(users => {
          // 过滤掉已选为负责人的用户
          this.membersOptions = users.map(user => ({
            ...user,
            optionId: this.getOptionId(),
            disabled: user.userName === this.form.leader
          }));
          this.membersLoading = false;
        }).catch(() => {
          this.membersLoading = false;
        });
      }
    }
  }
};
</script>

<style>
.el-form-item__label {
  min-width: 80px;
}

/* 添加下拉框的滚动条样式 */
.el-select-dropdown .el-select-dropdown__wrap {
  max-height: 204px;  /* 34px(每项高度) * 6项 = 204px */
  overflow-y: auto;
}

/* 美化滚动条样式 */
.el-select-dropdown .el-select-dropdown__wrap::-webkit-scrollbar {
  width: 6px;
}

.el-select-dropdown .el-select-dropdown__wrap::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: #c0c4cc;
}

.el-select-dropdown .el-select-dropdown__wrap::-webkit-scrollbar-track {
  border-radius: 3px;
  background: #ebeef5;
}

/* 调整下拉选项的样式 */
.el-select-dropdown .el-select-dropdown__item {
  height: 34px;
  line-height: 34px;
  padding: 0 15px;
}

/* 调整部门名称的样式 */
.el-select-dropdown .el-select-dropdown__item span:last-child {
  color: #909399;
  font-size: 12px;
  margin-left: 10px;
}

/* 添加以下样式来优化下拉框的显示 */
.el-select-dropdown__item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
}

.el-select-dropdown__item span:first-child {
  color: #606266;
}

.el-select-dropdown__item span:last-child {
  color: #909399;
  font-size: 12px;
}

/* 项目成员提示文字样式 */
.el-form-item-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}
</style>
