<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="员工编号" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入员工编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="员工姓名" prop="realName">
        <el-input
          v-model="queryParams.realName"
          placeholder="请输入员工姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发放月份" prop="salaryMonth">
        <el-date-picker
          v-model="queryParams.salaryMonth"
          type="month"
          placeholder="选择月份"
          value-format="yyyy-MM"
          style="width: 140px"
        />
      </el-form-item>
      <!-- <el-form-item label="工资数值" prop="salary">
        <el-input
          v-model="queryParams.salary"
          placeholder="请输入工资数值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createdAt">
        <el-date-picker clearable
          v-model="queryParams.createdAt"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" prop="updatedAt">
        <el-date-picker clearable
          v-model="queryParams.updatedAt"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择更新时间">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:salary:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:salary:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:salary:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:salary:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['system:salary:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="salaryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="员工编号" align="center" prop="userName" />
      <el-table-column label="员工姓名" align="center" prop="realName" />
      <el-table-column label="发放月份" align="center" prop="salaryMonth" />
      <el-table-column label="工资数值" align="center" prop="salary" />
      <!-- <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updatedAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:salary:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:salary:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改员工薪酬对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="员工姓名" prop="userName">
          <el-select
            v-model="selectedUser"
            placeholder="请选择员工"
            filterable
            clearable
            style="width: 100%"
            @change="handleUserChange"
            value-key="userName"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.userName"
              :label="`${user.nickName}-${user.userName}`"
              :value="user"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工资月份" prop="salaryMonth">
          <el-date-picker
            v-model="form.salaryMonth"
            type="month"
            placeholder="选择月份"
            value-format="yyyy-MM"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="工资数值" prop="salary">
          <el-input v-model="form.salary" placeholder="请输入工资数值" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :data="upload.data"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <div>月份格式必须为"YYYY-MM"，例如：2024-02</div>
          <el-link
            :underline="false"
            style="font-size:12px;vertical-align: baseline;"
            type="primary"
            @click="importTemplate"
          >下载模板</el-link>
        </div>
      </el-upload>
      <el-form :model="upload" label-width="120px">
        <el-form-item label="是否更新已有数据">
          <el-radio-group v-model="upload.updateSupport">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加导入结果对话框 -->
    <el-dialog :title="upload.resultTitle" :visible.sync="upload.resultOpen" width="500px" append-to-body center>
      <div class="upload-result" v-html="upload.resultMsg"></div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="upload.resultOpen = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSalary, getSalary, delSalary, addSalary, updateSalary } from "@/api/system/salary";
import { listUser } from "@/api/system/user";
import { getToken } from "@/utils/auth";

export default {
  name: "Salary",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 员工薪酬表格数据
      salaryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        realName: null,
        salaryMonth: null,
        salary: null,
        createdAt: null,
        updatedAt: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "员工编号不能为空", trigger: "blur" }
        ],
        realName: [
          { required: true, message: "员工姓名不能为空", trigger: "blur" }
        ],
        salaryMonth: [
          { required: true, message: "工资月份(格式：YYYY-MM)不能为空", trigger: "blur" }
        ],
        salary: [
          { required: true, message: "工资数值不能为空", trigger: "blur" }
        ]
        // createdAt: [
        //   { required: true, message: "创建时间不能为空", trigger: "blur" }
        // ],
        // updatedAt: [
        //   { required: true, message: "更新时间不能为空", trigger: "blur" }
        // ]
      },
      // 用户选项
      userOptions: [],
      // 选中的用户
      selectedUser: null,
      // 上传参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/salary/importData",
        // 是否更新已有数据
        updateSupport: false,
        data: {
          updateSupport: false
        },
        resultOpen: false,  // 是否显示结果对话框
        resultTitle: "导入结果", // 结果对话框标题
        resultMsg: "",      // 导入结果信息
      }
    };
  },
  created() {
    this.getList();
    // 获取用户列表
    this.getUserList();
  },
  methods: {
    /** 查询员工薪酬列表 */
    getList() {
      this.loading = true;
      listSalary(this.queryParams).then(response => {
        this.salaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取用户列表 */
    getUserList() {
      listUser().then(response => {
        this.userOptions = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userName: null,
        realName: null,
        salaryMonth: null,
        salary: null
      };
      this.selectedUser = null;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加员工薪酬";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSalary(id).then(response => {
        this.form = response.data;
        // 设置选中的用户
        this.selectedUser = this.userOptions.find(user => 
          user.userName === this.form.userName
        ) || null;
        this.open = true;
        this.title = "修改员工薪酬";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查是否存在重复记录
          const existingSalary = this.salaryList.find(item => 
            item.userName === this.form.userName && 
            item.salaryMonth === this.form.salaryMonth &&
            item.id !== this.form.id
          );

          if (existingSalary) {
            this.$modal.msgError("每人每月只能添加一次工资记录");
            return;
          }

          if (this.form.id != null) {
            updateSalary(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSalary(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除员工薪酬编号为"' + ids + '"的数据项？').then(function() {
        return delSalary(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/salary/export', {
        ...this.queryParams
      }, `salary_${new Date().getTime()}.xlsx`)
    },
    /** 用户选择改变时的处理 */
    handleUserChange(user) {
      if (user) {
        this.form.userName = user.userName;
        this.form.realName = user.nickName;
      } else {
        this.form.userName = '';
        this.form.realName = '';
        this.selectedUser = null;
      }
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "薪资导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/salary/importTemplate', {}, `salary_template_${new Date().getTime()}.xlsx`);
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      
      if (response.code === 200) {
        this.getList();
        // 显示导入结果对话框
        this.upload.resultTitle = "导入结果";
        this.upload.resultMsg = response.msg;
        this.upload.resultOpen = true;
      } else {
        // 显示导入结果对话框（错误信息）
        this.upload.resultTitle = "导入失败";
        this.upload.resultMsg = response.msg;
        this.upload.resultOpen = true;
      }
    },
    // 提交上传文件
    submitFileForm() {
      this.$confirm('是否确认导入薪资数据?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 更新 data 中的 updateSupport 值
        this.upload.data.updateSupport = this.upload.updateSupport;
        this.$refs.upload.submit();
      });
    }
  }
};
</script>

<style scoped>
.upload-result {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px 20px;
}
</style>
