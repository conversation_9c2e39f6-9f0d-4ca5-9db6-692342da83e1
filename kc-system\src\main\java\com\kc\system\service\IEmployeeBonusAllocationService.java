package com.kc.system.service;

import java.util.List;
import com.kc.system.domain.EmployeeBonusAllocation;
import com.kc.system.domain.dto.EmployeeBonusAllocationDTO;

/**
 * 员工奖金分配Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IEmployeeBonusAllocationService 
{
    /**
     * 查询员工奖金分配
     * 
     * @param id 员工奖金分配主键
     * @return 员工奖金分配
     */
    public EmployeeBonusAllocation selectEmployeeBonusAllocationById(Long id);

    /**
     * 查询员工奖金分配列表
     * 
     * @param employeeBonusAllocation 员工奖金分配
     * @return 员工奖金分配集合
     */
    public List<EmployeeBonusAllocation> selectEmployeeBonusAllocationList(EmployeeBonusAllocation employeeBonusAllocation);

    /**
     * 根据用户ID和月份查询员工奖金分配
     * 
     * @param userId 用户ID
     * @param allocationMonth 分配月份
     * @return 员工奖金分配
     */
    public EmployeeBonusAllocation selectByUserIdAndMonth(Long userId, String allocationMonth);

    /**
     * 根据部门ID和月份查询员工奖金分配列表
     * 
     * @param deptId 部门ID
     * @param allocationMonth 分配月份
     * @return 员工奖金分配集合
     */
    public List<EmployeeBonusAllocation> selectByDeptIdAndMonth(Long deptId, String allocationMonth);

    /**
     * 根据月份查询所有员工奖金分配
     *
     * @param allocationMonth 分配月份
     * @return 员工奖金分配集合
     */
    public List<EmployeeBonusAllocation> selectByMonth(String allocationMonth);

    /**
     * 根据分配者ID和月份查询员工奖金分配列表
     *
     * @param allocatorId 分配者ID
     * @param allocationMonth 分配月份
     * @return 员工奖金分配集合
     */
    public List<EmployeeBonusAllocation> selectByAllocatorAndMonth(Long allocatorId, String allocationMonth);

    /**
     * 新增员工奖金分配
     * 
     * @param employeeBonusAllocation 员工奖金分配
     * @return 结果
     */
    public int insertEmployeeBonusAllocation(EmployeeBonusAllocation employeeBonusAllocation);

    /**
     * 批量分配员工奖金
     * 
     * @param employeeBonusAllocationDTO 员工奖金分配DTO
     * @return 结果
     */
    public int batchAllocateEmployeeBonus(EmployeeBonusAllocationDTO employeeBonusAllocationDTO);

    /**
     * 修改员工奖金分配
     * 
     * @param employeeBonusAllocation 员工奖金分配
     * @return 结果
     */
    public int updateEmployeeBonusAllocation(EmployeeBonusAllocation employeeBonusAllocation);

    /**
     * 批量删除员工奖金分配
     * 
     * @param ids 需要删除的员工奖金分配主键集合
     * @return 结果
     */
    public int deleteEmployeeBonusAllocationByIds(Long[] ids);

    /**
     * 删除员工奖金分配信息
     * 
     * @param id 员工奖金分配主键
     * @return 结果
     */
    public int deleteEmployeeBonusAllocationById(Long id);

    /**
     * 根据部门奖金分配ID删除员工奖金分配
     * 
     * @param deptBonusId 部门奖金分配ID
     * @return 结果
     */
    public int deleteByDeptBonusId(Long deptBonusId);

    /**
     * 根据月份删除员工奖金分配
     * 
     * @param allocationMonth 分配月份
     * @return 结果
     */
    public int deleteByMonth(String allocationMonth);

    /**
     * 获取部门负责人可分配的员工列表
     * 
     * @param deptId 部门ID
     * @param allocationMonth 分配月份
     * @return 员工列表
     */
    public List<EmployeeBonusAllocation> getAvailableEmployeesForAllocation(Long deptId, String allocationMonth);
}
