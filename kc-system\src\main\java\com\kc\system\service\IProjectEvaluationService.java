package com.kc.system.service;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import com.kc.system.domain.ProjectEvaluation;

/**
 * 项目评价Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-01
 */
public interface IProjectEvaluationService 
{
    /**
     * 查询项目评价
     * 
     * @param id 项目评价主键
     * @return 项目评价
     */
    public ProjectEvaluation selectProjectEvaluationById(Long id);

    /**
     * 查询项目评价列表
     * 
     * @param projectEvaluation 项目评价
     * @return 项目评价集合
     */
    public List<ProjectEvaluation> selectProjectEvaluationList(ProjectEvaluation projectEvaluation);

    /**
     * 新增项目评价
     * 
     * @param projectEvaluation 项目评价
     * @return 结果
     */
    public int insertProjectEvaluation(ProjectEvaluation projectEvaluation);

    /**
     * 修改项目评价
     * 
     * @param projectEvaluation 项目评价
     * @return 结果
     */
    public int updateProjectEvaluation(ProjectEvaluation projectEvaluation);

    /**
     * 批量删除项目评价
     * 
     * @param ids 需要删除的项目评价主键集合
     * @return 结果
     */
    public int deleteProjectEvaluationByIds(Long[] ids);

    /**
     * 删除项目评价信息
     * 
     * @param id 项目评价主键
     * @return 结果
     */
    public int deleteProjectEvaluationById(Long id);
    
    /**
     * 获取用户参与的项目评分列表
     * 
     * @param userName 用户名
     * @param evaluationMonth 评价月份 (可选)
     * @return 项目评分列表
     */
    public List<ProjectEvaluation> selectUserProjectEvaluations(String userName, String evaluationMonth);
    
    /**
     * 通过用户ID获取项目评分列表
     *
     * @param userId 用户ID
     * @param evaluationMonth 评价月份 (可选)
     * @return 项目评分列表
     */
    public List<ProjectEvaluation> selectUserProjectEvaluationsByUserId(Long userId, String evaluationMonth);

    /**
     * 验证机构负责人评分前置条件
     * 检查被评分人是否为项目工作人员，如果是，则检查项目负责人是否已评分
     *
     * @param evaluateeId 被评价人ID
     * @param evaluationMonth 评价月份
     * @return 验证结果，包含是否通过和详细信息
     */
    public Map<String, Object> validateManagerEvaluationPreconditions(Long evaluateeId, String evaluationMonth);

    /**
     * 导出部门评分数据
     *
     * @param response HTTP响应对象
     * @param deptId 部门ID
     * @param evaluationMonth 评价月份
     * @param deptName 部门名称
     * @param userIds 用户ID列表（可选，如果为null则导出所有用户）
     */
    public void exportDeptEvaluationData(HttpServletResponse response, Long deptId, String evaluationMonth, String deptName, List<Long> userIds);
}
