import request from '@/utils/request'

// 查询项目成员列表
export function listMembers(query) {
  return request({
    url: '/system/members/list',
    method: 'get',
    params: query
  })
}

// 查询项目成员关联详细
export function getMembers(id) {
  return request({
    url: '/system/members/' + id,
    method: 'get'
  })
}

// 新增项目成员关联
export function addMembers(data) {
  return request({
    url: '/system/members',
    method: 'post',
    data: data
  })
}

// 修改项目成员关联
export function updateMembers(data) {
  return request({
    url: '/system/members',
    method: 'put',
    data: data
  })
}

// 删除项目成员关联
export function delMembers(id) {
  return request({
    url: '/system/members/' + id,
    method: 'delete'
  })
}

// 获取部门信息
export function getDept(deptId) {
  return request({
    url: '/system/members/dept/' + deptId,
    method: 'get'
  })
}

// 获取部门列表
export function getDeptList() {
  return request({
    url: '/system/members/deptList',
    method: 'get'
  })
}

// 获取用户参与的所有项目
export function getUserProjects(userName) {
  return request({
    url: '/system/members/userProjects/' + userName,
    method: 'get',
    responseType: 'json',
    timeout: 10000,
    headers: {
      'Cache-Control': 'no-cache'
    }
  }).catch(error => {
    console.error('获取用户项目API调用失败:', error);
    throw error;
  });
}

// 获取部门下有项目的用户及其项目信息
export function getDeptMembersWithProjects(deptIds) {
  // 如果是数组，将其转为逗号分隔的字符串
  const deptIdsParam = Array.isArray(deptIds) ? deptIds.join(',') : deptIds;
  
  return request({
    url: '/system/members/deptProjectMembers/' + deptIdsParam,
    method: 'get',
    responseType: 'json',
    timeout: 20000, // 增大超时时间，因为可能会处理大量数据
    headers: {
      'Cache-Control': 'no-cache'
    }
  }).catch(error => {
    console.error('获取部门成员项目信息API调用失败:', error);
    throw error;
  });
}
