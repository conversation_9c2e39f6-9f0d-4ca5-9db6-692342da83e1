-- ===== 将2025年7月1日以来创建的数据的evaluation_month改为2025-07 =====
-- 条件：created_at >= '2025-07-01' 且 evaluation_month != '2022-08'

-- ===== 第一步：数据备份 =====
CREATE TABLE project_evaluation_backup_july_update_20250715 AS 
SELECT * FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00';

-- ===== 第二步：分析当前数据 =====
-- 查看2025年7月1日以来创建的数据分布
SELECT 
    '2025年7月1日以来创建的数据分布' as analysis_type,
    evaluation_month,
    evaluation_type,
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00'
GROUP BY evaluation_month, evaluation_type
ORDER BY evaluation_month, evaluation_type;

-- 查看需要修改的数据（排除2022-08）
SELECT 
    '需要修改的数据统计' as analysis_type,
    evaluation_month,
    evaluation_type,
    COUNT(*) as count
FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00'
AND evaluation_month != '2022-08'
GROUP BY evaluation_month, evaluation_type
ORDER BY evaluation_month, evaluation_type;

-- 查看不需要修改的数据（2022-08）
SELECT 
    '不修改的数据统计(2022-08)' as analysis_type,
    evaluation_month,
    evaluation_type,
    COUNT(*) as count
FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00'
AND evaluation_month = '2022-08'
GROUP BY evaluation_month, evaluation_type;

-- ===== 第三步：预览要修改的数据 =====
-- 预览前20条要修改的记录
SELECT 
    '即将修改的数据预览' as preview_type,
    id,
    evaluation_month as current_month,
    '2025-07' as new_month,
    evaluation_type,
    evaluator_id,
    evaluatee_id,
    project_id,
    created_at
FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00'
AND evaluation_month != '2022-08'
ORDER BY created_at
LIMIT 20;

-- ===== 第四步：执行修改 =====
-- 将2025年7月1日以来创建的数据（除了2022-08）的evaluation_month改为2025-07
UPDATE project_evaluation 
SET evaluation_month = '2025-07'
WHERE created_at >= '2025-07-01 00:00:00'
AND evaluation_month != '2022-08';

-- ===== 第五步：验证修改结果 =====
-- 检查修改后的数据分布
SELECT 
    '修改后数据分布' as verification_type,
    evaluation_month,
    evaluation_type,
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00'
GROUP BY evaluation_month, evaluation_type
ORDER BY evaluation_month, evaluation_type;

-- 确认修改的记录数
SELECT 
    '修改统计' as verification_type,
    '已修改为2025-07的记录数' as description,
    COUNT(*) as count
FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00'
AND evaluation_month = '2025-07';

-- 确认未修改的2022-08记录数
SELECT 
    '修改统计' as verification_type,
    '保持2022-08不变的记录数' as description,
    COUNT(*) as count
FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00'
AND evaluation_month = '2022-08';

-- 检查是否还有其他月份的数据（应该为0）
SELECT 
    '修改统计' as verification_type,
    '其他月份剩余记录数(应该为0)' as description,
    COUNT(*) as count
FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00'
AND evaluation_month NOT IN ('2025-07', '2022-08');

-- ===== 第六步：详细验证 =====
-- 按评价类型统计修改结果
SELECT 
    '按评价类型统计修改结果' as verification_type,
    evaluation_type,
    COUNT(*) as total_count,
    SUM(CASE WHEN evaluation_month = '2025-07' THEN 1 ELSE 0 END) as modified_to_2025_07,
    SUM(CASE WHEN evaluation_month = '2022-08' THEN 1 ELSE 0 END) as kept_2022_08
FROM project_evaluation 
WHERE created_at >= '2025-07-01 00:00:00'
GROUP BY evaluation_type
ORDER BY evaluation_type;

-- ===== 第七步：最终确认 =====
SELECT 
    '修改完成' as status,
    NOW() as completion_time,
    '2025年7月1日以来创建的数据(除2022-08)已全部改为2025-07' as result;

-- ===== 回滚说明 =====
/*
如果需要回滚，可以执行以下语句：
DELETE FROM project_evaluation WHERE created_at >= '2025-07-01 00:00:00';
INSERT INTO project_evaluation SELECT * FROM project_evaluation_backup_july_update_20250715;
*/

-- ===== 清理备份表（确认修改成功后执行） =====
-- DROP TABLE project_evaluation_backup_july_update_20250715;
