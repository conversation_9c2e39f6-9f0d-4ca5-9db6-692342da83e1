package com.kc.system.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 评分计算配置类
 * 确保所有策略类被正确扫描和注册
 *
 * <AUTHOR>
 */
@Configuration
@ComponentScan(basePackages = {
    "com.kc.system.strategy",
    "com.kc.system.factory",
    "com.kc.system.service.impl"
})
public class ScoreCalculationConfig {
    // 配置类，用于确保策略模式相关的组件被正确扫描
}
