package com.kc.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 高分记录对象 high_score_record
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class HighScoreRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 评价年度 */
    @Excel(name = "评价年度")
    private String evaluationYear;

    /** 评价月份（如：2024-12） */
    @Excel(name = "评价月份", readConverterExp = "如=：2024-12")
    private String evaluationMonth;

    /** 评分（95-100分） */
    @Excel(name = "评分", readConverterExp = "9=5-100分")
    private BigDecimal score;

    /** 评价人ID */
    @Excel(name = "评价人ID")
    private Long evaluatorId;

    /** 评价人姓名 */
    @Excel(name = "评价人姓名")
    private String evaluatorName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setEvaluationYear(String evaluationYear) 
    {
        this.evaluationYear = evaluationYear;
    }

    public String getEvaluationYear() 
    {
        return evaluationYear;
    }
    public void setEvaluationMonth(String evaluationMonth) 
    {
        this.evaluationMonth = evaluationMonth;
    }

    public String getEvaluationMonth() 
    {
        return evaluationMonth;
    }
    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }
    public void setEvaluatorId(Long evaluatorId) 
    {
        this.evaluatorId = evaluatorId;
    }

    public Long getEvaluatorId() 
    {
        return evaluatorId;
    }
    public void setEvaluatorName(String evaluatorName) 
    {
        this.evaluatorName = evaluatorName;
    }

    public String getEvaluatorName() 
    {
        return evaluatorName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("evaluationYear", getEvaluationYear())
            .append("evaluationMonth", getEvaluationMonth())
            .append("score", getScore())
            .append("evaluatorId", getEvaluatorId())
            .append("evaluatorName", getEvaluatorName())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
