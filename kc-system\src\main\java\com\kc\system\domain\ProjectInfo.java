package com.kc.system.domain;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;
import java.util.ArrayList;
import java.util.HashMap;
import com.kc.common.annotation.Excel.Type;
import javax.validation.constraints.NotNull;

/**
 * 项目基础信息对象 project_info
 * 
 * <AUTHOR>
 * @date 2025-02-14
 */
public class ProjectInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 项目承担部门ID */
    @Excel(name = "部门ID")
    @NotNull(message = "部门ID不能为空")
    private Long deptId;

    /** 项目名称 */
    @Excel(name = "项目名称", type = Type.ALL)
    private String projectName;

    /** 项目简称 */
    @Excel(name = "项目简称", type = Type.ALL)
    private String projectShortName;

    /** 备注 */
    @Excel(name = "备注", type = Type.ALL)
    private String remarks;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updatedAt;

    /** 项目负责人姓名 */
    @Excel(name = "项目负责人员工编号")
    private String leaderName;

    /** 部门名称 */
//    @Excel(name = "承担部门")
    private String deptName;

    /** 项目负责人账号 */
    private String leader;

    /** 配合人员列表 */
    private List<Map<String, Object>> assistantList;

    /** 所有参与人员列表 */
    private List<Map<String, Object>> memberList;
    
    /** 参与人员显示字符串 */
//    @Excel(name = "参与人员")
    private String memberNames;

    /** 项目成员（参与角色）*/
    private String projectMembers;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }
    public void setProjectShortName(String projectShortName) 
    {
        this.projectShortName = projectShortName;
    }

    public String getProjectShortName() 
    {
        return projectShortName;
    }
    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }
    public void setCreatedAt(Date createdAt) 
    {
        this.createdAt = createdAt;
    }

    public Date getCreatedAt() 
    {
        return createdAt;
    }
    public void setUpdatedAt(Date updatedAt) 
    {
        this.updatedAt = updatedAt;
    }

    public Date getUpdatedAt() 
    {
        return updatedAt;
    }

    public String getLeaderName() {
        return leaderName;
    }

    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getLeader() {
        return leader;
    }

    public void setLeader(String leader) {
        this.leader = leader;
    }

    public List<Map<String, Object>> getAssistantList() {
        return assistantList;
    }

    public void setAssistantList(List<Map<String, Object>> assistantList) {
        this.assistantList = assistantList;
    }

    public void setMemberList(List<Map<String, Object>> memberList) {
        // 创建新的列表并复制内容，避免引用问题
        this.memberList = new ArrayList<>();
        if (memberList != null) {
            for (Map<String, Object> member : memberList) {
                // 创建新的 Map 避免引用问题
                Map<String, Object> newMember = new HashMap<>(member);
                this.memberList.add(newMember);
            }
        }
    }

    public List<Map<String, Object>> getMemberList() {
        return memberList;
    }
    
    public String getMemberNames() {
        return memberNames;
    }
    
    public void setMemberNames(String memberNames) {
        this.memberNames = memberNames;
    }

    /** 获取项目成员 */
    public String getProjectMembers()
    {
        return projectMembers;
    }

    /** 设置项目成员 */
    public void setProjectMembers(String projectMembers)
    {
        this.projectMembers = projectMembers;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("projectName", getProjectName())
            .append("projectShortName", getProjectShortName())
            .append("remarks", getRemarks())
            .append("createdAt", getCreatedAt())
            .append("updatedAt", getUpdatedAt())
            .append("leaderName", getLeaderName())
            .append("deptName", getDeptName())
            .append("leader", getLeader())
            .append("assistantList", getAssistantList())
            .append("memberList", getMemberList())
            .append("memberNames", getMemberNames())
            .append("projectMembers", getProjectMembers())
            .toString();
    }
}
