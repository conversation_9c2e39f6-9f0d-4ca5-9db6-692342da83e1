<template>
  <div>
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="工作月份" prop="workMonth">
        <el-date-picker
          v-model="queryParams.workMonth"
          type="month"
          placeholder="选择月份"
          value-format="yyyy-MM"
          style="width: 140px"
          @change="handleMonthChange"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:info:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 项目工作量统计表格 -->
    <el-table 
      v-loading="loading" 
      :data="projectStatsList"
      style="width: 100%"
    >
      <el-table-column label="序号" type="index" width="50" align="center" 
        :index="indexMethod"/>
      <el-table-column 
        label="项目名称" 
        align="center" 
        prop="projectName"
        min-width="150"
      />
      <el-table-column 
        label="工作月份" 
        align="center" 
        prop="workMonth"
        width="120"
      />
      <el-table-column 
        label="工作量分配" 
        align="center"
        min-width="200"
      >
        <template slot-scope="scope">
          <div>{{ formatWorkload(scope.row.workloadValue) }}</div>
        </template>
      </el-table-column>
      <el-table-column 
        label="涉及人数" 
        align="center" 
        prop="memberCount" 
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.memberCount || '0' }}
        </template>
      </el-table-column>
      <el-table-column 
        label="整体劳务费(万元)" 
        align="center" 
        prop="totalSalary" 
        width="150"
      >
        <template slot-scope="scope">
          <el-tooltip 
            v-if="scope.row.missingData"
            effect="dark" 
            :content="scope.row.missingData"
            placement="top"
          >
            <span>-</span>
          </el-tooltip>
          <span v-else>
            {{ scope.row.totalSalary ? formatSalary(scope.row.totalSalary) : '-' }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { getAllProjectStats } from "@/api/system/workload";
import { listAllProjects } from "@/api/system/info";
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

export default {
  name: "ProjectStatistics",
  components: {
    Pagination: () => import("@/components/Pagination")
  },
  props: {
    workMonth: {
      type: String,
      default: null
    },
    projectName: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      projectStatsList: [],
      projectList: [],
      filteredProjectList: [],
      showSearch: true,
      // 查询参数
      queryParams: {
        workMonth: this.getCurrentMonth(),
        projectName: null
      }
    };
  },
  created() {
    this.getProjectList();
  },
  methods: {
    /** 获取当前月份 YYYY-MM 格式 */
    getCurrentMonth() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${year}-${month}`;
    },
    getProjectList() {
      listAllProjects().then(response => {
        this.projectList = response.rows || [];
        if (this.projectList.length > 0) {
          this.search();
        }
      });
    },
    // 序号计算方法
    indexMethod(index) {
      return index + 1;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.search();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.workMonth = this.getCurrentMonth();
      this.queryParams.projectName = null;
      this.handleQuery();
    },
    // 月份变化时获取数据
    handleMonthChange() {
      this.handleQuery();
    },
    // 搜索方法
    search() {
      this.loading = true;
      getAllProjectStats(this.queryParams.workMonth).then(response => {
        // 获取所有统计数据
        let stats = response.data || [];
        
        // 如果有项目名称筛选
        if (this.queryParams.projectName) {
          stats = stats.filter(item => 
            item.projectName.toLowerCase().includes(this.queryParams.projectName.toLowerCase())
          );
        }
        
        // 排序处理
        this.projectStatsList = stats.sort((a, b) => {
          // 先按月份降序
          if (b.workMonth !== a.workMonth) {
            return b.workMonth.localeCompare(a.workMonth);
          }
          // 再按项目名称升序
          return a.projectName.localeCompare(b.projectName);
        });
        
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    formatWorkload(value) {
      if (!value) return '0.00';
      return Number(value).toFixed(2);
    },
    formatSalary(value) {
      return Number(value).toFixed(4);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有项目统计数据项?').then(() => {
        this.$modal.loading("正在导出数据，请稍候...");
        
        try {
          // 准备导出数据
          const exportData = this.projectStatsList.map((item, index) => {
            return {
              '序号': index + 1,
              '项目名称': item.projectName,
              '工作月份': item.workMonth,
              '工作量分配': this.formatWorkload(item.workloadValue),
              '涉及人数': item.memberCount || '0',
              '整体劳务费(万元)': item.totalSalary ? this.formatSalary(item.totalSalary) : '-'
            };
          });

          // 创建工作簿
          const wb = XLSX.utils.book_new();
          // 创建工作表
          const ws = XLSX.utils.json_to_sheet(exportData);
          // 设置列宽
          const colWidth = [
            { wch: 8 },  // 序号
            { wch: 20 }, // 项目名称
            { wch: 12 }, // 工作月份
            { wch: 12 }, // 工作量分配
            { wch: 10 }, // 涉及人数
            { wch: 15 }  // 整体劳务费
          ];
          ws['!cols'] = colWidth;

          // 添加工作表到工作簿
          XLSX.utils.book_append_sheet(wb, ws, '项目统计');

          // 生成文件名
          const fileName = `项目统计_${this.queryParams.workMonth || '全部'}_${new Date().getTime()}.xlsx`;

          // 导出文件
          XLSX.writeFile(wb, fileName);

          this.$modal.closeLoading();
          this.$modal.msgSuccess("导出成功");
        } catch (error) {
          console.error('导出失败:', error);
          this.$modal.closeLoading();
          this.$modal.msgError("导出失败");
        }
      });
    },
    /** 查询列表 */
    getList() {
      this.search();
    }
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.search-form {
  margin-bottom: 18px;
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 15px;
  padding: 10px;
  background: #fff;
}
</style>