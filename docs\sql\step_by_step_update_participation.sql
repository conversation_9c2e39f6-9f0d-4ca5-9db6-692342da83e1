-- ===== 分步修改project_participation表month字段 =====
-- 安全地将month='2025-07'改为month='2025-06'

-- ===== 步骤1：创建备份 =====
-- 执行前请确认：这将创建备份表
CREATE TABLE project_participation_backup_20250715 AS 
SELECT * FROM project_participation WHERE month IN ('2025-06', '2025-07');

-- 验证备份
SELECT 
    '备份验证' as step,
    month,
    COUNT(*) as backup_count
FROM project_participation_backup_20250715
GROUP BY month;

-- ===== 步骤2：分析当前数据 =====
-- 查看要修改的数据详情
SELECT 
    '当前2025-07数据分析' as analysis,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_name) as unique_users,
    COUNT(DISTINCT project_id) as unique_projects,
    MIN(created_at) as earliest_date,
    MAX(created_at) as latest_date
FROM project_participation 
WHERE month = '2025-07';

-- 查看部分具体记录
SELECT 
    '2025-07数据样本' as sample_type,
    id, user_name, project_id, project_name, participation_rate, created_at
FROM project_participation 
WHERE month = '2025-07'
ORDER BY created_at
LIMIT 5;

-- ===== 步骤3：检查潜在冲突 =====
-- 检查是否存在同一用户同一项目在2025-06已有记录的情况
SELECT 
    '潜在冲突检查' as check_type,
    p07.user_name,
    p07.project_id,
    p07.project_name,
    p06.participation_rate as existing_2025_06_rate,
    p07.participation_rate as will_change_2025_07_rate
FROM project_participation p07
INNER JOIN project_participation p06 ON 
    p07.user_name = p06.user_name 
    AND p07.project_id = p06.project_id
WHERE p07.month = '2025-07' 
AND p06.month = '2025-06'
ORDER BY p07.user_name, p07.project_id;

-- ===== 步骤4：执行修改（请谨慎执行） =====
-- 注意：执行前请确认上述检查结果
/*
UPDATE project_participation 
SET month = '2025-06'
WHERE month = '2025-07';
*/

-- ===== 步骤5：验证修改结果 =====
-- 执行修改后运行此查询验证结果
/*
SELECT 
    '修改后验证' as verification,
    month,
    COUNT(*) as count
FROM project_participation 
WHERE month IN ('2025-05', '2025-06', '2025-07', '2025-08')
GROUP BY month
ORDER BY month;

-- 确认2025-07记录已清空
SELECT 
    '2025-07记录检查' as check_type,
    COUNT(*) as remaining_count,
    CASE WHEN COUNT(*) = 0 THEN '✓ 已全部修改' ELSE '✗ 仍有记录' END as status
FROM project_participation 
WHERE month = '2025-07';

-- 检查2025-06记录数量变化
SELECT 
    '2025-06记录变化' as check_type,
    (SELECT COUNT(*) FROM project_participation WHERE month = '2025-06') as current_count,
    (SELECT COUNT(*) FROM project_participation_backup_20250715 WHERE month = '2025-06') as original_2025_06,
    (SELECT COUNT(*) FROM project_participation_backup_20250715 WHERE month = '2025-07') as original_2025_07,
    (SELECT COUNT(*) FROM project_participation WHERE month = '2025-06') - 
    (SELECT COUNT(*) FROM project_participation_backup_20250715 WHERE month = '2025-06') as increase_count;
*/

-- ===== 步骤6：回滚方案 =====
-- 如果修改有问题，可以使用以下语句回滚
/*
-- 删除修改后的数据
DELETE FROM project_participation WHERE month IN ('2025-06', '2025-07');

-- 从备份恢复
INSERT INTO project_participation 
SELECT * FROM project_participation_backup_20250715;

-- 验证回滚结果
SELECT 
    '回滚验证' as step,
    month,
    COUNT(*) as count
FROM project_participation 
WHERE month IN ('2025-06', '2025-07')
GROUP BY month;
*/

-- ===== 使用说明 =====
SELECT '===== 使用说明 =====' as instructions;

SELECT 
    '执行顺序' as step_order,
    '1. 先执行步骤1-3，查看分析结果' as step_1,
    '2. 确认无冲突后，取消步骤4的注释并执行' as step_2,
    '3. 执行步骤5验证结果' as step_3,
    '4. 如有问题，执行步骤6回滚' as step_4;
