package com.kc.system.service;

import java.util.List;
import com.kc.system.domain.EvaluationConfig;

/**
 * 评价系统配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
public interface IEvaluationConfigService 
{
    /**
     * 查询评价系统配置
     * 
     * @param id 评价系统配置主键
     * @return 评价系统配置
     */
    public EvaluationConfig selectEvaluationConfigById(Long id);

    /**
     * 查询评价系统配置列表
     * 
     * @param evaluationConfig 评价系统配置
     * @return 评价系统配置集合
     */
    public List<EvaluationConfig> selectEvaluationConfigList(EvaluationConfig evaluationConfig);

    /**
     * 根据配置类型和月份查询配置
     * 
     * @param configType 配置类型
     * @param month 月份
     * @return 评价系统配置
     */
    public EvaluationConfig selectEvaluationConfigByTypeAndMonth(String configType, String month);

    /**
     * 检查当前时间是否允许执行特定配置类型的操作
     * 
     * @param configType 配置类型
     * @param month 月份
     * @return true=允许操作，false=不允许操作
     */
    public boolean checkOperationAllowed(String configType, String month);

    /**
     * 新增评价系统配置
     * 
     * @param evaluationConfig 评价系统配置
     * @return 结果
     */
    public int insertEvaluationConfig(EvaluationConfig evaluationConfig);

    /**
     * 修改评价系统配置
     * 
     * @param evaluationConfig 评价系统配置
     * @return 结果
     */
    public int updateEvaluationConfig(EvaluationConfig evaluationConfig);

    /**
     * 批量删除评价系统配置
     * 
     * @param ids 需要删除的评价系统配置主键集合
     * @return 结果
     */
    public int deleteEvaluationConfigByIds(Long[] ids);

    /**
     * 删除评价系统配置信息
     * 
     * @param id 评价系统配置主键
     * @return 结果
     */
    public int deleteEvaluationConfigById(Long id);
    
    /**
     * 校验配置类型和月份组合是否唯一
     * 
     * @param evaluationConfig 评价系统配置
     * @return 结果
     */
    public boolean checkConfigTypeAndMonthUnique(EvaluationConfig evaluationConfig);
} 