package com.kc.system.domain.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 员工奖金分配DTO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class EmployeeBonusAllocationDTO {
    
    /** 部门奖金分配ID */
    private Long deptBonusId;
    
    /** 分配月份 */
    private String allocationMonth;
    
    /** 部门ID */
    private Long deptId;
    
    /** 部门剩余奖金 */
    private BigDecimal remainingBonus;
    
    /** 员工奖金分配列表 */
    private List<EmployeeBonusItem> employeeBonusList;
    
    public static class EmployeeBonusItem {
        /** 用户ID */
        private Long userId;
        
        /** 用户名 */
        private String userName;
        
        /** 用户姓名 */
        private String nickName;
        
        /** 分配奖金金额 */
        private BigDecimal bonusAmount;
        
        /** 分配原因 */
        private String allocationReason;

        // getter和setter方法
        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getNickName() {
            return nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public BigDecimal getBonusAmount() {
            return bonusAmount;
        }

        public void setBonusAmount(BigDecimal bonusAmount) {
            this.bonusAmount = bonusAmount;
        }

        public String getAllocationReason() {
            return allocationReason;
        }

        public void setAllocationReason(String allocationReason) {
            this.allocationReason = allocationReason;
        }
    }

    // getter和setter方法
    public Long getDeptBonusId() {
        return deptBonusId;
    }

    public void setDeptBonusId(Long deptBonusId) {
        this.deptBonusId = deptBonusId;
    }

    public String getAllocationMonth() {
        return allocationMonth;
    }

    public void setAllocationMonth(String allocationMonth) {
        this.allocationMonth = allocationMonth;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public BigDecimal getRemainingBonus() {
        return remainingBonus;
    }

    public void setRemainingBonus(BigDecimal remainingBonus) {
        this.remainingBonus = remainingBonus;
    }

    public List<EmployeeBonusItem> getEmployeeBonusList() {
        return employeeBonusList;
    }

    public void setEmployeeBonusList(List<EmployeeBonusItem> employeeBonusList) {
        this.employeeBonusList = employeeBonusList;
    }
}
