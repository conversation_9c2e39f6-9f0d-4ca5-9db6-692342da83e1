package com.kc.system.mapper;

import java.util.List;
import com.kc.system.domain.HighScoreRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 高分记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface HighScoreRecordMapper 
{
    /**
     * 查询高分记录
     * 
     * @param id 高分记录主键
     * @return 高分记录
     */
    public HighScoreRecord selectHighScoreRecordById(Long id);

    /**
     * 根据用户ID和年度查询高分记录
     * 
     * @param userId 用户ID
     * @param evaluationYear 评价年度
     * @return 高分记录
     */
    public HighScoreRecord selectByUserAndYear(@Param("userId") Long userId, @Param("evaluationYear") String evaluationYear);

    /**
     * 查询高分记录列表
     * 
     * @param highScoreRecord 高分记录
     * @return 高分记录集合
     */
    public List<HighScoreRecord> selectHighScoreRecordList(HighScoreRecord highScoreRecord);

    /**
     * 新增高分记录
     * 
     * @param highScoreRecord 高分记录
     * @return 结果
     */
    public int insertHighScoreRecord(HighScoreRecord highScoreRecord);

    /**
     * 修改高分记录
     * 
     * @param highScoreRecord 高分记录
     * @return 结果
     */
    public int updateHighScoreRecord(HighScoreRecord highScoreRecord);

    /**
     * 删除高分记录
     * 
     * @param id 高分记录主键
     * @return 结果
     */
    public int deleteHighScoreRecordById(Long id);

    /**
     * 批量删除高分记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHighScoreRecordByIds(Long[] ids);

    /**
     * 统计部门在指定年度的高分记录数量
     *
     * @param deptId 部门ID
     * @param evaluationYear 评价年度
     * @return 高分记录数量
     */
    public int countByDeptAndYear(@Param("deptId") Long deptId, @Param("evaluationYear") String evaluationYear);
}
