-- 查找机构成员评分菜单的SQL脚本

-- 1. 查找可能的机构成员评分菜单
SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    perms,
    menu_type,
    visible,
    status
FROM sys_menu 
WHERE (
    menu_name = '机构成员评分' 
    OR menu_name LIKE '%机构%评分%' 
    OR menu_name LIKE '%成员评分%' 
    OR menu_name LIKE '%部门评价%'
    OR menu_name LIKE '%员工评分%'
    OR menu_name LIKE '%评价管理%'
)
AND menu_type = 'C'
AND status = '0'
ORDER BY menu_name;

-- 2. 查看所有评分/评价相关的菜单
SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    perms,
    menu_type
FROM sys_menu 
WHERE (
    menu_name LIKE '%评分%' 
    OR menu_name LIKE '%评价%'
    OR menu_name LIKE '%考核%'
    OR perms LIKE '%evaluation%'
    OR path LIKE '%evaluation%'
)
AND status = '0'
ORDER BY parent_id, order_num;

-- 3. 查看完整的菜单树结构（评价相关）
SELECT 
    CONCAT(
        CASE WHEN p.menu_name IS NOT NULL THEN CONCAT(p.menu_name, ' > ') ELSE '' END,
        m.menu_name
    ) as menu_path,
    m.menu_id,
    m.menu_name,
    m.parent_id,
    m.path,
    m.component,
    m.perms,
    m.menu_type
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE (
    m.menu_name LIKE '%评分%' 
    OR m.menu_name LIKE '%评价%'
    OR m.menu_name LIKE '%考核%'
    OR m.perms LIKE '%evaluation%'
    OR m.path LIKE '%evaluation%'
    OR p.menu_name LIKE '%评分%' 
    OR p.menu_name LIKE '%评价%'
)
AND m.status = '0'
ORDER BY p.order_num, m.order_num;

-- 4. 如果需要手动指定菜单ID，请使用以下模板：
/*
-- 手动设置机构成员评分菜单ID
SET @org_member_evaluation_menu_id = 具体的菜单ID;

-- 然后执行权限配置
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES 
('高分配额查询', @org_member_evaluation_menu_id, 10, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:query', '#', 'admin', sysdate(), '', null, '查询部门高分配额信息'),
('高分配额检查', @org_member_evaluation_menu_id, 11, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:check', '#', 'admin', sysdate(), '', null, '检查高分配额是否可用'),
('高分配额管理', @org_member_evaluation_menu_id, 12, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:manage', '#', 'admin', sysdate(), '', null, '管理高分配额（使用和释放）');
*/
