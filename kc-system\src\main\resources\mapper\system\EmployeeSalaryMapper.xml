<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.EmployeeSalaryMapper">
    
    <resultMap type="EmployeeSalary" id="EmployeeSalaryResult">
        <result property="id"    column="id"    />
        <result property="userName"    column="user_name"    />
        <result property="realName"    column="real_name"    />
        <result property="salaryMonth"    column="salary_month"    />
        <result property="salary"    column="salary"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectEmployeeSalaryVo">
        select id, user_name, real_name, salary_month, salary, created_at, updated_at from employee_salary
    </sql>

    <select id="selectEmployeeSalaryList" parameterType="EmployeeSalary" resultMap="EmployeeSalaryResult">
        <include refid="selectEmployeeSalaryVo"/>
        <where>  
            <if test="userName != null and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="realName != null and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="salaryMonth != null and salaryMonth != ''"> and salary_month = #{salaryMonth}</if>
            <if test="salary != null"> and salary = #{salary}</if>
        </where>
    </select>
    
    <select id="selectEmployeeSalaryById" parameterType="Long" resultMap="EmployeeSalaryResult">
        <include refid="selectEmployeeSalaryVo"/>
        where id = #{id}
    </select>

    <insert id="insertEmployeeSalary" parameterType="EmployeeSalary" useGeneratedKeys="true" keyProperty="id">
        insert into employee_salary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="realName != null and realName != ''">real_name,</if>
            <if test="salaryMonth != null and salaryMonth != ''">salary_month,</if>
            <if test="salary != null">salary,</if>
            created_at,
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="realName != null and realName != ''">#{realName},</if>
            <if test="salaryMonth != null and salaryMonth != ''">#{salaryMonth},</if>
            <if test="salary != null">#{salary},</if>
            NOW(),
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateEmployeeSalary" parameterType="EmployeeSalary">
        update employee_salary
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="realName != null and realName != ''">real_name = #{realName},</if>
            <if test="salaryMonth != null and salaryMonth != ''">salary_month = #{salaryMonth},</if>
            <if test="salary != null">salary = #{salary},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            updated_at = NOW(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeSalaryById" parameterType="Long">
        delete from employee_salary where id = #{id}
    </delete>

    <delete id="deleteEmployeeSalaryByIds" parameterType="String">
        delete from employee_salary where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 检查是否存在重复记录 -->
    <select id="checkDuplicateSalary" resultType="Integer">
        select count(1) 
        from employee_salary 
        where user_name = #{userName} 
        and salary_month = #{salaryMonth}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <select id="selectByMonths" resultMap="EmployeeSalaryResult">
        SELECT 
            id,
            user_name,
            real_name,
            salary_month,
            salary,
            created_at,
            updated_at
        FROM employee_salary
        WHERE salary_month IN
        <foreach item="month" collection="months" open="(" separator="," close=")">
            #{month}
        </foreach>
    </select>

    <!-- 根据月份查询所有员工薪酬 -->
    <select id="selectEmployeeSalaryByMonth" resultMap="EmployeeSalaryResult">
        select user_name, salary
        from employee_salary
        where salary_month = #{workMonth}
    </select>
</mapper>