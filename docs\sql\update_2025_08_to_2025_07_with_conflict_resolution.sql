-- ===== 将2025-08改为2025-07并处理冲突 =====
-- 方案：保留最新记录，删除旧记录

-- ===== 第一步：创建备份表 =====
CREATE TABLE project_participation_backup_20250715_v2 AS 
SELECT * FROM project_participation WHERE month IN ('2025-06', '2025-07', '2025-08');

-- ===== 第二步：分析冲突情况 =====
-- 查看冲突统计
SELECT 
    '冲突分析' as step,
    COUNT(*) as conflict_count,
    '2025-07与2025-08之间的重复用户-项目组合' as description
FROM project_participation pp1
INNER JOIN project_participation pp2 ON pp1.user_name = pp2.user_name AND pp1.project_id = pp2.project_id
WHERE pp1.month = '2025-07' AND pp2.month = '2025-08';

-- ===== 第三步：创建要保留的记录列表 =====
-- 对于冲突的记录，保留创建时间最新的
CREATE TEMPORARY TABLE records_to_keep_v2 AS
SELECT 
    user_name,
    project_id,
    MAX(created_at) as latest_created,
    CASE 
        WHEN MAX(created_at) = MAX(CASE WHEN month = '2025-07' THEN created_at END) THEN '2025-07'
        WHEN MAX(created_at) = MAX(CASE WHEN month = '2025-08' THEN created_at END) THEN '2025-08'
        ELSE '2025-07'  -- 默认保留2025-07
    END as keep_month
FROM project_participation 
WHERE month IN ('2025-07', '2025-08')
GROUP BY user_name, project_id;

-- 查看保留策略统计
SELECT 
    '保留策略统计' as step,
    keep_month,
    COUNT(*) as count
FROM records_to_keep_v2 
GROUP BY keep_month;

-- ===== 第四步：删除冲突的旧记录 =====
-- 预览要删除的记录数量
SELECT 
    '即将删除的记录统计' as step,
    COUNT(*) as delete_count
FROM project_participation pp
INNER JOIN records_to_keep_v2 rtk ON pp.user_name = rtk.user_name AND pp.project_id = rtk.project_id
WHERE pp.month IN ('2025-07', '2025-08')
AND NOT (pp.month = rtk.keep_month AND pp.created_at = rtk.latest_created);

-- 执行删除操作
DELETE pp FROM project_participation pp
INNER JOIN records_to_keep_v2 rtk ON pp.user_name = rtk.user_name AND pp.project_id = rtk.project_id
WHERE pp.month IN ('2025-07', '2025-08')
AND NOT (pp.month = rtk.keep_month AND pp.created_at = rtk.latest_created);

-- ===== 第五步：将剩余的2025-08记录改为2025-07 =====
-- 预览要修改的记录
SELECT 
    '即将修改的2025-08记录' as step,
    COUNT(*) as update_count
FROM project_participation 
WHERE month = '2025-08';

-- 执行修改
UPDATE project_participation 
SET month = '2025-07'
WHERE month = '2025-08';

-- 清理临时表
DROP TEMPORARY TABLE records_to_keep_v2;

-- ===== 第六步：验证修改结果 =====
-- 检查修改后的数据分布
SELECT 
    '修改后数据分布' as step,
    month, 
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_participation 
WHERE month IN ('2025-06', '2025-07', '2025-08') 
GROUP BY month 
ORDER BY month;

-- 检查是否还有重复数据
SELECT 
    '重复数据检查' as step,
    CASE WHEN COUNT(*) = 0 THEN '✓ 无重复数据' 
         ELSE CONCAT('✗ 仍有', COUNT(*), '组重复数据') END as result
FROM (
    SELECT user_name, project_id, COUNT(*) as cnt
    FROM project_participation 
    WHERE month = '2025-07'
    GROUP BY user_name, project_id
    HAVING COUNT(*) > 1
) duplicates;

-- 确认2025-08数据已全部修改
SELECT 
    '2025-08数据检查' as step,
    CASE WHEN COUNT(*) = 0 THEN '✓ 所有2025-08数据已改为2025-07' 
         ELSE CONCAT('✗ 仍有', COUNT(*), '条2025-08数据') END as result
FROM project_participation 
WHERE month = '2025-08';

-- ===== 第七步：最终统计 =====
SELECT 
    '执行完成' as status,
    NOW() as completion_time,
    '2025-08数据已合并到2025-07，冲突已解决' as result;

-- ===== 回滚说明 =====
/*
如果需要回滚，可以执行：
DELETE FROM project_participation WHERE month IN ('2025-06', '2025-07', '2025-08');
INSERT INTO project_participation SELECT * FROM project_participation_backup_20250715_v2;
*/
