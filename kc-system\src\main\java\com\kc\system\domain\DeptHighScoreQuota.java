package com.kc.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 部门高分配额对象 dept_high_score_quota
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class DeptHighScoreQuota extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 评价年度（如：2024） */
    @Excel(name = "评价年度", readConverterExp = "如=：2024")
    private String evaluationYear;

    /** 部门总人数 */
    @Excel(name = "部门总人数")
    private Integer totalEmployees;

    /** 高分配额（30%） */
    @Excel(name = "高分配额", readConverterExp = "3=0%")
    private Integer highScoreQuota;

    /** 已使用配额 */
    @Excel(name = "已使用配额")
    private Integer usedQuota;

    /** 剩余配额 */
    @Excel(name = "剩余配额")
    private Integer remainingQuota;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setEvaluationYear(String evaluationYear) 
    {
        this.evaluationYear = evaluationYear;
    }

    public String getEvaluationYear() 
    {
        return evaluationYear;
    }
    public void setTotalEmployees(Integer totalEmployees) 
    {
        this.totalEmployees = totalEmployees;
    }

    public Integer getTotalEmployees() 
    {
        return totalEmployees;
    }
    public void setHighScoreQuota(Integer highScoreQuota) 
    {
        this.highScoreQuota = highScoreQuota;
    }

    public Integer getHighScoreQuota() 
    {
        return highScoreQuota;
    }
    public void setUsedQuota(Integer usedQuota) 
    {
        this.usedQuota = usedQuota;
    }

    public Integer getUsedQuota() 
    {
        return usedQuota;
    }
    public void setRemainingQuota(Integer remainingQuota) 
    {
        this.remainingQuota = remainingQuota;
    }

    public Integer getRemainingQuota() 
    {
        return remainingQuota;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("evaluationYear", getEvaluationYear())
            .append("totalEmployees", getTotalEmployees())
            .append("highScoreQuota", getHighScoreQuota())
            .append("usedQuota", getUsedQuota())
            .append("remainingQuota", getRemainingQuota())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
