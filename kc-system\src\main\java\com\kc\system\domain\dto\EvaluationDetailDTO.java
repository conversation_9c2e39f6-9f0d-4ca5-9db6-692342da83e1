package com.kc.system.domain.dto;

import java.math.BigDecimal;
import java.util.List;
import com.kc.system.domain.ProjectParticipation;
import com.kc.system.domain.ProjectEvaluation;

/**
 * 评价结果计算明细数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-05-25
 */
public class EvaluationDetailDTO {
    
    /** 用户ID */
    private Long userId;
    
    /** 员工编号 */
    private String userName;
    
    /** 姓名 */
    private String nickName;
    
    /** 部门ID */
    private Long deptId;
    
    /** 部门名称 */
    private String deptName;
    
    /** 评价月份 */
    private String evaluationMonth;
    
    /** 最终评分 */
    private BigDecimal finalScore;
    
    /** 机构负责人评分 */
    private BigDecimal managerScore;
    
    /** 项目负责人平均评分 */
    private BigDecimal projectLeaderScore;
    
    /** 用户角色 */
    private String userRole;
    
    /** 项目参与情况列表 */
    private List<ProjectParticipationDTO> effortList;
    
    /** 项目负责人评分列表 */
    private List<ProjectLeaderScoreDTO> projectLeaderScoreList;
    
    /** 机构负责人评分列表 */
    private List<ManagerScoreDTO> managerScoreList;
    
    /** 项目负责人评分计算公式 */
    private String projectLeaderScoreFormula;
    
    /** 最终得分计算公式 */
    private String finalScoreFormula;
    
    /**
     * 项目参与情况DTO
     */
    public static class ProjectParticipationDTO {
        private Long id;
        private Long projectId;
        private String projectName;
        private String projectShortName;
        private BigDecimal participationRate;
        private String comments;
        
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public Long getProjectId() {
            return projectId;
        }
        
        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }
        
        public String getProjectName() {
            return projectName;
        }
        
        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }
        
        public String getProjectShortName() {
            return projectShortName;
        }
        
        public void setProjectShortName(String projectShortName) {
            this.projectShortName = projectShortName;
        }
        
        public BigDecimal getParticipationRate() {
            return participationRate;
        }
        
        public void setParticipationRate(BigDecimal participationRate) {
            this.participationRate = participationRate;
        }
        
        public String getComments() {
            return comments;
        }
        
        public void setComments(String comments) {
            this.comments = comments;
        }
    }
    
    /**
     * 项目负责人评分DTO
     */
    public static class ProjectLeaderScoreDTO {
        private Long projectId;
        private String projectName;
        private String projectShortName;
        private Long evaluatorId;
        private String evaluatorName;
        private BigDecimal score;
        private BigDecimal participationRate;
        private String createdAt;
        
        public Long getProjectId() {
            return projectId;
        }
        
        public void setProjectId(Long projectId) {
            this.projectId = projectId;
        }
        
        public String getProjectName() {
            return projectName;
        }
        
        public void setProjectName(String projectName) {
            this.projectName = projectName;
        }
        
        public String getProjectShortName() {
            return projectShortName;
        }
        
        public void setProjectShortName(String projectShortName) {
            this.projectShortName = projectShortName;
        }
        
        public Long getEvaluatorId() {
            return evaluatorId;
        }
        
        public void setEvaluatorId(Long evaluatorId) {
            this.evaluatorId = evaluatorId;
        }
        
        public String getEvaluatorName() {
            return evaluatorName;
        }
        
        public void setEvaluatorName(String evaluatorName) {
            this.evaluatorName = evaluatorName;
        }
        
        public BigDecimal getScore() {
            return score;
        }
        
        public void setScore(BigDecimal score) {
            this.score = score;
        }
        
        public BigDecimal getParticipationRate() {
            return participationRate;
        }
        
        public void setParticipationRate(BigDecimal participationRate) {
            this.participationRate = participationRate;
        }
        
        public String getCreatedAt() {
            return createdAt;
        }
        
        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }
    }
    
    /**
     * 机构负责人评分DTO
     */
    public static class ManagerScoreDTO {
        private Long evaluatorId;
        private String evaluatorName;
        private BigDecimal score;
        private String comments;
        private String createdAt;
        
        public Long getEvaluatorId() {
            return evaluatorId;
        }
        
        public void setEvaluatorId(Long evaluatorId) {
            this.evaluatorId = evaluatorId;
        }
        
        public String getEvaluatorName() {
            return evaluatorName;
        }
        
        public void setEvaluatorName(String evaluatorName) {
            this.evaluatorName = evaluatorName;
        }
        
        public BigDecimal getScore() {
            return score;
        }
        
        public void setScore(BigDecimal score) {
            this.score = score;
        }
        
        public String getComments() {
            return comments;
        }
        
        public void setComments(String comments) {
            this.comments = comments;
        }
        
        public String getCreatedAt() {
            return createdAt;
        }
        
        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getEvaluationMonth() {
        return evaluationMonth;
    }

    public void setEvaluationMonth(String evaluationMonth) {
        this.evaluationMonth = evaluationMonth;
    }

    public BigDecimal getFinalScore() {
        return finalScore;
    }

    public void setFinalScore(BigDecimal finalScore) {
        this.finalScore = finalScore;
    }

    public BigDecimal getManagerScore() {
        return managerScore;
    }

    public void setManagerScore(BigDecimal managerScore) {
        this.managerScore = managerScore;
    }

    public BigDecimal getProjectLeaderScore() {
        return projectLeaderScore;
    }

    public void setProjectLeaderScore(BigDecimal projectLeaderScore) {
        this.projectLeaderScore = projectLeaderScore;
    }

    public String getUserRole() {
        return userRole;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public List<ProjectParticipationDTO> getEffortList() {
        return effortList;
    }

    public void setEffortList(List<ProjectParticipationDTO> effortList) {
        this.effortList = effortList;
    }

    public List<ProjectLeaderScoreDTO> getProjectLeaderScoreList() {
        return projectLeaderScoreList;
    }

    public void setProjectLeaderScoreList(List<ProjectLeaderScoreDTO> projectLeaderScoreList) {
        this.projectLeaderScoreList = projectLeaderScoreList;
    }

    public List<ManagerScoreDTO> getManagerScoreList() {
        return managerScoreList;
    }

    public void setManagerScoreList(List<ManagerScoreDTO> managerScoreList) {
        this.managerScoreList = managerScoreList;
    }

    public String getProjectLeaderScoreFormula() {
        return projectLeaderScoreFormula;
    }

    public void setProjectLeaderScoreFormula(String projectLeaderScoreFormula) {
        this.projectLeaderScoreFormula = projectLeaderScoreFormula;
    }

    public String getFinalScoreFormula() {
        return finalScoreFormula;
    }

    public void setFinalScoreFormula(String finalScoreFormula) {
        this.finalScoreFormula = finalScoreFormula;
    }
} 