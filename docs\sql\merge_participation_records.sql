-- ===== 合并project_participation记录的解决方案 =====
-- 处理2025-06和2025-07的数据冲突问题

-- ===== 第一步：数据备份 =====
CREATE TABLE project_participation_backup_merge_20250715 AS 
SELECT * FROM project_participation WHERE month IN ('2025-06', '2025-07');

-- ===== 第二步：分析冲突数据 =====
-- 查看所有冲突记录的详细信息
SELECT 
    '冲突记录详情' as analysis_type,
    p06.user_name,
    p06.project_id,
    p06.project_name,
    p06.participation_rate as rate_2025_06,
    p07.participation_rate as rate_2025_07,
    p06.created_at as created_2025_06,
    p07.created_at as created_2025_07,
    CASE 
        WHEN p06.created_at > p07.created_at THEN '2025-06较新'
        WHEN p07.created_at > p06.created_at THEN '2025-07较新'
        ELSE '时间相同'
    END as time_comparison
FROM project_participation p06
INNER JOIN project_participation p07 ON 
    p06.user_name = p07.user_name 
    AND p06.project_id = p07.project_id
WHERE p06.month = '2025-06' 
AND p07.month = '2025-07'
ORDER BY p06.user_name, p06.project_id;

-- ===== 第三步：合并策略选择 =====
-- 策略1：保留最新创建的记录，删除旧记录
-- 策略2：保留2025-07的数据，更新为2025-06，删除原2025-06记录
-- 策略3：合并精力分配比例（求和或平均）

-- ===== 方案A：保留最新记录（推荐） =====
-- 第一步：删除冲突中较旧的记录
/*
-- 删除2025-06中较旧的记录
DELETE p06 FROM project_participation p06
INNER JOIN project_participation p07 ON 
    p06.user_name = p07.user_name 
    AND p06.project_id = p07.project_id
WHERE p06.month = '2025-06' 
AND p07.month = '2025-07'
AND p06.created_at <= p07.created_at;

-- 删除2025-07中较旧的记录
DELETE p07 FROM project_participation p07
INNER JOIN project_participation p06 ON 
    p07.user_name = p06.user_name 
    AND p07.project_id = p06.project_id
WHERE p07.month = '2025-07' 
AND p06.month = '2025-06'
AND p07.created_at < p06.created_at;

-- 将剩余的2025-07记录改为2025-06
UPDATE project_participation 
SET month = '2025-06'
WHERE month = '2025-07';
*/

-- ===== 方案B：保留2025-07数据，替换2025-06 =====
/*
-- 删除有冲突的2025-06记录
DELETE p06 FROM project_participation p06
INNER JOIN project_participation p07 ON 
    p06.user_name = p07.user_name 
    AND p06.project_id = p07.project_id
WHERE p06.month = '2025-06' 
AND p07.month = '2025-07';

-- 将所有2025-07记录改为2025-06
UPDATE project_participation 
SET month = '2025-06'
WHERE month = '2025-07';
*/

-- ===== 方案C：精力分配比例求和合并 =====
/*
-- 创建临时表存储合并后的数据
CREATE TEMPORARY TABLE merged_participation AS
SELECT 
    p06.user_name,
    p06.project_id,
    p06.project_name,
    p06.dept_id,
    p06.assigner_id,
    p06.assigner_name,
    (p06.participation_rate + p07.participation_rate) as participation_rate,
    '2025-06' as month,
    GREATEST(p06.created_at, p07.created_at) as created_at,
    NOW() as updated_at,
    CONCAT('合并自2025-06(', p06.participation_rate, ')和2025-07(', p07.participation_rate, ')') as comments
FROM project_participation p06
INNER JOIN project_participation p07 ON 
    p06.user_name = p07.user_name 
    AND p06.project_id = p07.project_id
WHERE p06.month = '2025-06' 
AND p07.month = '2025-07';

-- 删除原有冲突记录
DELETE p06 FROM project_participation p06
INNER JOIN project_participation p07 ON 
    p06.user_name = p07.user_name 
    AND p06.project_id = p07.project_id
WHERE p06.month = '2025-06' 
AND p07.month = '2025-07';

DELETE p07 FROM project_participation p07
INNER JOIN merged_participation mp ON 
    p07.user_name = mp.user_name 
    AND p07.project_id = mp.project_id
WHERE p07.month = '2025-07';

-- 插入合并后的记录
INSERT INTO project_participation 
(user_name, project_id, project_name, participation_rate, month, dept_id, assigner_id, assigner_name, comments, created_at, updated_at)
SELECT 
    user_name, project_id, project_name, participation_rate, month, dept_id, assigner_id, assigner_name, comments, created_at, updated_at
FROM merged_participation;

-- 将剩余的2025-07记录改为2025-06
UPDATE project_participation 
SET month = '2025-06'
WHERE month = '2025-07';

DROP TEMPORARY TABLE merged_participation;
*/

-- ===== 验证结果 =====
-- 执行任一方案后，运行以下查询验证
/*
SELECT 
    '最终验证' as check_type,
    month,
    COUNT(*) as count
FROM project_participation 
WHERE month IN ('2025-05', '2025-06', '2025-07', '2025-08')
GROUP BY month
ORDER BY month;

-- 检查是否还有重复记录
SELECT 
    '重复检查' as check_type,
    user_name,
    project_id,
    COUNT(*) as duplicate_count
FROM project_participation 
WHERE month = '2025-06'
GROUP BY user_name, project_id
HAVING COUNT(*) > 1;
*/

-- ===== 回滚方案 =====
/*
DELETE FROM project_participation WHERE month IN ('2025-06', '2025-07');
INSERT INTO project_participation SELECT * FROM project_participation_backup_merge_20250715;
*/
