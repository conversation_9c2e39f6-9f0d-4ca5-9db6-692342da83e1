package com.kc.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.kc.system.domain.QuotaGroup;
import com.kc.system.domain.QuotaGroupDept;
import com.kc.system.domain.QuotaGroupQuota;

/**
 * 配额组Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public interface QuotaGroupMapper 
{
    /**
     * 查询配额组
     * 
     * @param id 配额组主键
     * @return 配额组
     */
    public QuotaGroup selectQuotaGroupById(Long id);

    /**
     * 查询配额组列表
     * 
     * @param quotaGroup 配额组
     * @return 配额组集合
     */
    public List<QuotaGroup> selectQuotaGroupList(QuotaGroup quotaGroup);

    /**
     * 新增配额组
     * 
     * @param quotaGroup 配额组
     * @return 结果
     */
    public int insertQuotaGroup(QuotaGroup quotaGroup);

    /**
     * 修改配额组
     * 
     * @param quotaGroup 配额组
     * @return 结果
     */
    public int updateQuotaGroup(QuotaGroup quotaGroup);

    /**
     * 删除配额组
     * 
     * @param id 配额组主键
     * @return 结果
     */
    public int deleteQuotaGroupById(Long id);

    /**
     * 批量删除配额组
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQuotaGroupByIds(Long[] ids);

    /**
     * 根据部门ID查询配额组
     * 
     * @param deptId 部门ID
     * @return 配额组
     */
    public QuotaGroup selectQuotaGroupByDeptId(Long deptId);

    /**
     * 查询配额组部门关系列表
     * 
     * @param quotaGroupDept 配额组部门关系
     * @return 配额组部门关系集合
     */
    public List<QuotaGroupDept> selectQuotaGroupDeptList(QuotaGroupDept quotaGroupDept);

    /**
     * 新增配额组部门关系
     * 
     * @param quotaGroupDept 配额组部门关系
     * @return 结果
     */
    public int insertQuotaGroupDept(QuotaGroupDept quotaGroupDept);

    /**
     * 删除配额组部门关系
     * 
     * @param groupId 配额组ID
     * @return 结果
     */
    public int deleteQuotaGroupDeptByGroupId(Long groupId);

    /**
     * 查询配额组配额列表
     * 
     * @param quotaGroupQuota 配额组配额
     * @return 配额组配额集合
     */
    public List<QuotaGroupQuota> selectQuotaGroupQuotaList(QuotaGroupQuota quotaGroupQuota);

    /**
     * 根据配额组ID和年度查询配额
     *
     * @param groupId 配额组ID
     * @param year 年度
     * @return 配额组配额
     */
    public QuotaGroupQuota selectQuotaGroupQuotaByGroupAndYear(@Param("groupId") Long groupId, @Param("year") String year);

    /**
     * 新增配额组配额
     * 
     * @param quotaGroupQuota 配额组配额
     * @return 结果
     */
    public int insertQuotaGroupQuota(QuotaGroupQuota quotaGroupQuota);

    /**
     * 修改配额组配额
     * 
     * @param quotaGroupQuota 配额组配额
     * @return 结果
     */
    public int updateQuotaGroupQuota(QuotaGroupQuota quotaGroupQuota);

    /**
     * 删除配额组配额
     * 
     * @param groupId 配额组ID
     * @return 结果
     */
    public int deleteQuotaGroupQuotaByGroupId(Long groupId);
}
