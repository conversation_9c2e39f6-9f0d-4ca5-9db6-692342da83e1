<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评价年度" prop="evaluationYear">
        <el-select v-model="queryParams.evaluationYear" placeholder="请选择评价年度" clearable>
          <el-option label="2025" value="2025" />
          <el-option label="2024" value="2024" />
          <el-option label="2023" value="2023" />
        </el-select>
      </el-form-item>
      <el-form-item label="配额类型" prop="quotaType">
        <el-select v-model="queryParams.quotaType" placeholder="请选择配额类型" clearable>
          <el-option label="手动设置" value="MANUAL" />
          <el-option label="配额组" value="GROUP" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:quotaManagement:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:quotaManagement:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:quotaManagement:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleBatchSet"
          v-hasPermi="['system:quotaManagement:edit']"
        >批量设置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:quotaManagement:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-pie-chart"
          size="mini"
          @click="handleStatistics"
          v-hasPermi="['system:quotaManagement:statistics']"
        >配额统计</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="quotaManagementList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="部门名称" align="center" prop="deptName" />
      <el-table-column label="评价年度" align="center" prop="evaluationYear" />
      <el-table-column label="高分配额" align="center" prop="highScoreQuota">
        <template slot-scope="scope">
          <span :class="scope.row.highScoreQuota === 0 ? 'text-danger' : ''">
            {{ scope.row.highScoreQuota }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="配额类型" align="center" prop="quotaType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.quotaType === 'MANUAL'" type="primary">手动设置</el-tag>
          <el-tag v-else-if="scope.row.quotaType === 'GROUP'" type="warning">配额组</el-tag>
          <el-tag v-else type="default">{{ scope.row.quotaType }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:quotaManagement:edit']"
            :disabled="scope.row.quotaType === 'GROUP'"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['system:quotaManagement:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:quotaManagement:remove']"
            :disabled="scope.row.quotaType === 'GROUP'"
          >删除</el-button>
          <el-tooltip v-if="scope.row.quotaType === 'GROUP'" content="配额组部门请在配额组管理中操作" placement="top">
            <el-button size="mini" type="text" icon="el-icon-info" disabled>配额组</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改配额管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="部门" prop="deptId">
          <el-select
            v-model="form.deptId"
            placeholder="请选择部门"
            style="width: 100%"
            filterable
            clearable
            @change="handleDeptChange"
          >
            <el-option
              v-for="dept in deptList"
              :key="dept.deptId"
              :label="dept.deptName"
              :value="dept.deptId"
            />
          </el-select>
          <div v-if="isQuotaGroupDept" style="margin-top: 5px;">
            <el-alert
              title="该部门属于配额组，请在配额组管理中设置配额"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>
        </el-form-item>
        <el-form-item label="评价年度" prop="evaluationYear">
          <el-input v-model="form.evaluationYear" disabled style="width: 100%" />
        </el-form-item>
        <el-form-item label="高分配额" prop="highScoreQuota">
          <el-input-number
            v-model="form.highScoreQuota"
            :min="0"
            placeholder="高分配额"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 配额详情对话框 -->
    <el-dialog title="配额详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="部门名称">{{ detailForm.deptName }}</el-descriptions-item>
        <el-descriptions-item label="评价年度">{{ detailForm.evaluationYear }}</el-descriptions-item>
        <el-descriptions-item label="高分配额">{{ detailForm.highScoreQuota }}</el-descriptions-item>
        <el-descriptions-item label="配额类型">
          <el-tag v-if="detailForm.quotaType === 'MANUAL'" type="primary">手动设置</el-tag>
          <el-tag v-else-if="detailForm.quotaType === 'GROUP'" type="warning">配额组</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 批量设置对话框 -->
    <el-dialog title="批量设置配额" :visible.sync="batchOpen" width="1000px" append-to-body>
      <el-form :model="batchForm" ref="batchForm" label-width="100px">
        <el-form-item label="评价年度">
          <el-select v-model="batchForm.evaluationYear" placeholder="请选择评价年度" @change="loadBatchData">
            <el-option label="2025" value="2025" />
            <el-option label="2024" value="2024" />
            <el-option label="2023" value="2023" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <el-table :data="batchForm.quotaList" style="width: 100%" max-height="400">
        <el-table-column prop="deptName" label="部门名称" width="200" />
        <el-table-column label="高分配额" width="150">
          <template slot-scope="scope">
            <el-input-number
              v-model="scope.row.highScoreQuota"
              :min="0"
              size="mini"
            />
          </template>
        </el-table-column>
        <el-table-column prop="quotaType" label="配额类型" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.quotaType === 'MANUAL'" type="primary" size="mini">手动</el-tag>
            <el-tag v-else-if="scope.row.quotaType === 'GROUP'" type="warning" size="mini">配额组</el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitBatchForm">确 定</el-button>
        <el-button @click="batchOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 配额统计对话框 -->
    <el-dialog title="配额统计" :visible.sync="statisticsOpen" width="1200px" append-to-body>
      <el-form :model="statisticsForm" ref="statisticsForm" :inline="true" label-width="80px">
        <el-form-item label="统计年度">
          <el-select v-model="statisticsForm.year" placeholder="请选择年度" @change="loadStatistics">
            <el-option label="2025" value="2025" />
            <el-option label="2024" value="2024" />
            <el-option label="2023" value="2023" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <!-- 统计卡片 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>总部门数</span>
            </div>
            <div class="text-center">
              <span class="text-primary" style="font-size: 24px;">{{ statisticsData.totalDepts }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>总配额</span>
            </div>
            <div class="text-center">
              <span class="text-warning" style="font-size: 24px;">{{ statisticsData.totalQuota }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-table :data="statisticsData.list" style="width: 100%">
        <el-table-column prop="deptName" label="部门名称" />
        <el-table-column prop="highScoreQuota" label="高分配额" />
        <el-table-column prop="quotaType" label="配额类型">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.quotaType === 'MANUAL'" type="primary" size="mini">手动设置</el-tag>
            <el-tag v-else-if="scope.row.quotaType === 'GROUP'" type="warning" size="mini">配额组</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  listQuotaManagement,
  getQuotaManagement,
  delQuotaManagement,
  addQuotaManagement,
  updateQuotaManagement,
  batchSetQuota,
  getQuotaStatistics,
  checkDeptInQuotaGroup
} from "@/api/system/quotaManagement";
import { listDept } from "@/api/system/dept";

export default {
  name: "QuotaManagement",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 配额管理表格数据
      quotaManagementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 是否显示批量设置弹出层
      batchOpen: false,
      // 是否显示统计弹出层
      statisticsOpen: false,
      // 部门列表
      deptList: [],
      // 是否为配额组部门
      isQuotaGroupDept: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptName: null,
        evaluationYear: new Date().getFullYear().toString(),
        quotaType: null
      },
      // 表单参数
      form: {},
      // 详情表单参数
      detailForm: {},
      // 批量设置表单参数
      batchForm: {
        evaluationYear: new Date().getFullYear().toString(),
        quotaList: []
      },
      // 统计表单参数
      statisticsForm: {
        year: new Date().getFullYear().toString()
      },
      // 统计数据
      statisticsData: {
        totalDepts: 0,
        totalQuota: 0,
        list: []
      },
      // 表单校验
      rules: {
        deptId: [
          { required: true, message: "部门不能为空", trigger: "change" }
        ],
        evaluationYear: [
          { required: true, message: "评价年度不能为空", trigger: "change" }
        ],
        highScoreQuota: [
          { required: true, message: "高分配额不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDeptList();
  },
  methods: {
    /** 查询配额管理列表 */
    getList() {
      this.loading = true;
      listQuotaManagement(this.queryParams).then(response => {
        this.quotaManagementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询部门列表 */
    getDeptList() {
      listDept().then(response => {
        this.deptList = response.data;
      });
    },
    /** 部门选择变化处理 */
    handleDeptChange(deptId) {
      if (deptId) {
        // 检查部门是否属于配额组
        checkDeptInQuotaGroup(deptId).then(response => {
          this.isQuotaGroupDept = response.data;
        });
      } else {
        this.isQuotaGroupDept = false;
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deptId: null,
        evaluationYear: new Date().getFullYear().toString(),
        highScoreQuota: 0
      };
      this.isQuotaGroupDept = false;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getDeptList(); // 确保部门列表是最新的
      this.open = true;
      this.title = "添加配额管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getDeptList(); // 确保部门列表是最新的
      const id = row.id || this.ids
      getQuotaManagement(id).then(response => {
        this.form = response.data;
        // 检查部门是否属于配额组
        if (this.form.deptId) {
          this.handleDeptChange(this.form.deptId);
        }
        this.open = true;
        this.title = "修改配额管理";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const id = row.id;
      getQuotaManagement(id).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查是否为配额组部门
          if (this.isQuotaGroupDept) {
            this.$modal.msgError("该部门属于配额组，不能单独设置配额。请在配额组管理中设置配额。");
            return;
          }

          // 设置默认值
          if (this.form.id == null) {
            // 新增时设置默认值
            this.form.quotaType = "MANUAL";
          }

          if (this.form.id != null) {
            updateQuotaManagement(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQuotaManagement(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除配额管理编号为"' + ids + '"的数据项？').then(function() {
        return delQuotaManagement(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/quotaManagement/export', {
        ...this.queryParams
      }, `quotaManagement_${new Date().getTime()}.xlsx`)
    },
    /** 批量设置按钮操作 */
    handleBatchSet() {
      this.batchForm.evaluationYear = new Date().getFullYear().toString();
      this.batchOpen = true;
      this.loadBatchData();
    },
    /** 加载批量设置数据 */
    loadBatchData() {
      if (!this.batchForm.evaluationYear) return;

      const queryParams = {
        evaluationYear: this.batchForm.evaluationYear,
        pageNum: 1,
        pageSize: 1000
      };

      listQuotaManagement(queryParams).then(response => {
        this.batchForm.quotaList = response.rows;
      });
    },
    // 移除计算剩余配额方法，不再需要
    /** 提交批量设置 */
    submitBatchForm() {
      batchSetQuota(this.batchForm.quotaList).then(() => {
        this.$modal.msgSuccess("批量设置成功");
        this.batchOpen = false;
        this.getList();
      });
    },
    /** 配额统计按钮操作 */
    handleStatistics() {
      this.statisticsForm.year = new Date().getFullYear().toString();
      this.statisticsOpen = true;
      this.loadStatistics();
    },
    /** 加载统计数据 */
    loadStatistics() {
      if (!this.statisticsForm.year) return;

      getQuotaStatistics(this.statisticsForm.year).then(response => {
        this.statisticsData.list = response.data;

        // 计算统计汇总
        this.statisticsData.totalDepts = response.data.length;
        this.statisticsData.totalQuota = response.data.reduce((sum, item) => sum + item.highScoreQuota, 0);
      });
    },
    // 移除使用率计算方法，不再需要
  }
};
</script>

<style scoped>
.text-center {
  text-align: center;
}
.text-primary {
  color: #409EFF;
}
.text-success {
  color: #67C23A;
}
.text-info {
  color: #909399;
}
.text-warning {
  color: #E6A23C;
}
.text-danger {
  color: #F56C6C;
}
.mb20 {
  margin-bottom: 20px;
}
.box-card {
  margin-bottom: 20px;
}
</style>
