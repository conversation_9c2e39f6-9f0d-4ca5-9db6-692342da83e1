package com.kc.system.service.impl;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import com.kc.system.mapper.EvaluationResultMapper;
import com.kc.system.domain.EvaluationResult;
import com.kc.system.domain.EvaluationConfig;
import com.kc.system.service.IEvaluationResultService;
import com.kc.system.service.IEvaluationConfigService;
import com.kc.common.core.domain.model.LoginUser;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.system.service.ISysRoleService;
import com.kc.system.service.ISysDeptService;
import com.kc.system.service.ISysUserService;
import com.kc.common.core.domain.entity.SysDept;
import com.kc.common.exception.ServiceException;
import com.kc.system.domain.ProjectParticipation;
import com.kc.system.domain.ProjectEvaluation;
import com.kc.system.service.IProjectParticipationService;
import com.kc.system.service.IProjectEvaluationService;
import com.kc.system.domain.dto.EvaluationDetailDTO;
import com.kc.system.domain.dto.EvaluationDetailDTO.ProjectParticipationDTO;
import com.kc.system.domain.dto.EvaluationDetailDTO.ProjectLeaderScoreDTO;
import com.kc.system.domain.dto.EvaluationDetailDTO.ManagerScoreDTO;
import com.kc.system.mapper.SysUserMapper;
import com.kc.system.service.IScoreCalculationService;
import com.kc.system.dto.ScoreCalculationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;

/**
 * 评价结果Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
@Service
public class EvaluationResultServiceImpl implements IEvaluationResultService 
{
    private static final Logger log = LoggerFactory.getLogger(EvaluationResultServiceImpl.class);

    @Autowired
    private EvaluationResultMapper evaluationResultMapper;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysDeptService deptService;
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private IProjectParticipationService projectParticipationService;
    
    @Autowired
    private IProjectEvaluationService projectEvaluationService;
    
    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private IEvaluationConfigService evaluationConfigService;

    @Autowired
    private IScoreCalculationService scoreCalculationService;

    /**
     * 查询评价结果
     * 
     * @param id 评价结果主键
     * @return 评价结果
     */
    @Override
    public EvaluationResult selectEvaluationResultById(Long id)
    {
        return evaluationResultMapper.selectEvaluationResultById(id);
    }

    /**
     * 查询评价结果列表
     * 
     * @param evaluationResult 评价结果
     * @return 评价结果
     */
    @Override
    public List<EvaluationResult> selectEvaluationResultList(EvaluationResult evaluationResult)
    {
        return evaluationResultMapper.selectEvaluationResultList(evaluationResult);
    }

    /**
     * 新增评价结果
     * 
     * @param evaluationResult 评价结果
     * @return 结果
     */
    @Override
    public int insertEvaluationResult(EvaluationResult evaluationResult)
    {
        return evaluationResultMapper.insertEvaluationResult(evaluationResult);
    }

    /**
     * 修改评价结果
     * 
     * @param evaluationResult 评价结果
     * @return 结果
     */
    @Override
    public int updateEvaluationResult(EvaluationResult evaluationResult)
    {
        return evaluationResultMapper.updateEvaluationResult(evaluationResult);
    }

    /**
     * 批量删除评价结果
     * 
     * @param ids 需要删除的评价结果主键
     * @return 结果
     */
    @Override
    public int deleteEvaluationResultByIds(Long[] ids)
    {
        return evaluationResultMapper.deleteEvaluationResultByIds(ids);
    }

    /**
     * 删除评价结果信息
     * 
     * @param id 评价结果主键
     * @return 结果
     */
    @Override
    public int deleteEvaluationResultById(Long id)
    {
        return evaluationResultMapper.deleteEvaluationResultById(id);
    }
    
    /**
     * 计算指定月份的评价结果（带数据权限）
     * 
     * @param month 评价月份 格式：yyyy-MM
     * @param loginUser 当前登录用户，用于数据权限控制
     * @return 处理的记录数
     */
    @Override
    public int calculateEvaluationResultsWithDataScope(String month, LoginUser loginUser)
    {
        // 直接调用无权限限制的存储过程，在计算时不考虑数据权限
        jdbcTemplate.execute("CALL calculate_evaluation_results('" + month + "')");
        
        // 查询处理的记录数
        EvaluationResult query = new EvaluationResult();
        query.setEvaluationMonth(month);
        
        // 在查询结果时会自动应用数据权限过滤（由系统的数据权限拦截器处理）
        List<EvaluationResult> results = evaluationResultMapper.selectEvaluationResultList(query);
        return results.size();
    }

    /**
     * 计算评价结果
     * 
     * @param evaluationMonth 评价月份
     * @param userId 当前用户ID
     * @param deptIds 当前用户数据权限范围内的部门ID列表，逗号分隔
     * @return 结果
     */
    @Override
    public int calculateEvaluationResults(String evaluationMonth, Long userId, String deptIds)
    {
        try {
            // 直接使用JdbcTemplate调用存储过程
            String sql = "CALL calculate_evaluation_results_with_datascope(?, ?, ?)";
            jdbcTemplate.update(sql, 
                evaluationMonth, 
                userId, 
                deptIds);
            
            return 1;
        } catch (Exception e) {
            log.error("计算评价结果出错", e);
            throw new ServiceException("计算评价结果失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取评价结果计算明细
     * 
     * @param userId 用户ID
     * @param evaluationMonth 评价月份 格式：yyyy-MM
     * @return 评价结果计算明细
     */
    @Override
    public EvaluationDetailDTO getEvaluationDetail(Long userId, String evaluationMonth) {
        // 查询评价结果基本信息
        EvaluationResult query = new EvaluationResult();
        query.setUserId(userId);
        query.setEvaluationMonth(evaluationMonth);
        List<EvaluationResult> resultList = evaluationResultMapper.selectEvaluationResultList(query);
        
        if (resultList == null || resultList.isEmpty()) {
            throw new ServiceException("未找到指定用户的评价结果");
        }
        
        EvaluationResult evaluationResult = resultList.get(0);
        
        // 创建返回DTO
        EvaluationDetailDTO detailDTO = new EvaluationDetailDTO();
        detailDTO.setUserId(evaluationResult.getUserId());
        detailDTO.setUserName(evaluationResult.getUserName());
        detailDTO.setNickName(evaluationResult.getNickName());
        detailDTO.setDeptId(evaluationResult.getDeptId());
        detailDTO.setDeptName(evaluationResult.getDeptName());
        detailDTO.setEvaluationMonth(evaluationResult.getEvaluationMonth());
        detailDTO.setFinalScore(evaluationResult.getFinalScore());
        detailDTO.setManagerScore(evaluationResult.getManagerScore());
        detailDTO.setProjectLeaderScore(evaluationResult.getProjectLeaderScore());
        detailDTO.setUserRole(evaluationResult.getUserRole());
        
        // 获取用户通过用户名
        SysUser user = userMapper.selectUserByUserName(evaluationResult.getUserName());
        if (user == null) {
            throw new ServiceException("未找到指定用户信息");
        }
        
        // 获取项目参与情况 - 直接使用评分月份查询精力分配数据
        ProjectParticipation participationQuery = new ProjectParticipation();
        participationQuery.setUserName(evaluationResult.getUserName());
        participationQuery.setMonth(evaluationMonth);
        List<ProjectParticipation> participationList = projectParticipationService.selectProjectParticipationList(participationQuery);
        
        List<ProjectParticipationDTO> effortList = new ArrayList<>();
        if (participationList != null && !participationList.isEmpty()) {
            // 转换为DTO
            for (ProjectParticipation participation : participationList) {
                ProjectParticipationDTO dto = new ProjectParticipationDTO();
                dto.setId(participation.getId());
                dto.setProjectId(participation.getProjectId());
                dto.setProjectName(participation.getProjectName());
                
                // 设置项目简称，这里假设从项目名称中提取
                String shortName = getProjectShortName(participation.getProjectName());
                dto.setProjectShortName(shortName);
                
                dto.setParticipationRate(participation.getParticipationRate());
                dto.setComments(participation.getComments());
                effortList.add(dto);
            }
        }
        detailDTO.setEffortList(effortList);
        
        // 获取项目负责人信息，用于判断项目负责人是否为被评价人自己
        StringBuilder projectLeaderSql = new StringBuilder();
        projectLeaderSql.append("SELECT ")
                .append("    pm.project_id, ")
                .append("    su.user_id as leader_user_id ")
                .append("FROM project_members pm ")
                .append("JOIN sys_user su ON pm.user_name = su.user_name ")
                .append("WHERE pm.role = '负责人'");

        List<Map<String, Object>> projectLeaders = jdbcTemplate.queryForList(projectLeaderSql.toString());

        // 构建项目ID到负责人用户ID的映射
        Map<Long, Long> projectLeaderMap = new HashMap<>();
        for (Map<String, Object> leader : projectLeaders) {
            Long projectId = Long.valueOf(leader.get("project_id").toString());
            Long leaderUserId = Long.valueOf(leader.get("leader_user_id").toString());
            projectLeaderMap.put(projectId, leaderUserId);
        }

        // 获取项目负责人评分情况
        List<ProjectEvaluation> leaderEvaluations = getProjectLeaderEvaluations(userId, evaluationMonth);
        List<ProjectLeaderScoreDTO> leaderScoreList = new ArrayList<>();

        // 获取机构负责人评分，用于替代项目负责人自评
        List<ProjectEvaluation> managerEvaluations = getManagerEvaluations(userId, evaluationMonth);
        BigDecimal managerScore = null;
        if (managerEvaluations != null && !managerEvaluations.isEmpty()) {
            managerScore = managerEvaluations.get(0).getScore();
        }

        // 处理每个项目的精力分配，为项目负责人自评的项目添加虚拟评分记录
        if (participationList != null && !participationList.isEmpty()) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            for (ProjectParticipation participation : participationList) {
                Long projectId = participation.getProjectId();
                Long projectLeaderUserId = projectLeaderMap.get(projectId);
                boolean isProjectLeader = userId.equals(projectLeaderUserId);

                // 查找该项目是否有实际的项目负责人评分
                ProjectEvaluation actualEvaluation = null;
                if (leaderEvaluations != null) {
                    actualEvaluation = leaderEvaluations.stream()
                            .filter(eval -> eval.getProjectId().equals(projectId))
                            .findFirst()
                            .orElse(null);
                }

                if (isProjectLeader && actualEvaluation == null && managerScore != null) {
                    // 如果是项目负责人且没有实际评分，使用机构负责人评分创建虚拟记录
                    ProjectLeaderScoreDTO dto = new ProjectLeaderScoreDTO();
                    dto.setProjectId(projectId);
                    dto.setProjectName(participation.getProjectName());
                    dto.setProjectShortName(getProjectShortName(participation.getProjectName()));
                    dto.setEvaluatorId(null); // 虚拟记录，没有实际评价人
                    dto.setEvaluatorName("机构负责人评分替代");
                    dto.setScore(managerScore);
                    dto.setParticipationRate(participation.getParticipationRate());
                    dto.setCreatedAt("系统自动替代");

                    leaderScoreList.add(dto);
                } else if (actualEvaluation != null) {
                    // 如果有实际评分，添加实际评分记录
                    ProjectLeaderScoreDTO dto = new ProjectLeaderScoreDTO();
                    dto.setProjectId(actualEvaluation.getProjectId());
                    dto.setProjectName(actualEvaluation.getProjectName());
                    dto.setProjectShortName(getProjectShortName(actualEvaluation.getProjectName()));
                    dto.setEvaluatorId(actualEvaluation.getEvaluatorId());

                    // 获取评价人姓名
                    SysUser evaluator = userService.selectUserById(actualEvaluation.getEvaluatorId());
                    if (evaluator != null) {
                        dto.setEvaluatorName(evaluator.getNickName());
                    }

                    dto.setScore(actualEvaluation.getScore());
                    dto.setParticipationRate(participation.getParticipationRate());

                    // 格式化创建时间
                    if (actualEvaluation.getCreatedAt() != null) {
                        dto.setCreatedAt(dateFormat.format(actualEvaluation.getCreatedAt()));
                    }

                    leaderScoreList.add(dto);
                }
            }
        }
        detailDTO.setProjectLeaderScoreList(leaderScoreList);
        
        // 生成项目负责人评分计算公式
        String projectLeaderFormula = generateProjectLeaderScoreFormula(leaderScoreList, participationList);
        detailDTO.setProjectLeaderScoreFormula(projectLeaderFormula);
        
        // 获取机构负责人评分情况（重用之前获取的数据）
        List<ManagerScoreDTO> managerScoreList = new ArrayList<>();
        
        if (managerEvaluations != null && !managerEvaluations.isEmpty()) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            
            // 转换为DTO
            for (ProjectEvaluation evaluation : managerEvaluations) {
                ManagerScoreDTO dto = new ManagerScoreDTO();
                dto.setEvaluatorId(evaluation.getEvaluatorId());
                
                // 获取评价人姓名
                SysUser evaluator = userService.selectUserById(evaluation.getEvaluatorId());
                if (evaluator != null) {
                    dto.setEvaluatorName(evaluator.getNickName());
                }
                
                dto.setScore(evaluation.getScore());
                dto.setComments(evaluation.getComments());
                
                // 格式化创建时间
                if (evaluation.getCreatedAt() != null) {
                    dto.setCreatedAt(dateFormat.format(evaluation.getCreatedAt()));
                }
                
                managerScoreList.add(dto);
            }
        }
        detailDTO.setManagerScoreList(managerScoreList);
        
        // 生成最终得分计算公式
        String finalScoreFormula = generateFinalScoreFormula(evaluationResult);
        detailDTO.setFinalScoreFormula(finalScoreFormula);
        
        return detailDTO;
    }
    
    /**
     * 获取项目负责人评分列表
     */
    private List<ProjectEvaluation> getProjectLeaderEvaluations(Long userId, String evaluationMonth) {
        ProjectEvaluation query = new ProjectEvaluation();
        query.setEvaluateeId(userId);
        query.setEvaluationMonth(evaluationMonth);
        query.setEvaluationType("project_leader");
        return projectEvaluationService.selectProjectEvaluationList(query);
    }
    
    /**
     * 获取机构负责人评分列表
     */
    private List<ProjectEvaluation> getManagerEvaluations(Long userId, String evaluationMonth) {
        ProjectEvaluation query = new ProjectEvaluation();
        query.setEvaluateeId(userId);
        query.setEvaluationMonth(evaluationMonth);
        query.setEvaluationType("manager");
        return projectEvaluationService.selectProjectEvaluationList(query);
    }
    
    /**
     * 从项目列表中获取指定项目的精力分配比例
     */
    private BigDecimal getParticipationRate(List<ProjectParticipation> participationList, Long projectId) {
        if (participationList == null || participationList.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        for (ProjectParticipation participation : participationList) {
            if (participation.getProjectId().equals(projectId)) {
                return participation.getParticipationRate();
            }
        }
        
        return BigDecimal.ZERO;
    }
    
    /**
     * 从项目名称中提取项目简称
     */
    private String getProjectShortName(String projectName) {
        if (projectName == null || projectName.isEmpty()) {
            return "";
        }
        
        // 简单处理：如果项目名称包含小括号，取小括号中的内容作为简称
        if (projectName.contains("(") && projectName.contains(")")) {
            int start = projectName.indexOf("(") + 1;
            int end = projectName.indexOf(")");
            if (start < end) {
                return projectName.substring(start, end);
            }
        }
        
        // 否则截取前10个字符（如果有）
        return projectName.length() > 10 ? projectName.substring(0, 10) + "..." : projectName;
    }
    
    /**
     * 生成项目负责人评分计算公式
     */
    private String generateProjectLeaderScoreFormula(List<ProjectLeaderScoreDTO> leaderScoreList, 
                                                   List<ProjectParticipation> participationList) {
        if (leaderScoreList == null || leaderScoreList.isEmpty() || participationList == null || participationList.isEmpty()) {
            return "无项目负责人评分数据";
        }
        
        // 计算总精力分配
        BigDecimal totalEffort = BigDecimal.ZERO;
        for (ProjectParticipation participation : participationList) {
            totalEffort = totalEffort.add(participation.getParticipationRate());
        }
        
        if (totalEffort.compareTo(BigDecimal.ZERO) == 0) {
            return "无精力分配数据";
        }
        
        // 构建公式
        StringBuilder formula = new StringBuilder();
        formula.append("(");
        
        for (int i = 0; i < leaderScoreList.size(); i++) {
            ProjectLeaderScoreDTO dto = leaderScoreList.get(i);
            BigDecimal rate = dto.getParticipationRate();
            BigDecimal score = dto.getScore();
            
            if (i > 0) {
                formula.append(" + ");
            }
            
            formula.append(score)
                  .append(" × ")
                  .append(rate);
        }
        
        formula.append(") ÷ ")
               .append(totalEffort);
        
        return formula.toString();
    }
    
    /**
     * 生成最终得分计算公式
     */
    private String generateFinalScoreFormula(EvaluationResult result) {
        if (result == null) {
            return "";
        }
        
        String userRole = result.getUserRole();
        
        if ("project_member".equals(userRole) || "both".equals(userRole)) {
            return "机构负责人评分 × 60% + 项目负责人评分 × 40%";
        } else if ("project_leader".equals(userRole) || "none".equals(userRole)) {
            return "机构负责人评分";
        } else {
            return "未知计算公式";
        }
    }

    private void addChildDeptIds(List<SysDept> deptTree, Long parentId, List<Long> deptIds) {
        for (SysDept dept : deptTree) {
            if (dept.getParentId().equals(parentId)) {
                deptIds.add(dept.getDeptId());
                addChildDeptIds(deptTree, dept.getDeptId(), deptIds);
            }
        }
    }

    /**
     * 计算评价结果 - Java实现替代存储过程
     * 
     * @param evaluationMonth 评价月份
     * @param userId 当前用户ID
     * @param deptIds 当前用户数据权限范围内的部门ID列表，逗号分隔
     * @return 结果
     */
    @Override
    public int calculateEvaluationResultsJava(String evaluationMonth, Long userId, String deptIds) {
        log.info("开始执行评价结果计算，参数: month={}", evaluationMonth);
        
        try {
            // 输出评价周期配置信息
            EvaluationConfig managerConfig = evaluationConfigService.selectEvaluationConfigByTypeAndMonth("manager_score", "common");
            EvaluationConfig leaderConfig = evaluationConfigService.selectEvaluationConfigByTypeAndMonth("leader_score", "common");
            
            if (managerConfig != null) {
                log.info("机构负责人评分配置: enabled={}, startDate={}, endDate={}", 
                    managerConfig.getEnabled(), 
                    managerConfig.getStartDate(), 
                    managerConfig.getEndDate());
                
                // 检查当前日期是否超过结束日期
                Date currentDate = new Date();
                log.info("当前日期: {}, 是否超过机构负责人评分结束日期: {}", 
                    currentDate, 
                    currentDate.after(managerConfig.getEndDate()));
            } else {
                log.info("未找到机构负责人评分配置");
            }
            
            if (leaderConfig != null) {
                log.info("项目负责人评分配置: enabled={}, startDate={}, endDate={}", 
                    leaderConfig.getEnabled(), 
                    leaderConfig.getStartDate(), 
                    leaderConfig.getEndDate());
                
                // 检查当前日期是否超过结束日期
                Date currentDate = new Date();
                log.info("当前日期: {}, 是否超过项目负责人评分结束日期: {}", 
                    currentDate, 
                    currentDate.after(leaderConfig.getEndDate()));
            } else {
                log.info("未找到项目负责人评分配置");
            }
            
            // 清空当月评价结果数据
            log.info("清空所有当月评价结果数据");
            Map<String, Object> deleteParams = new HashMap<>();
            deleteParams.put("evaluationMonth", evaluationMonth);
            evaluationResultMapper.deleteEvaluationResultByMonth(deleteParams);
            
            // 第一步：获取所有用户并确定他们的角色
            log.info("获取用户数据并确定角色");
            List<Map<String, Object>> userRoles = getUserRolesData(deptIds);
            log.info("获取到 {} 个用户角色数据", userRoles.size());

            // 第二步：获取机构负责人评分数据
            log.info("获取机构负责人评分数据");
            Map<Long, BigDecimal> managerScores = getManagerScoresData(evaluationMonth, deptIds);
            log.info("获取到 {} 个用户的机构负责人评分数据", managerScores.size());

            // 第三步：获取项目负责人评分数据（考虑精力分配）
            log.info("获取项目负责人评分数据");
            Map<Long, Map<String, Object>> projectLeaderScores = getProjectLeaderScoresData(evaluationMonth, null);
            log.info("获取到 {} 个用户的项目负责人评分数据", projectLeaderScores.size());

            // 第四步：计算最终评分并保存结果
            log.info("计算最终评分并保存结果");
            int insertCount = calculateAndSaveFinalScores(userRoles, managerScores, projectLeaderScores, evaluationMonth);
            
            log.info("处理完成，共计算评价结果 {} 条记录", insertCount);
            return insertCount;
        } catch (Exception e) {
            log.error("计算评价结果出错", e);
            throw new ServiceException("计算评价结果失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取用户角色数据
     * 
     * @param deptIds 部门ID列表，逗号分隔，如果为null则获取所有部门
     * @return 用户角色数据列表
     */
    private List<Map<String, Object>> getUserRolesData(String deptIds) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ")
           .append("    u.user_id, ")
           .append("    u.user_name, ")
           .append("    u.nick_name, ")
           .append("    u.dept_id, ")
           .append("    d.dept_name, ")
           .append("    CASE ")
           .append("        WHEN EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role = '负责人') ")
           .append("            AND EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role != '负责人') THEN 'both' ")
           .append("        WHEN EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role = '负责人') THEN 'project_leader' ")
           .append("        WHEN EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role != '负责人') THEN 'project_member' ")
           .append("        ELSE 'none' ")
           .append("    END AS user_role ")
           .append("FROM sys_user u ")
           .append("LEFT JOIN sys_dept d ON u.dept_id = d.dept_id ")
           .append("WHERE u.del_flag = '0' AND u.status = '0' ")
           .append("AND NOT EXISTS (")
           .append("    SELECT 1 FROM sys_dept d ")
           .append("    WHERE d.leader = u.user_name ")
           .append("    AND d.status = '0' ")
           .append("    AND d.del_flag = '0'")
           .append(")");
        
        // 添加部门ID限制条件
        if (deptIds != null && !deptIds.isEmpty() && !deptIds.trim().isEmpty()) {
            sql.append(" AND u.dept_id IN (").append(deptIds).append(")");
            log.info("添加部门过滤条件: {}", deptIds);
        } else {
            log.info("忽略部门过滤条件，处理所有用户数据");
        }

        log.info("执行用户角色查询SQL: {}", sql.toString());
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql.toString());
        log.info("用户角色查询返回 {} 条记录", results.size());

        // 输出前几个用户的信息用于调试
        for (int i = 0; i < Math.min(5, results.size()); i++) {
            Map<String, Object> user = results.get(i);
            log.info("用户[{}]: userId={}, userName={}, deptId={}, role={}",
                i+1, user.get("user_id"), user.get("user_name"), user.get("dept_id"), user.get("user_role"));
        }

        return results;
    }
    
    /**
     * 获取机构负责人评分数据
     *
     * @param evaluationMonth 评价月份
     * @param deptIds 部门ID列表，逗号分隔，如果为null则获取所有部门
     * @return 用户ID到评分的映射
     */
    private Map<Long, BigDecimal> getManagerScoresData(String evaluationMonth, String deptIds) {
        log.info("获取机构负责人评分数据：评价月份={}", evaluationMonth);
        
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ")
           .append("    u.user_id, ")
           .append("    COALESCE((")
           .append("        SELECT AVG(score) FROM project_evaluation ")
           .append("        WHERE evaluatee_id = u.user_id ")
           .append("        AND evaluation_month = ? ")
           .append("        AND evaluation_type = 'manager'")
           .append("    ), 80) AS manager_score ");
        
        sql.append("FROM sys_user u ")
           .append("WHERE u.del_flag = '0' AND u.status = '0'");
        
        // 添加部门ID限制条件
        if (deptIds != null && !deptIds.isEmpty() && !deptIds.trim().isEmpty()) {
            sql.append(" AND u.dept_id IN (").append(deptIds).append(")");
        }

        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql.toString(), evaluationMonth);
        Map<Long, BigDecimal> managerScores = new HashMap<>();
        
        for (Map<String, Object> row : results) {
            Long userId = Long.valueOf(row.get("user_id").toString());
            Object scoreObj = row.get("manager_score");

            // 由于SQL中使用了COALESCE，scoreObj不应该为NULL，但为了安全起见还是检查一下
            if (scoreObj != null) {
                BigDecimal score = new BigDecimal(scoreObj.toString());
                managerScores.put(userId, score);

                // 判断是否是默认分数
                if (!existsManagerEvaluation(userId, evaluationMonth)) {
                    log.info("用户[{}]没有机构负责人评分，使用默认分数80", userId);
                } else {
                    log.info("用户[{}]有机构负责人评分={}", userId, score);
                }
            } else {
                // 如果SQL返回NULL（不应该发生），使用默认分数80
                managerScores.put(userId, new BigDecimal(80));
                log.info("用户[{}]SQL返回NULL，使用默认分数80", userId);
            }
        }
        
        return managerScores;
    }
    
    /**
     * 检查用户是否有机构负责人评分
     */
    private boolean existsManagerEvaluation(Long userId, String evaluationMonth) {
        String sql = "SELECT COUNT(1) FROM project_evaluation WHERE evaluatee_id = ? AND evaluation_month = ? AND evaluation_type = 'manager'";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, userId, evaluationMonth);
        return count != null && count > 0;
    }
    
    /**
     * 获取项目负责人评分数据
     *
     * @param evaluationMonth 评价月份
     * @param userIds 用户ID列表，如果为null则获取所有用户
     * @return 用户ID到评分信息的映射
     */
    private Map<Long, Map<String, Object>> getProjectLeaderScoresData(String evaluationMonth, List<Long> userIds) {
        log.info("=== 开始计算项目负责人评分数据 ===");
        log.info("获取项目负责人评分数据：评价月份={}, 用户数量={}", evaluationMonth, userIds != null ? userIds.size() : "全部");
        
        // 直接使用评分月份查询精力分配数据（不再需要月份调整）
        String participationQueryMonth = evaluationMonth;
        log.info("查询精力分配数据：评分月份={}, 精力分配查询月份={}", evaluationMonth, participationQueryMonth);

        // 获取用户的项目参与情况
        StringBuilder participationSql = new StringBuilder();
        participationSql.append("SELECT ")
                .append("    su.user_id, ")
                .append("    pp.project_id, ")
                .append("    pp.participation_rate ")
                .append("FROM project_participation pp ")
                .append("JOIN sys_user su ON pp.user_name = su.user_name ")
                .append("WHERE pp.month = ?");

        // 添加用户ID限制条件
        if (userIds != null && !userIds.isEmpty()) {
            String userIdStr = userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            participationSql.append(" AND su.user_id IN (").append(userIdStr).append(")");
        }

        List<Map<String, Object>> participations = jdbcTemplate.queryForList(participationSql.toString(), participationQueryMonth);
        
        // 获取项目负责人评分
        StringBuilder evaluationSql = new StringBuilder();
        evaluationSql.append("SELECT ")
                .append("    pe.evaluatee_id, ")
                .append("    pe.project_id, ")
                .append("    pe.score ")
                .append("FROM project_evaluation pe ")
                .append("JOIN sys_user su ON pe.evaluatee_id = su.user_id ")
                .append("WHERE pe.evaluation_month = ? ")
                .append("AND pe.evaluation_type = 'project_leader'");

        // 添加用户ID限制条件
        if (userIds != null && !userIds.isEmpty()) {
            String userIdStr = userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            evaluationSql.append(" AND su.user_id IN (").append(userIdStr).append(")");
        }

        List<Map<String, Object>> evaluations = jdbcTemplate.queryForList(evaluationSql.toString(), evaluationMonth);

        // 获取项目负责人信息，用于判断项目负责人是否为被评价人自己
        StringBuilder projectLeaderSql = new StringBuilder();
        projectLeaderSql.append("SELECT ")
                .append("    pm.project_id, ")
                .append("    su.user_id as leader_user_id ")
                .append("FROM project_members pm ")
                .append("JOIN sys_user su ON pm.user_name = su.user_name ")
                .append("WHERE pm.role = '负责人'");

        List<Map<String, Object>> projectLeaders = jdbcTemplate.queryForList(projectLeaderSql.toString());

        // 构建项目ID到负责人用户ID的映射
        Map<Long, Long> projectLeaderMap = new HashMap<>();
        for (Map<String, Object> leader : projectLeaders) {
            Long projectId = Long.valueOf(leader.get("project_id").toString());
            Long leaderUserId = Long.valueOf(leader.get("leader_user_id").toString());
            projectLeaderMap.put(projectId, leaderUserId);
        }

        // 获取机构负责人评分，用于替代项目负责人自评
        Map<Long, BigDecimal> managerScores = getManagerScoresByUserIds(userIds, evaluationMonth);
        
        // 整合数据
        Map<Long, Map<String, Object>> projectLeaderScores = new HashMap<>();
        Map<Long, List<Map<String, Object>>> userParticipations = new HashMap<>();
        Map<String, List<Map<String, Object>>> userProjectEvaluations = new HashMap<>();
        
        // 组织参与数据
        for (Map<String, Object> participation : participations) {
            Long userId = Long.valueOf(participation.get("user_id").toString());
            
            if (!userParticipations.containsKey(userId)) {
                userParticipations.put(userId, new ArrayList<>());
            }
            
            userParticipations.get(userId).add(participation);
        }
        
        // 组织评分数据
        for (Map<String, Object> evaluation : evaluations) {
            Long evaluateeId = Long.valueOf(evaluation.get("evaluatee_id").toString());
            Long projectId = Long.valueOf(evaluation.get("project_id").toString());
            String key = evaluateeId + "_" + projectId;
            
            if (!userProjectEvaluations.containsKey(key)) {
                userProjectEvaluations.put(key, new ArrayList<>());
            }
            
            userProjectEvaluations.get(key).add(evaluation);
        }
        
        // 注意：只为有项目参与的用户计算项目负责人评分
        // 没有项目参与的用户在最终计算时会使用0分

        // 计算每个用户的项目负责人评分
        for (Long userId : userParticipations.keySet()) {
            List<Map<String, Object>> userParts = userParticipations.get(userId);
            BigDecimal weightedScoreSum = BigDecimal.ZERO;
            BigDecimal participationRateSum = BigDecimal.ZERO;

            boolean hasEvaluations = false;
            int projectCount = 0;
            int evaluatedProjectCount = 0;

            log.info("=== 开始计算用户[{}]的项目负责人评分 ===", userId);
            
            for (Map<String, Object> participation : userParts) {
                Long projectId = Long.valueOf(participation.get("project_id").toString());
                BigDecimal participationRate = new BigDecimal(participation.get("participation_rate").toString());
                String key = userId + "_" + projectId;

                log.info("处理用户[{}]的项目[{}]，精力分配={}", userId, projectId, participationRate);

                // 累加总精力分配比例
                participationRateSum = participationRateSum.add(participationRate);
                projectCount++;

                // 检查当前用户是否为该项目的负责人
                Long projectLeaderUserId = projectLeaderMap.get(projectId);
                boolean isProjectLeader = userId.equals(projectLeaderUserId);

                log.info("项目[{}]的负责人用户ID={}, 当前用户[{}]是否为负责人={}", projectId, projectLeaderUserId, userId, isProjectLeader);

                if (isProjectLeader) {
                    // 如果当前用户是该项目的负责人，使用机构负责人评分替代
                    // 注意：项目负责人自评替代不受useDefaultScore限制，这是评分替代机制而非默认评分
                    BigDecimal managerScore = managerScores.get(userId);
                    log.info("用户[{}]是项目[{}]的负责人，机构负责人评分={}", userId, projectId, managerScore);

                    if (managerScore != null) {
                        hasEvaluations = true;
                        evaluatedProjectCount++;
                        BigDecimal weightedScore = managerScore.multiply(participationRate);
                        weightedScoreSum = weightedScoreSum.add(weightedScore);
                        log.info("用户[{}]项目[{}]：使用机构负责人评分{}，精力分配{}，加权得分={}",
                                userId, projectId, managerScore, participationRate, weightedScore);
                    } else {
                        // 如果没有机构负责人评分，使用默认分数80（项目负责人自评替代机制，不受useDefaultScore限制）
                        hasEvaluations = true;
                        evaluatedProjectCount++;
                        BigDecimal defaultScore = new BigDecimal(80);
                        BigDecimal weightedScore = defaultScore.multiply(participationRate);
                        weightedScoreSum = weightedScoreSum.add(weightedScore);
                        log.info("用户[{}]项目[{}]：没有机构负责人评分，使用默认分数80，精力分配{}，加权得分={}",
                                userId, projectId, participationRate, weightedScore);
                    }
                } else {
                    // 如果不是项目负责人，按原逻辑处理项目负责人评分
                    log.info("用户[{}]不是项目[{}]的负责人，查找实际项目负责人评分", userId, projectId);

                    if (userProjectEvaluations.containsKey(key)) {
                        List<Map<String, Object>> projectEvals = userProjectEvaluations.get(key);
                        log.info("用户[{}]项目[{}]找到{}条项目负责人评分记录", userId, projectId, projectEvals.size());

                        if (!projectEvals.isEmpty()) {
                            hasEvaluations = true;
                            evaluatedProjectCount++;

                            // 计算项目平均得分
                            BigDecimal projectScoreSum = BigDecimal.ZERO;
                            for (Map<String, Object> eval : projectEvals) {
                                BigDecimal score = new BigDecimal(eval.get("score").toString());
                                projectScoreSum = projectScoreSum.add(score);
                                log.info("用户[{}]项目[{}]评分记录：{}", userId, projectId, score);
                            }
                            BigDecimal projectAvgScore = projectScoreSum.divide(new BigDecimal(projectEvals.size()), 2, RoundingMode.HALF_UP);
                            BigDecimal weightedScore = projectAvgScore.multiply(participationRate);

                            // 累加加权得分
                            weightedScoreSum = weightedScoreSum.add(weightedScore);
                            log.info("用户[{}]项目[{}]：平均评分{}，精力分配{}，加权得分={}",
                                    userId, projectId, projectAvgScore, participationRate, weightedScore);
                        } else {
                            // 如果没有评分，使用默认分数80
                            evaluatedProjectCount++;
                            BigDecimal defaultScore = new BigDecimal(80);
                            BigDecimal weightedScore = defaultScore.multiply(participationRate);
                            weightedScoreSum = weightedScoreSum.add(weightedScore);
                            log.info("用户[{}]项目[{}]没有项目负责人评分，使用默认分数80，加权得分={}", userId, projectId, weightedScore);
                        }
                    } else {
                        // 如果没有评分记录，使用默认分数80
                        evaluatedProjectCount++;
                        BigDecimal defaultScore = new BigDecimal(80);
                        BigDecimal weightedScore = defaultScore.multiply(participationRate);
                        weightedScoreSum = weightedScoreSum.add(weightedScore);
                        log.info("用户[{}]项目[{}]没有评分记录，使用默认分数80，加权得分={}", userId, projectId, weightedScore);
                    }
                }
            }
            
            // 计算最终加权平均分
            BigDecimal finalScore = null;
            boolean isDefaultScore = false;

            log.info("用户[{}]计算汇总：总精力分配={}, 加权得分总和={}, 项目总数={}, 有评分项目数={}",
                    userId, participationRateSum, weightedScoreSum, projectCount, evaluatedProjectCount);

            if (participationRateSum.compareTo(BigDecimal.ZERO) > 0) {
                if (evaluatedProjectCount > 0) {
                    // 如果有评分（实际评分或默认评分），则计算加权平均分
                    finalScore = weightedScoreSum.divide(participationRateSum, 2, RoundingMode.HALF_UP);
                    isDefaultScore = evaluatedProjectCount > (hasEvaluations ? 1 : 0);

                    log.info("用户[{}]项目负责人评分计算：{} / {} = {}", userId, weightedScoreSum, participationRateSum, finalScore);

                    if (hasEvaluations) {
                        if (evaluatedProjectCount == projectCount) {
                            log.info("用户[{}]所有项目都有评分，计算加权平均分={}", userId, finalScore);
                        } else {
                            log.info("用户[{}]部分项目使用默认评分，计算加权平均分={}", userId, finalScore);
                        }
                    } else {
                        log.info("用户[{}]所有项目都使用默认评分80，计算加权平均分={}", userId, finalScore);
                    }
                } else {
                    // 如果没有评分且不使用默认评分，则使用0分
                    finalScore = BigDecimal.ZERO;
                    isDefaultScore = false;
                    log.info("用户[{}]没有项目负责人评分且不使用默认评分，使用0分", userId);
                }
            } else {
                // 如果没有精力分配，则使用0分
                finalScore = BigDecimal.ZERO;
                isDefaultScore = false;
                log.info("用户[{}]没有精力分配，使用0分", userId);
            }
            
            // 保存结果
            Map<String, Object> scoreInfo = new HashMap<>();
            scoreInfo.put("project_leader_score", finalScore);  // 修正键名
            scoreInfo.put("hasParticipation", !userParts.isEmpty());
            scoreInfo.put("hasEvaluations", hasEvaluations);
            scoreInfo.put("isDefaultScore", isDefaultScore);

            projectLeaderScores.put(userId, scoreInfo);
        }
        
        return projectLeaderScores;
    }
    
    /**
     * 计算最终评分并保存结果
     * 
     * @param userRoles 用户角色数据
     * @param managerScores 机构负责人评分数据
     * @param projectLeaderScores 项目负责人评分数据
     * @param evaluationMonth 评价月份
     * @return 插入的记录数
     */
    private int calculateAndSaveFinalScores(
            List<Map<String, Object>> userRoles,
            Map<Long, BigDecimal> managerScores,
            Map<Long, Map<String, Object>> projectLeaderScores,
            String evaluationMonth) {
        
        int insertCount = 0;
        
        for (Map<String, Object> userRole : userRoles) {
            Long userId = Long.valueOf(userRole.get("user_id").toString());
            String userName = userRole.get("user_name").toString();
            String nickName = userRole.containsKey("nick_name") && userRole.get("nick_name") != null ? 
                              userRole.get("nick_name").toString() : userName;
            Long deptId = userRole.containsKey("dept_id") && userRole.get("dept_id") != null ? 
                         Long.valueOf(userRole.get("dept_id").toString()) : null;
            String deptName = userRole.containsKey("dept_name") && userRole.get("dept_name") != null ? 
                             userRole.get("dept_name").toString() : "";
            String role = userRole.get("user_role").toString();
            
            // 获取机构负责人评分
            BigDecimal managerScore = null;

            if (managerScores.containsKey(userId)) {
                managerScore = managerScores.get(userId);
            }

            // 获取项目负责人评分
            BigDecimal projectLeaderScore = null;
            boolean isDefaultProjectLeaderScore = false;

            if (projectLeaderScores.containsKey(userId)) {
                Map<String, Object> scoreInfo = projectLeaderScores.get(userId);
                projectLeaderScore = new BigDecimal(scoreInfo.get("score").toString());
                isDefaultProjectLeaderScore = Boolean.valueOf(scoreInfo.get("isDefaultScore").toString());

                if (isDefaultProjectLeaderScore) {
                    log.info("用户[{}]使用默认项目负责人评分80", userName);
                }
            } else {
                // 如果没有项目负责人评分，设置为0分
                projectLeaderScore = BigDecimal.ZERO;
                log.info("用户[{}]没有项目负责人评分记录，设置为0分", userName);
            }
            
            // 如果没有机构负责人评分，则跳过此用户
            if (managerScore == null) {
                log.info("用户[{}]没有机构负责人评分，跳过计算", userName);
                continue;
            }
            
            // 计算最终得分 - 使用新的策略模式
            BigDecimal finalScore;

            try {
                // 使用策略模式计算评分
                ScoreCalculationResult calculationResult = scoreCalculationService.calculateUserScore(userId, evaluationMonth);
                finalScore = calculationResult.getFinalScore();

                log.info("用户[{}]使用策略[{}]计算最终得分={}, 计算详情: {}",
                        userName, calculationResult.getStrategyType(), finalScore, calculationResult.getCalculationDetails());

                // 如果策略计算失败或返回0分，回退到原有逻辑
                if (finalScore == null || finalScore.compareTo(BigDecimal.ZERO) == 0) {
                    log.warn("用户[{}]策略计算返回0分或null，回退到原有计算逻辑", userName);
                    finalScore = calculateFinalScoreByRole(role, managerScore, projectLeaderScore, userName);
                }
            } catch (Exception e) {
                log.error("用户[{}]策略计算失败，回退到原有计算逻辑: {}", userName, e.getMessage());
                finalScore = calculateFinalScoreByRole(role, managerScore, projectLeaderScore, userName);
            }
            
            // 创建并保存评价结果记录
            EvaluationResult result = new EvaluationResult();
            result.setUserId(userId);
            result.setUserName(userName);
            result.setNickName(nickName);
            result.setDeptId(deptId);
            result.setDeptName(deptName);
            result.setEvaluationMonth(evaluationMonth);
            result.setFinalScore(finalScore);
            result.setManagerScore(managerScore);
            result.setProjectLeaderScore(projectLeaderScore);
            result.setUserRole(role);
            
            evaluationResultMapper.insertEvaluationResult(result);
            insertCount++;
        }
        
        return insertCount;
    }

    /**
     * 根据角色计算最终得分（回退逻辑）
     */
    private BigDecimal calculateFinalScoreByRole(String role, BigDecimal managerScore, BigDecimal projectLeaderScore, String userName) {
        BigDecimal finalScore;

        if ("project_leader".equals(role) || "none".equals(role)) {
            // 仅项目负责人或不参与项目：评价得分=机构负责人评分
            finalScore = managerScore;
            log.info("用户[{}]角色为[{}]，最终得分=机构负责人评分={}", userName, role, finalScore);
        } else {
            // 既为项目负责人又为项目成员或仅项目成员：
            // 但是如果项目负责人评分为0或null，说明该用户虽然是项目工作人员但没有项目参与度分配
            // 这种情况下应该直接使用机构负责人评分
            if (projectLeaderScore == null || projectLeaderScore.compareTo(BigDecimal.ZERO) == 0) {
                finalScore = managerScore;
                log.info("用户[{}]角色为[{}]，但项目负责人评分为0，最终得分=机构负责人评分={}", userName, role, finalScore);
            } else {
                // 评价得分=机构负责人评价得分*60%+项目负责人评价平均分*40%
                BigDecimal managerWeight = new BigDecimal("0.6");
                BigDecimal leaderWeight = new BigDecimal("0.4");

                // 两种评分都有
                finalScore = managerScore.multiply(managerWeight)
                            .add(projectLeaderScore.multiply(leaderWeight))
                            .setScale(2, RoundingMode.HALF_UP);
                log.info("用户[{}]角色为[{}]，最终得分=机构负责人评分{}×60%+项目负责人评分{}×40%={}",
                        userName, role, managerScore, projectLeaderScore, finalScore);
            }
        }

        return finalScore;
    }

    /**
     * 计算指定用户列表的评价结果并插入到evaluation_result表
     *
     * @param userIds 用户ID列表
     * @param evaluationMonth 评价月份
     * @return 结果（处理的记录数）
     */
    @Override
    @Transactional
    public int calculateAndInsertEvaluationResults(List<Long> userIds, String evaluationMonth) {
        if (userIds == null || userIds.isEmpty()) {
            log.warn("用户ID列表为空，跳过计算");
            return 0;
        }

        log.info("开始计算指定用户的评价结果，用户数量: {}, 评价月份: {}, 用户ID列表: {}", userIds.size(), evaluationMonth, userIds);

        int insertCount = 0;

        try {
            // 删除这些用户当月的评价结果
            for (Long userId : userIds) {
                Map<String, Object> deleteParams = new HashMap<>();
                deleteParams.put("userId", userId);
                deleteParams.put("evaluationMonth", evaluationMonth);
                evaluationResultMapper.deleteEvaluationResultByUserAndMonth(deleteParams);
            }

            // 获取用户基本信息和角色
            List<Map<String, Object>> userRoles = getUserRolesByUserIds(userIds);
            log.info("获取到 {} 个用户角色数据", userRoles.size());

            // 调试：打印用户角色数据
            for (Map<String, Object> userRole : userRoles) {
                log.debug("用户角色数据: {}", userRole);
            }

            // 获取机构负责人评分数据
            Map<Long, BigDecimal> managerScores = getManagerScoresByUserIds(userIds, evaluationMonth);
            log.info("获取到 {} 个用户的机构负责人评分数据: {}", managerScores.size(), managerScores);

            // 获取项目负责人评分数据 - 使用详细计算逻辑（包含项目负责人自评替代）
            Map<Long, Map<String, Object>> projectLeaderScores = getProjectLeaderScoresData(evaluationMonth, userIds);
            log.info("获取到 {} 个用户的项目负责人评分数据: {}", projectLeaderScores.size(), projectLeaderScores);

            // 计算并保存每个用户的评价结果
            for (Map<String, Object> userRole : userRoles) {
                try {
                    log.debug("处理用户角色数据: {}", userRole);

                    Long userId = null;
                    if (userRole.get("user_id") != null) {
                        Object userIdObj = userRole.get("user_id");
                        log.debug("用户ID对象类型: {}, 值: {}", userIdObj.getClass().getName(), userIdObj);
                        if (userIdObj instanceof Number) {
                            userId = ((Number) userIdObj).longValue();
                        } else {
                            log.warn("用户ID不是Number类型: {}", userIdObj.getClass().getName());
                            continue;
                        }
                    } else {
                        log.warn("用户ID为null，跳过该用户");
                        continue;
                    }

                    String userName = (String) userRole.get("user_name");
                    String nickName = (String) userRole.get("nick_name");

                    Long deptId = null;
                    if (userRole.get("dept_id") != null) {
                        Object deptIdObj = userRole.get("dept_id");
                        if (deptIdObj instanceof Number) {
                            deptId = ((Number) deptIdObj).longValue();
                        }
                    }

                    String deptName = (String) userRole.get("dept_name");
                    String role = (String) userRole.get("user_role");

                    log.debug("解析用户信息: userId={}, userName={}, nickName={}, deptId={}, role={}",
                            userId, userName, nickName, deptId, role);

                    // 获取机构负责人评分
                    BigDecimal managerScore = managerScores.getOrDefault(userId, BigDecimal.ZERO);
                    log.info("用户[{}] (ID: {}) 机构负责人评分: {}", userName, userId, managerScore);

                    // 获取项目负责人评分
                    Map<String, Object> projectScoreData = projectLeaderScores.get(userId);
                    BigDecimal projectLeaderScore = BigDecimal.ZERO;
                    if (projectScoreData != null && projectScoreData.get("project_leader_score") != null) {
                        Object scoreObj = projectScoreData.get("project_leader_score");
                        if (scoreObj instanceof BigDecimal) {
                            projectLeaderScore = (BigDecimal) scoreObj;
                        } else if (scoreObj instanceof Number) {
                            projectLeaderScore = new BigDecimal(scoreObj.toString());
                        }
                    }
                    log.info("用户[{}] (ID: {}) 项目负责人评分: {}", userName, userId, projectLeaderScore);

                    // 计算最终得分
                    log.info("用户[{}] (ID: {}) 开始计算最终得分，角色: {}, 机构负责人评分: {}, 项目负责人评分: {}",
                            userName, userId, role, managerScore, projectLeaderScore);
                    BigDecimal finalScore = calculateFinalScoreByRole(role, managerScore, projectLeaderScore, userName);

                    // 创建并保存评价结果记录
                    EvaluationResult result = new EvaluationResult();
                    result.setUserId(userId);
                    result.setUserName(userName);
                    result.setNickName(nickName);
                    result.setDeptId(deptId);
                    result.setDeptName(deptName);
                    result.setEvaluationMonth(evaluationMonth);
                    result.setFinalScore(finalScore);
                    result.setManagerScore(managerScore);
                    result.setProjectLeaderScore(projectLeaderScore);
                    result.setUserRole(role);

                    evaluationResultMapper.insertEvaluationResult(result);
                    insertCount++;

                    log.info("用户[{}]评价结果计算完成: 最终得分={}, 机构负责人评分={}, 项目负责人评分={}, 角色={}",
                            userName, finalScore, managerScore, projectLeaderScore, role);
                } catch (Exception e) {
                    log.error("处理用户[{}]时发生错误: {}", userRole.get("user_name"), e.getMessage(), e);
                    // 继续处理下一个用户，不中断整个流程
                }
            }

            log.info("指定用户评价结果计算完成，共处理 {} 条记录", insertCount);
            return insertCount;

        } catch (Exception e) {
            log.error("计算指定用户评价结果失败", e);
            throw new ServiceException("计算评价结果失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户ID列表获取用户角色信息
     */
    private List<Map<String, Object>> getUserRolesByUserIds(List<Long> userIds) {
        String userIdStr = userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        return evaluationResultMapper.getUserRolesByUserIds(userIdStr);
    }

    /**
     * 根据用户ID列表获取机构负责人评分
     */
    private Map<Long, BigDecimal> getManagerScoresByUserIds(List<Long> userIds, String evaluationMonth) {
        String userIdStr = userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        log.debug("查询机构负责人评分，用户ID字符串: {}, 评价月份: {}", userIdStr, evaluationMonth);
        List<Map<String, Object>> scores = evaluationResultMapper.getManagerScoresByUserIds(userIdStr, evaluationMonth);
        log.debug("机构负责人评分查询结果: {}", scores);

        Map<Long, BigDecimal> result = new HashMap<>();
        Map<Long, String> userEvaluationTypes = new HashMap<>(); // 记录每个用户的评价类型

        for (Map<String, Object> score : scores) {
            Long userId = null;
            if (score.get("user_id") != null) {
                Object userIdObj = score.get("user_id");
                if (userIdObj instanceof Number) {
                    userId = ((Number) userIdObj).longValue();
                }
            }

            BigDecimal managerScore = null;
            if (score.get("manager_score") != null) {
                Object scoreObj = score.get("manager_score");
                if (scoreObj instanceof BigDecimal) {
                    managerScore = (BigDecimal) scoreObj;
                } else if (scoreObj instanceof Number) {
                    managerScore = new BigDecimal(scoreObj.toString());
                }
            }

            String evaluationType = (String) score.get("evaluation_type");

            if (userId != null) {
                // 如果用户还没有评分记录，或者当前评价类型优先级更高，则更新
                if (!result.containsKey(userId) ||
                    ("manager".equals(evaluationType) && "parent_manager".equals(userEvaluationTypes.get(userId)))) {
                    result.put(userId, managerScore != null ? managerScore : BigDecimal.ZERO);
                    userEvaluationTypes.put(userId, evaluationType);
                    log.debug("用户{}使用{}类型的评分: {}", userId, evaluationType, managerScore);
                }
            }
        }
        return result;
    }

    /**
     * 根据用户ID列表获取项目负责人评分
     */
    private Map<Long, Map<String, Object>> getProjectLeaderScoresByUserIds(List<Long> userIds, String evaluationMonth) {
        String userIdStr = userIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        List<Map<String, Object>> scores = evaluationResultMapper.getProjectLeaderScoresByUserIds(userIdStr, evaluationMonth);

        Map<Long, Map<String, Object>> result = new HashMap<>();
        for (Map<String, Object> score : scores) {
            Long userId = null;
            if (score.get("user_id") != null) {
                Object userIdObj = score.get("user_id");
                if (userIdObj instanceof Number) {
                    userId = ((Number) userIdObj).longValue();
                }
            }

            if (userId != null) {
                result.put(userId, score);
            }
        }
        return result;
    }


}
