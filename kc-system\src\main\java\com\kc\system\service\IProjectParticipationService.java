package com.kc.system.service;

import java.util.List;
import java.util.Map;
import com.kc.system.domain.ProjectParticipation;

/**
 * 项目参与度分配Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IProjectParticipationService 
{
    /**
     * 查询项目参与度分配
     * 
     * @param id 项目参与度分配主键
     * @return 项目参与度分配
     */
    public ProjectParticipation selectProjectParticipationById(Long id);

    /**
     * 查询项目参与度分配列表
     * 
     * @param projectParticipation 项目参与度分配
     * @return 项目参与度分配集合
     */
    public List<ProjectParticipation> selectProjectParticipationList(ProjectParticipation projectParticipation);

    /**
     * 新增项目参与度分配
     * 
     * @param projectParticipation 项目参与度分配
     * @return 结果
     */
    public int insertProjectParticipation(ProjectParticipation projectParticipation);

    /**
     * 修改项目参与度分配
     * 
     * @param projectParticipation 项目参与度分配
     * @return 结果
     */
    public int updateProjectParticipation(ProjectParticipation projectParticipation);

    /**
     * 批量删除项目参与度分配
     * 
     * @param ids 需要删除的项目参与度分配主键集合
     * @return 结果
     */
    public int deleteProjectParticipationByIds(Long[] ids);

    /**
     * 删除项目参与度分配信息
     *
     * @param id 项目参与度分配主键
     * @return 结果
     */
    public int deleteProjectParticipationById(Long id);

    /**
     * 查询项目参与度分配列表（关联用户和部门信息）
     *
     * @param projectParticipation 项目参与度分配
     * @return 项目参与度分配集合（包含用户和部门信息）
     */
    public List<Map<String, Object>> selectProjectParticipationWithUserInfo(ProjectParticipation projectParticipation);
}
