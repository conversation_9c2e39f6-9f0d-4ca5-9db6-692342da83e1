package com.kc.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 配额组配额对象 quota_group_quota
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public class QuotaGroupQuota extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 配额组ID */
    @Excel(name = "配额组ID")
    private Long groupId;

    /** 评价年度 */
    @Excel(name = "评价年度")
    private String evaluationYear;

    /** 组内总人数 */
    @Excel(name = "组内总人数")
    private Integer totalEmployees;

    /** 高分配额 */
    @Excel(name = "高分配额")
    private Integer highScoreQuota;

    /** 已使用配额 */
    @Excel(name = "已使用配额")
    private Integer usedQuota;

    /** 剩余配额 */
    @Excel(name = "剩余配额")
    private Integer remainingQuota;

    /** 配额组名称（关联查询字段） */
    private String groupName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setGroupId(Long groupId) 
    {
        this.groupId = groupId;
    }

    public Long getGroupId() 
    {
        return groupId;
    }

    public void setEvaluationYear(String evaluationYear) 
    {
        this.evaluationYear = evaluationYear;
    }

    public String getEvaluationYear() 
    {
        return evaluationYear;
    }

    public void setTotalEmployees(Integer totalEmployees) 
    {
        this.totalEmployees = totalEmployees;
    }

    public Integer getTotalEmployees() 
    {
        return totalEmployees;
    }

    public void setHighScoreQuota(Integer highScoreQuota) 
    {
        this.highScoreQuota = highScoreQuota;
    }

    public Integer getHighScoreQuota() 
    {
        return highScoreQuota;
    }

    public void setUsedQuota(Integer usedQuota) 
    {
        this.usedQuota = usedQuota;
    }

    public Integer getUsedQuota() 
    {
        return usedQuota;
    }

    public void setRemainingQuota(Integer remainingQuota) 
    {
        this.remainingQuota = remainingQuota;
    }

    public Integer getRemainingQuota() 
    {
        return remainingQuota;
    }

    public String getGroupName() 
    {
        return groupName;
    }

    public void setGroupName(String groupName) 
    {
        this.groupName = groupName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("groupId", getGroupId())
            .append("evaluationYear", getEvaluationYear())
            .append("totalEmployees", getTotalEmployees())
            .append("highScoreQuota", getHighScoreQuota())
            .append("usedQuota", getUsedQuota())
            .append("remainingQuota", getRemainingQuota())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
