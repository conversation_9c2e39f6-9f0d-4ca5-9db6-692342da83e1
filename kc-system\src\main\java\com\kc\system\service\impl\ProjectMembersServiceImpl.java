package com.kc.system.service.impl;

import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kc.system.mapper.ProjectMembersMapper;
import com.kc.system.domain.ProjectMembers;
import com.kc.system.service.IProjectMembersService;
import com.kc.system.mapper.ProjectWorkloadMapper;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目成员关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-14
 */
@Service
public class ProjectMembersServiceImpl implements IProjectMembersService 
{
    @Autowired
    private ProjectMembersMapper projectMembersMapper;

    @Autowired
    private ProjectWorkloadMapper projectWorkloadMapper;

    /**
     * 查询项目成员关联
     * 
     * @param id 项目成员关联主键
     * @return 项目成员关联
     */
    @Override
    public ProjectMembers selectProjectMembersById(Long id)
    {
        return projectMembersMapper.selectProjectMembersById(id);
    }

    /**
     * 查询项目成员关联列表
     * 
     * @param projectMembers 项目成员关联
     * @return 项目成员关联
     */
    @Override
    public List<ProjectMembers> selectProjectMembersList(ProjectMembers projectMembers)
    {
        return projectMembersMapper.selectProjectMembersList(projectMembers);
    }

    /**
     * 新增项目成员关联
     * 
     * @param projectMembers 项目成员关联
     * @return 结果
     */
    @Override
    public int insertProjectMembers(ProjectMembers projectMembers)
    {
        return projectMembersMapper.insertProjectMembers(projectMembers);
    }

    /**
     * 修改项目成员关联
     * 
     * @param projectMembers 项目成员关联
     * @return 结果
     */
    @Override
    public int updateProjectMembers(ProjectMembers projectMembers)
    {
        return projectMembersMapper.updateProjectMembers(projectMembers);
    }

    /**
     * 删除项目成员关联信息
     * 
     * @param id 项目成员关联主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteProjectMembersById(Long id)
    {
        // 先查询要删除的成员信息
        ProjectMembers member = projectMembersMapper.selectProjectMembersById(id);
        if (member != null) {
            // 删除工时记录
            projectWorkloadMapper.deleteProjectWorkloadByProjectIdAndUserName(
                member.getProjectId(), 
                member.getUserName()
            );
        }
        // 删除成员关联
        return projectMembersMapper.deleteProjectMembersById(id);
    }

    /**
     * 批量删除项目成员关联
     * 
     * @param ids 需要删除的项目成员关联主键集合
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteProjectMembersByIds(Long[] ids)
    {
        // 先删除每个成员的工时记录
        for (Long id : ids) {
            ProjectMembers member = projectMembersMapper.selectProjectMembersById(id);
            if (member != null) {
                // 删除工时记录
                projectWorkloadMapper.deleteProjectWorkloadByProjectIdAndUserName(
                    member.getProjectId(), 
                    member.getUserName()
                );
            }
        }
        // 删除成员关联
        return projectMembersMapper.deleteProjectMembersByIds(ids);
    }

    @Override
    public ProjectMembers selectProjectMemberByUserName(Long projectId, String userName) {
        return projectMembersMapper.selectProjectMemberByUserName(projectId, userName);
    }
    
    /**
     * 获取用户参与的所有项目
     * 
     * @param userName 用户名
     * @return 项目列表
     */
    @Override
    public List<Map<String, Object>> selectUserProjects(String userName) {
        return projectMembersMapper.selectUserProjects(userName);
    }

    @Override
    public List<Map<String, Object>> selectDeptMembersWithProjects(List<Long> deptIds) {
        return projectMembersMapper.selectDeptMembersWithProjects(deptIds);
    }
}
