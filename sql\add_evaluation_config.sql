-- 添加评分功能开关配置（如果不存在的话）
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
SELECT '评分功能开关', 'evaluation.enabled', 'true', 'Y', 'admin', NOW(), '控制整个考核评分功能的开启和关闭，true为开启，false为关闭'
WHERE NOT EXISTS (SELECT 1 FROM sys_config WHERE config_key = 'evaluation.enabled');

-- 如果配置已存在但类型不对，更新类型
UPDATE sys_config SET config_type = 'Y' WHERE config_key = 'evaluation.enabled' AND config_type != 'Y';
