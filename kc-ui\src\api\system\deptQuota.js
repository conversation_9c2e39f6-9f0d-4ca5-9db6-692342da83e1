import request from '@/utils/request'

// 查询部门高分配额列表
export function listDeptQuota(query) {
  return request({
    url: '/system/deptQuota/list',
    method: 'get',
    params: query
  })
}

// 查询部门高分配额详细
export function getDeptQuota(id) {
  return request({
    url: '/system/deptQuota/' + id,
    method: 'get'
  })
}

// 获取部门配额信息
export function getDeptQuotaInfo(deptId, year) {
  return request({
    url: `/system/deptQuota/info/${deptId}/${year}`,
    method: 'get'
  })
}

// 获取多个部门的配额信息
export function getMultipleDeptQuotaInfo(deptIds, year) {
  return request({
    url: '/system/deptQuota/multiple',
    method: 'get',
    params: {
      deptIds: deptIds.join(','),
      year: year
    }
  })
}

// 检查配额是否可用
export function checkQuotaAvailable(deptId, year, count) {
  return request({
    url: '/system/deptQuota/check',
    method: 'get',
    params: { deptId, year, count }
  })
}

// 新增部门高分配额
export function addDeptQuota(data) {
  return request({
    url: '/system/deptQuota',
    method: 'post',
    data: data
  })
}

// 修改部门高分配额
export function updateDeptQuota(data) {
  return request({
    url: '/system/deptQuota',
    method: 'put',
    data: data
  })
}

// 删除部门高分配额
export function delDeptQuota(id) {
  return request({
    url: '/system/deptQuota/' + id,
    method: 'delete'
  })
}

// 使用高分配额
export function useQuota(data) {
  return request({
    url: '/system/deptQuota/use',
    method: 'post',
    data: data
  })
}

// 释放高分配额
export function releaseQuota(data) {
  return request({
    url: '/system/deptQuota/release',
    method: 'post',
    data: data
  })
}
