package com.kc.framework.security.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.kc.common.utils.StringUtils;
import com.kc.common.core.domain.model.LoginUser;
import com.kc.common.utils.SecurityUtils;
import com.kc.common.constant.Constants;
import java.util.Set;

/**
 * 权限验证处理
 * 
 * <AUTHOR>
 */
@Service("ss")
public class PermissionService {
    private static final Logger log = LoggerFactory.getLogger(PermissionService.class);

    /**
     * 验证用户是否具备某权限
     * 
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    public boolean hasPermi(String permission) {
        if (StringUtils.isEmpty(permission)) {
            return false;
        }
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNull(loginUser) || CollectionUtils.isEmpty(loginUser.getPermissions())) {
            return false;
        }
        log.debug("检查权限 [{}] 用户权限列表: {}", permission, loginUser.getPermissions());
        return hasPermissions(loginUser.getPermissions(), permission);
    }

    /**
     * 判断是否包含权限
     * 
     * @param permissions 权限列表
     * @param permission 权限字符串
     * @return 用户是否具备某权限
     */
    private boolean hasPermissions(Set<String> permissions, String permission) {
        return permissions.contains(Constants.ALL_PERMISSION) || permissions.contains(StringUtils.trim(permission));
    }
} 