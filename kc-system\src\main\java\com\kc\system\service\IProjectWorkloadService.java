package com.kc.system.service;

import java.util.List;
import java.util.Map;
import com.kc.system.domain.ProjectWorkload;

/**
 * 项目工时记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-15
 */
public interface IProjectWorkloadService 
{
    /**
     * 查询项目工时记录
     * 
     * @param id 项目工时记录主键
     * @return 项目工时记录
     */
    public ProjectWorkload selectProjectWorkloadById(Long id);

    /**
     * 查询项目工时记录列表
     *
     * @param projectWorkload 项目工时记录
     * @return 项目工时记录集合
     */
    public List<ProjectWorkload> selectProjectWorkloadList(ProjectWorkload projectWorkload);

    /**
     * 查询用户实际工时记录
     *
     * @param projectWorkload 项目工时记录
     * @return 项目工时记录集合
     */
    public List<ProjectWorkload> selectUserActualWorkload(ProjectWorkload projectWorkload);

    /**
     * 查询实际工时记录列表（只返回有实际记录的数据）
     *
     * @param projectWorkload 项目工时记录
     * @return 项目工时记录集合
     */
    public List<ProjectWorkload> selectActualWorkloadList(ProjectWorkload projectWorkload);

    /**
     * 新增项目工时记录
     * 
     * @param projectWorkload 项目工时记录
     * @return 结果
     */
    public int insertProjectWorkload(ProjectWorkload projectWorkload);

    /**
     * 修改项目工时记录
     * 
     * @param projectWorkload 项目工时记录
     * @return 结果
     */
    public int updateProjectWorkload(ProjectWorkload projectWorkload);

    /**
     * 批量删除项目工时记录
     * 
     * @param ids 需要删除的项目工时记录主键集合
     * @return 结果
     */
    public int deleteProjectWorkloadByIds(Long[] ids);

    /**
     * 删除项目工时记录信息
     * 
     * @param id 项目工时记录主键
     * @return 结果
     */
    public int deleteProjectWorkloadById(Long id);

    /**
     * 获取项目统计数据
     * 
     * @param projectId 项目ID
     * @param workMonth 工作月份
     * @return 项目统计数据
     */
    public List<Map<String, Object>> getProjectStats(Long projectId, String workMonth);

    /**
     * 获取所有项目统计数据（不分页）
     * 
     * @param workMonth 工作月份
     * @return 统计数据列表
     */
    public List<Map<String, Object>> getAllProjectStats(String workMonth);

    /**
     * 统计当前月份中未填报工时的人数及项目名称
     * 
     * @param workMonth 工作月份
     * @return 未填报工时的统计信息
     */
    public List<Map<String, Object>> selectUnfilledWorkloadCount(String workMonth);
}
