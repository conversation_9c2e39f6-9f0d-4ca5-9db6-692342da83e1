package com.kc.system.domain;

import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 配额组对象 quota_group
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public class QuotaGroup extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 配额组名称 */
    @Excel(name = "配额组名称")
    private String groupName;

    /** 配额组编码 */
    @Excel(name = "配额组编码")
    private String groupCode;

    /** 配额组描述 */
    @Excel(name = "配额组描述")
    private String description;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 配额组部门列表 */
    private List<QuotaGroupDept> deptList;

    /** 配额组配额列表 */
    private List<QuotaGroupQuota> quotaList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setGroupName(String groupName) 
    {
        this.groupName = groupName;
    }

    public String getGroupName() 
    {
        return groupName;
    }

    public void setGroupCode(String groupCode) 
    {
        this.groupCode = groupCode;
    }

    public String getGroupCode() 
    {
        return groupCode;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public List<QuotaGroupDept> getDeptList() 
    {
        return deptList;
    }

    public void setDeptList(List<QuotaGroupDept> deptList) 
    {
        this.deptList = deptList;
    }

    public List<QuotaGroupQuota> getQuotaList() 
    {
        return quotaList;
    }

    public void setQuotaList(List<QuotaGroupQuota> quotaList) 
    {
        this.quotaList = quotaList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("groupName", getGroupName())
            .append("groupCode", getGroupCode())
            .append("description", getDescription())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
