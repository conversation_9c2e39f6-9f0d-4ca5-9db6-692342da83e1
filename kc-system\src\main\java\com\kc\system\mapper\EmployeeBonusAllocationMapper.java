package com.kc.system.mapper;

import java.util.List;
import com.kc.system.domain.EmployeeBonusAllocation;
import org.apache.ibatis.annotations.Param;

/**
 * 员工奖金分配Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface EmployeeBonusAllocationMapper 
{
    /**
     * 查询员工奖金分配
     * 
     * @param id 员工奖金分配主键
     * @return 员工奖金分配
     */
    public EmployeeBonusAllocation selectEmployeeBonusAllocationById(Long id);

    /**
     * 查询员工奖金分配列表
     * 
     * @param employeeBonusAllocation 员工奖金分配
     * @return 员工奖金分配集合
     */
    public List<EmployeeBonusAllocation> selectEmployeeBonusAllocationList(EmployeeBonusAllocation employeeBonusAllocation);

    /**
     * 根据用户ID和月份查询员工奖金分配
     * 
     * @param userId 用户ID
     * @param allocationMonth 分配月份
     * @return 员工奖金分配
     */
    public EmployeeBonusAllocation selectByUserIdAndMonth(@Param("userId") Long userId, @Param("allocationMonth") String allocationMonth);

    /**
     * 根据部门奖金分配ID查询员工奖金分配列表
     * 
     * @param deptBonusId 部门奖金分配ID
     * @return 员工奖金分配集合
     */
    public List<EmployeeBonusAllocation> selectByDeptBonusId(@Param("deptBonusId") Long deptBonusId);

    /**
     * 根据部门ID和月份查询员工奖金分配列表
     * 
     * @param deptId 部门ID
     * @param allocationMonth 分配月份
     * @return 员工奖金分配集合
     */
    public List<EmployeeBonusAllocation> selectByDeptIdAndMonth(@Param("deptId") Long deptId, @Param("allocationMonth") String allocationMonth);

    /**
     * 根据月份查询所有员工奖金分配
     *
     * @param allocationMonth 分配月份
     * @return 员工奖金分配集合
     */
    public List<EmployeeBonusAllocation> selectByMonth(@Param("allocationMonth") String allocationMonth);

    /**
     * 根据分配者ID和月份查询员工奖金分配列表
     *
     * @param allocatorId 分配者ID
     * @param allocationMonth 分配月份
     * @return 员工奖金分配集合
     */
    public List<EmployeeBonusAllocation> selectByAllocatorAndMonth(@Param("allocatorId") Long allocatorId, @Param("allocationMonth") String allocationMonth);

    /**
     * 新增员工奖金分配
     * 
     * @param employeeBonusAllocation 员工奖金分配
     * @return 结果
     */
    public int insertEmployeeBonusAllocation(EmployeeBonusAllocation employeeBonusAllocation);

    /**
     * 批量新增员工奖金分配
     * 
     * @param employeeBonusAllocations 员工奖金分配列表
     * @return 结果
     */
    public int batchInsertEmployeeBonusAllocation(List<EmployeeBonusAllocation> employeeBonusAllocations);

    /**
     * 修改员工奖金分配
     * 
     * @param employeeBonusAllocation 员工奖金分配
     * @return 结果
     */
    public int updateEmployeeBonusAllocation(EmployeeBonusAllocation employeeBonusAllocation);

    /**
     * 删除员工奖金分配
     * 
     * @param id 员工奖金分配主键
     * @return 结果
     */
    public int deleteEmployeeBonusAllocationById(Long id);

    /**
     * 批量删除员工奖金分配
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmployeeBonusAllocationByIds(Long[] ids);

    /**
     * 根据部门奖金分配ID删除员工奖金分配
     * 
     * @param deptBonusId 部门奖金分配ID
     * @return 结果
     */
    public int deleteByDeptBonusId(@Param("deptBonusId") Long deptBonusId);

    /**
     * 根据月份删除员工奖金分配
     * 
     * @param allocationMonth 分配月份
     * @return 结果
     */
    public int deleteByMonth(@Param("allocationMonth") String allocationMonth);
}
