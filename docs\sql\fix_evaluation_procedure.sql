-- 修复版本的存储过程
DROP PROCEDURE IF EXISTS `calculate_evaluation_results_with_datascope`;

DELIMITER $$
CREATE DEFINER=`root`@`%` PROCEDURE `calculate_evaluation_results_with_datascope`(
    IN p_month VARCHAR(7),
    IN p_user_id BIGINT,
    IN p_dept_ids VARCHAR(1000)
)
BEGIN
    -- 声明变量
    DECLARE v_processed_count INT DEFAULT 0;
    
    -- 添加日志
    SELECT CONCAT('开始执行评价结果计算，参数: month=', p_month, 
        ', user_id=', p_user_id, ', dept_ids=', IFNULL(p_dept_ids, 'NULL')) AS '系统日志';
    
    -- 直接清空当月评价结果
    SELECT '清空所有当月评价结果数据' AS '系统日志';
    
    -- 记录清空前的记录数
    SELECT COUNT(*) INTO v_processed_count FROM evaluation_result WHERE evaluation_month = p_month;
    SELECT CONCAT('清空前记录数: ', v_processed_count) AS '系统日志';
    
    DELETE FROM evaluation_result WHERE evaluation_month = p_month;
    
    -- 计算将处理的用户数
    SELECT COUNT(*) INTO v_processed_count FROM sys_user 
    WHERE del_flag = '0' AND status = '0';
    
    SELECT CONCAT('将处理用户数: ', v_processed_count) AS '系统日志';
    
    -- 计算所有用户的评分结果
    INSERT INTO evaluation_result 
        (user_id, user_name, nick_name, dept_id, evaluation_month, final_score, manager_score, project_leader_score, user_role)
    SELECT 
        u.user_id,
        u.user_name,
        u.nick_name,
        u.dept_id,
        p_month,
        -- 计算最终得分
        CASE 
            -- 仅项目负责人：评价得分=机构负责人评分
            WHEN EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role = '负责人') 
                 AND NOT EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role != '负责人') THEN
                COALESCE((SELECT AVG(score) FROM project_evaluation 
                         WHERE evaluatee_id = u.user_id AND evaluation_month = p_month 
                         AND evaluation_type = 'manager'), 0)
            
            -- 不参与项目员工：评价得分=机构负责人评分
            WHEN NOT EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name) THEN
                COALESCE((SELECT AVG(score) FROM project_evaluation 
                         WHERE evaluatee_id = u.user_id AND evaluation_month = p_month 
                         AND evaluation_type = 'manager'), 0)
            
            -- 既为项目负责人，又为项目员工：评价得分=机构负责人评价得分*60%+项目负责人评价平均分*40%
            WHEN EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role = '负责人') 
                 AND EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role != '负责人') THEN
                COALESCE((SELECT AVG(score) FROM project_evaluation 
                         WHERE evaluatee_id = u.user_id AND evaluation_month = p_month 
                         AND evaluation_type = 'manager'), 0) * 0.6 + 
                COALESCE((
                    SELECT SUM(pe.score * pp.participation_rate) / SUM(pp.participation_rate)
                    FROM project_evaluation pe
                    JOIN project_participation pp ON pe.project_id = pp.project_id 
                    JOIN sys_user su ON pp.user_name = su.user_name
                    WHERE pe.evaluatee_id = u.user_id 
                      AND pe.evaluation_month = p_month 
                      AND pe.evaluation_type = 'project_leader'
                      AND pe.evaluator_id != pe.evaluatee_id
                      AND pp.month = p_month
                ), 0) * 0.4
            
            -- 仅为项目成员：评价得分=机构负责人评价得分*60%+项目负责人其评价平均分*40%
            WHEN EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role != '负责人')
                 AND NOT EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role = '负责人') THEN
                COALESCE((SELECT AVG(score) FROM project_evaluation 
                         WHERE evaluatee_id = u.user_id AND evaluation_month = p_month 
                         AND evaluation_type = 'manager'), 0) * 0.6 + 
                COALESCE((
                    SELECT SUM(pe.score * pp.participation_rate) / SUM(pp.participation_rate)
                    FROM project_evaluation pe
                    JOIN project_participation pp ON pe.project_id = pp.project_id 
                    JOIN sys_user su ON pp.user_name = su.user_name
                    WHERE pe.evaluatee_id = u.user_id 
                      AND pe.evaluation_month = p_month 
                      AND pe.evaluation_type = 'project_leader'
                      AND pe.evaluator_id != pe.evaluatee_id
                      AND pp.month = p_month
                ), 0) * 0.4
            
            ELSE 0
        END AS final_score,
        
        -- 机构负责人评分
        COALESCE((SELECT AVG(score) FROM project_evaluation 
                 WHERE evaluatee_id = u.user_id AND evaluation_month = p_month 
                 AND evaluation_type = 'manager'), 0) AS manager_score,
        
        -- 项目负责人平均评分
        COALESCE((
            SELECT SUM(pe.score * pp.participation_rate) / SUM(pp.participation_rate)
            FROM project_evaluation pe
            JOIN project_participation pp ON pe.project_id = pp.project_id 
            JOIN sys_user su ON pp.user_name = su.user_name
            WHERE pe.evaluatee_id = u.user_id 
              AND pe.evaluation_month = p_month 
              AND pe.evaluation_type = 'project_leader'
              AND pe.evaluator_id != pe.evaluatee_id
              AND pp.month = p_month
        ), 0) AS project_leader_score,
        
        -- 用户角色判断
        CASE 
            WHEN EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role = '负责人') 
                 AND NOT EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role != '负责人') THEN
                'project_leader'
            WHEN NOT EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name) THEN
                'none'
            WHEN EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role = '负责人') 
                 AND EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role != '负责人') THEN
                'both'
            WHEN EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role != '负责人')
                 AND NOT EXISTS (SELECT 1 FROM project_members pm WHERE pm.user_name = u.user_name AND pm.role = '负责人') THEN
                'project_member'
            ELSE 'none'
        END AS user_role
    FROM 
        sys_user u
    WHERE 
        u.del_flag = '0' AND u.status = '0';
         
    -- 记录处理的记录数
    SELECT ROW_COUNT() INTO v_processed_count;
    SELECT CONCAT('处理完成，新增评价结果记录: ', v_processed_count) AS '系统日志';
    
    -- 添加日志，显示处理的记录数
    SELECT CONCAT('处理完成，共计算评价结果 ', 
                  (SELECT COUNT(*) FROM evaluation_result WHERE evaluation_month = p_month), 
                  ' 条记录') AS ResultMessage;
END$$

DELIMITER ; 