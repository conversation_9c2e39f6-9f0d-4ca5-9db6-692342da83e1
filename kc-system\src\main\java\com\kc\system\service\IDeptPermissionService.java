package com.kc.system.service;

import com.kc.common.core.domain.entity.SysDept;
import java.util.List;

/**
 * 部门权限服务接口
 * 用于处理复杂的部门权限逻辑，特别是子部门权限控制
 * 
 * <AUTHOR>
 */
public interface IDeptPermissionService {
    
    /**
     * 获取用户有权限管理的部门列表（考虑层级关系）
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @return 有权限的部门列表
     */
    List<SysDept> getUserManagedDepts(Long userId, String userName);
    
    /**
     * 获取用户直接负责的部门列表（不包括通过父部门继承的权限）
     * 
     * @param userName 用户名
     * @return 直接负责的部门列表
     */
    List<SysDept> getUserDirectManagedDepts(String userName);
    
    /**
     * 获取用户通过父部门权限可以管理的子部门列表
     * 
     * @param userName 用户名
     * @return 可管理的子部门列表
     */
    List<SysDept> getUserInheritedManagedDepts(String userName);
    
    /**
     * 检查用户是否有权限管理指定部门
     * 
     * @param userName 用户名
     * @param deptId 部门ID
     * @return 是否有权限
     */
    boolean hasPermissionToManageDept(String userName, Long deptId);
    
    /**
     * 检查用户是否是指定部门的直接负责人
     * 
     * @param userName 用户名
     * @param deptId 部门ID
     * @return 是否是直接负责人
     */
    boolean isDirectLeaderOfDept(String userName, Long deptId);
    
    /**
     * 检查用户是否通过父部门权限管理指定部门
     * 
     * @param userName 用户名
     * @param deptId 部门ID
     * @return 是否通过父部门权限管理
     */
    boolean isInheritedLeaderOfDept(String userName, Long deptId);
    
    /**
     * 获取用户在员工评分场景下有权限查看的部门
     * 规则：
     * 1. 如果用户是子部门负责人，只能看自己直接负责的部门
     * 2. 如果用户是父部门负责人，可以看自己负责的部门及其子部门
     * 
     * @param userName 用户名
     * @return 有权限查看的部门列表
     */
    List<SysDept> getDeptsForEmployeeEvaluation(String userName);
    
    /**
     * 获取用户在奖金分配场景下有权限查看的部门
     * 规则：可能与员工评分规则不同
     * 
     * @param userName 用户名
     * @return 有权限查看的部门列表
     */
    List<SysDept> getDeptsForBonusAllocation(String userName);
}
