package com.kc.system.mapper;

import java.util.List;
import com.kc.system.domain.DeptBonusAllocation;
import org.apache.ibatis.annotations.Param;

/**
 * 部门奖金分配Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface DeptBonusAllocationMapper 
{
    /**
     * 查询部门奖金分配
     * 
     * @param id 部门奖金分配主键
     * @return 部门奖金分配
     */
    public DeptBonusAllocation selectDeptBonusAllocationById(Long id);

    /**
     * 查询部门奖金分配列表
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 部门奖金分配集合
     */
    public List<DeptBonusAllocation> selectDeptBonusAllocationList(DeptBonusAllocation deptBonusAllocation);

    /**
     * 根据部门ID和月份查询部门奖金分配
     * 
     * @param deptId 部门ID
     * @param allocationMonth 分配月份
     * @return 部门奖金分配
     */
    public DeptBonusAllocation selectByDeptIdAndMonth(@Param("deptId") Long deptId, @Param("allocationMonth") String allocationMonth);

    /**
     * 根据月份查询所有部门奖金分配
     * 
     * @param allocationMonth 分配月份
     * @return 部门奖金分配集合
     */
    public List<DeptBonusAllocation> selectByMonth(@Param("allocationMonth") String allocationMonth);

    /**
     * 新增部门奖金分配
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 结果
     */
    public int insertDeptBonusAllocation(DeptBonusAllocation deptBonusAllocation);

    /**
     * 批量新增部门奖金分配
     * 
     * @param deptBonusAllocations 部门奖金分配列表
     * @return 结果
     */
    public int batchInsertDeptBonusAllocation(List<DeptBonusAllocation> deptBonusAllocations);

    /**
     * 修改部门奖金分配
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 结果
     */
    public int updateDeptBonusAllocation(DeptBonusAllocation deptBonusAllocation);

    /**
     * 更新部门奖金分配状态和已分配金额
     * 
     * @param id 部门奖金分配ID
     * @param allocatedBonus 已分配奖金
     * @param remainingBonus 剩余奖金
     * @param allocationStatus 分配状态
     * @return 结果
     */
    public int updateAllocationStatus(@Param("id") Long id, 
                                    @Param("allocatedBonus") java.math.BigDecimal allocatedBonus,
                                    @Param("remainingBonus") java.math.BigDecimal remainingBonus,
                                    @Param("allocationStatus") String allocationStatus);

    /**
     * 删除部门奖金分配
     * 
     * @param id 部门奖金分配主键
     * @return 结果
     */
    public int deleteDeptBonusAllocationById(Long id);

    /**
     * 批量删除部门奖金分配
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeptBonusAllocationByIds(Long[] ids);

    /**
     * 根据月份删除部门奖金分配
     * 
     * @param allocationMonth 分配月份
     * @return 结果
     */
    public int deleteByMonth(@Param("allocationMonth") String allocationMonth);
}
