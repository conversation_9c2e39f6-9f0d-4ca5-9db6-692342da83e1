<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.DeptHighScoreQuotaMapper">
    
    <resultMap type="DeptHighScoreQuota" id="DeptHighScoreQuotaResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="evaluationYear"    column="evaluation_year"    />
        <result property="totalEmployees"    column="total_employees"    />
        <result property="highScoreQuota"    column="high_score_quota"    />
        <result property="usedQuota"    column="used_quota"    />
        <result property="remainingQuota"    column="remaining_quota"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectDeptHighScoreQuotaVo">
        select id, dept_id, evaluation_year, total_employees, high_score_quota, used_quota, remaining_quota, create_time, update_time, create_by, update_by from dept_high_score_quota
    </sql>

    <select id="selectDeptHighScoreQuotaList" parameterType="DeptHighScoreQuota" resultMap="DeptHighScoreQuotaResult">
        <include refid="selectDeptHighScoreQuotaVo"/>
        <where>  
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="evaluationYear != null  and evaluationYear != ''"> and evaluation_year = #{evaluationYear}</if>
            <if test="totalEmployees != null "> and total_employees = #{totalEmployees}</if>
            <if test="highScoreQuota != null "> and high_score_quota = #{highScoreQuota}</if>
            <if test="usedQuota != null "> and used_quota = #{usedQuota}</if>
            <if test="remainingQuota != null "> and remaining_quota = #{remainingQuota}</if>
        </where>
    </select>
    
    <select id="selectDeptHighScoreQuotaById" parameterType="Long" resultMap="DeptHighScoreQuotaResult">
        <include refid="selectDeptHighScoreQuotaVo"/>
        where id = #{id}
    </select>

    <select id="selectByDeptAndYear" resultMap="DeptHighScoreQuotaResult">
        <include refid="selectDeptHighScoreQuotaVo"/>
        where dept_id = #{deptId} and evaluation_year = #{evaluationYear}
    </select>
        
    <insert id="insertDeptHighScoreQuota" parameterType="DeptHighScoreQuota" useGeneratedKeys="true" keyProperty="id">
        insert into dept_high_score_quota
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="evaluationYear != null and evaluationYear != ''">evaluation_year,</if>
            <if test="totalEmployees != null">total_employees,</if>
            <if test="highScoreQuota != null">high_score_quota,</if>
            <if test="usedQuota != null">used_quota,</if>
            <if test="remainingQuota != null">remaining_quota,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="evaluationYear != null and evaluationYear != ''">#{evaluationYear},</if>
            <if test="totalEmployees != null">#{totalEmployees},</if>
            <if test="highScoreQuota != null">#{highScoreQuota},</if>
            <if test="usedQuota != null">#{usedQuota},</if>
            <if test="remainingQuota != null">#{remainingQuota},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateDeptHighScoreQuota" parameterType="DeptHighScoreQuota">
        update dept_high_score_quota
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="evaluationYear != null and evaluationYear != ''">evaluation_year = #{evaluationYear},</if>
            <if test="totalEmployees != null">total_employees = #{totalEmployees},</if>
            <if test="highScoreQuota != null">high_score_quota = #{highScoreQuota},</if>
            <if test="usedQuota != null">used_quota = #{usedQuota},</if>
            <if test="remainingQuota != null">remaining_quota = #{remainingQuota},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeptHighScoreQuotaById" parameterType="Long">
        delete from dept_high_score_quota where id = #{id}
    </delete>

    <delete id="deleteDeptHighScoreQuotaByIds" parameterType="String">
        delete from dept_high_score_quota where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
