package com.kc.system.service;

import java.util.List;
import com.kc.system.domain.EvaluationResult;
import com.kc.common.core.domain.model.LoginUser;
import com.kc.system.domain.dto.EvaluationDetailDTO;

/**
 * 评价结果Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
public interface IEvaluationResultService 
{
    /**
     * 查询评价结果
     * 
     * @param id 评价结果主键
     * @return 评价结果
     */
    public EvaluationResult selectEvaluationResultById(Long id);

    /**
     * 查询评价结果列表
     * 
     * @param evaluationResult 评价结果
     * @return 评价结果集合
     */
    public List<EvaluationResult> selectEvaluationResultList(EvaluationResult evaluationResult);

    /**
     * 新增评价结果
     * 
     * @param evaluationResult 评价结果
     * @return 结果
     */
    public int insertEvaluationResult(EvaluationResult evaluationResult);

    /**
     * 修改评价结果
     * 
     * @param evaluationResult 评价结果
     * @return 结果
     */
    public int updateEvaluationResult(EvaluationResult evaluationResult);

    /**
     * 批量删除评价结果
     * 
     * @param ids 需要删除的评价结果主键集合
     * @return 结果
     */
    public int deleteEvaluationResultByIds(Long[] ids);

    /**
     * 删除评价结果信息
     * 
     * @param id 评价结果主键
     * @return 结果
     */
    public int deleteEvaluationResultById(Long id);
    
    /**
     * 计算评价结果
     * 
     * @param evaluationMonth 评价月份
     * @param userId 当前用户ID
     * @param deptIds 当前用户数据权限范围内的部门ID列表，逗号分隔
     * @return 结果
     */
    public int calculateEvaluationResults(String evaluationMonth, Long userId, String deptIds);
    
    /**
     * 计算指定月份的评价结果（带数据权限）
     * 
     * @param month 评价月份 格式：yyyy-MM
     * @param loginUser 当前登录用户，用于数据权限控制
     * @return 处理的记录数
     */
    public int calculateEvaluationResultsWithDataScope(String month, LoginUser loginUser);
    
    /**
     * 获取评价结果计算明细
     * 
     * @param userId 用户ID
     * @param evaluationMonth 评价月份 格式：yyyy-MM
     * @return 评价结果计算明细
     */
    public EvaluationDetailDTO getEvaluationDetail(Long userId, String evaluationMonth);
    
    /**
     * 计算评价结果 - Java实现替代存储过程
     *
     * @param evaluationMonth 评价月份
     * @param userId 当前用户ID
     * @param deptIds 当前用户数据权限范围内的部门ID列表，逗号分隔
     * @return 结果（处理的记录数）
     */
    public int calculateEvaluationResultsJava(String evaluationMonth, Long userId, String deptIds);

    /**
     * 计算指定用户列表的评价结果并插入到evaluation_result表
     *
     * @param userIds 用户ID列表
     * @param evaluationMonth 评价月份
     * @return 结果（处理的记录数）
     */
    public int calculateAndInsertEvaluationResults(List<Long> userIds, String evaluationMonth);
}
