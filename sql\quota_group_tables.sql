-- 配额组管理相关表结构

-- 1. 配额组表
CREATE TABLE `quota_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_name` varchar(100) NOT NULL COMMENT '配额组名称',
  `group_code` varchar(50) NOT NULL COMMENT '配额组编码',
  `description` varchar(500) DEFAULT NULL COMMENT '配额组描述',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_code` (`group_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额组表';

-- 2. 配额组部门关系表
CREATE TABLE `quota_group_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` bigint(20) NOT NULL COMMENT '配额组ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `is_primary` char(1) DEFAULT '0' COMMENT '是否主部门（0否 1是）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_dept` (`group_id`, `dept_id`),
  KEY `idx_group_id` (`group_id`),
  KEY `idx_dept_id` (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额组部门关系表';

-- 3. 配额组配额表
CREATE TABLE `quota_group_quota` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` bigint(20) NOT NULL COMMENT '配额组ID',
  `evaluation_year` varchar(4) NOT NULL COMMENT '评价年度',
  `total_employees` int(11) NOT NULL DEFAULT '0' COMMENT '组内总人数',
  `high_score_quota` int(11) NOT NULL DEFAULT '0' COMMENT '高分配额',
  `used_quota` int(11) NOT NULL DEFAULT '0' COMMENT '已使用配额',
  `remaining_quota` int(11) NOT NULL DEFAULT '0' COMMENT '剩余配额',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_year` (`group_id`, `evaluation_year`),
  KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额组配额表';

-- 4. 初始化科技创新部和共享商务中心的配额组
INSERT INTO `quota_group` (`group_name`, `group_code`, `description`, `create_by`) 
VALUES ('科技创新配额组', 'TECH_INNOVATION_GROUP', '科技创新部与共享商务中心共享配额组', 'admin');

-- 获取刚插入的配额组ID
SET @group_id = LAST_INSERT_ID();

-- 添加部门到配额组（200为主部门，210为子部门）
INSERT INTO `quota_group_dept` (`group_id`, `dept_id`, `is_primary`, `create_by`) VALUES 
(@group_id, 200, '1', 'admin'),
(@group_id, 210, '0', 'admin');

-- 5. 为配额组初始化2025年配额（基于当前数据）
INSERT INTO `quota_group_quota` (`group_id`, `evaluation_year`, `total_employees`, `high_score_quota`, `used_quota`, `remaining_quota`, `create_by`)
SELECT 
    @group_id,
    '2025',
    SUM(total_employees) as total_employees,
    SUM(high_score_quota) as high_score_quota,
    SUM(used_quota) as used_quota,
    SUM(remaining_quota) as remaining_quota,
    'admin'
FROM dept_high_score_quota 
WHERE dept_id IN (200, 210) AND evaluation_year = '2025';

-- 6. 创建索引优化查询性能
CREATE INDEX `idx_quota_group_status` ON `quota_group` (`status`);
CREATE INDEX `idx_quota_group_dept_primary` ON `quota_group_dept` (`is_primary`);
CREATE INDEX `idx_quota_group_quota_year` ON `quota_group_quota` (`evaluation_year`);
