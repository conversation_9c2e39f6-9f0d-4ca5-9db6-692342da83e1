package com.kc.system.service.impl;

import java.util.List;
import java.util.Map;
import com.kc.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kc.system.mapper.ProjectSalaryDetailMapper;
import com.kc.system.domain.ProjectSalaryDetail;
import com.kc.system.service.IProjectSalaryDetailService;

/**
 * 项目劳务费构成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@Service
public class ProjectSalaryDetailServiceImpl implements IProjectSalaryDetailService 
{
    @Autowired
    private ProjectSalaryDetailMapper projectSalaryDetailMapper;

    /**
     * 查询项目劳务费构成
     * 
     * @param id 项目劳务费构成主键
     * @return 项目劳务费构成
     */
    @Override
    public ProjectSalaryDetail selectProjectSalaryDetailById(Long id)
    {
        return projectSalaryDetailMapper.selectProjectSalaryDetailById(id);
    }

    /**
     * 查询项目劳务费构成列表
     * 
     * @param projectSalaryDetail 项目劳务费构成
     * @return 项目劳务费构成
     */
    @Override
    public List<ProjectSalaryDetail> selectProjectSalaryDetailList(ProjectSalaryDetail projectSalaryDetail)
    {
        return projectSalaryDetailMapper.selectProjectSalaryDetailList(projectSalaryDetail);
    }

    /**
     * 新增项目劳务费构成
     * 
     * @param projectSalaryDetail 项目劳务费构成
     * @return 结果
     */
    @Override
    public int insertProjectSalaryDetail(ProjectSalaryDetail projectSalaryDetail)
    {
        projectSalaryDetail.setCreateTime(DateUtils.getNowDate());
        return projectSalaryDetailMapper.insertProjectSalaryDetail(projectSalaryDetail);
    }

    /**
     * 修改项目劳务费构成
     * 
     * @param projectSalaryDetail 项目劳务费构成
     * @return 结果
     */
    @Override
    public int updateProjectSalaryDetail(ProjectSalaryDetail projectSalaryDetail)
    {
        projectSalaryDetail.setUpdateTime(DateUtils.getNowDate());
        return projectSalaryDetailMapper.updateProjectSalaryDetail(projectSalaryDetail);
    }

    /**
     * 批量删除项目劳务费构成
     * 
     * @param ids 需要删除的项目劳务费构成主键
     * @return 结果
     */
    @Override
    public int deleteProjectSalaryDetailByIds(Long[] ids)
    {
        return projectSalaryDetailMapper.deleteProjectSalaryDetailByIds(ids);
    }

    /**
     * 删除项目劳务费构成信息
     * 
     * @param id 项目劳务费构成主键
     * @return 结果
     */
    @Override
    public int deleteProjectSalaryDetailById(Long id)
    {
        return projectSalaryDetailMapper.deleteProjectSalaryDetailById(id);
    }

    @Override
    public List<Map<String, Object>> selectProjectSalaryDetailsByProjectIdAndMonth(Long projectId, String workMonth) {
        return projectSalaryDetailMapper.selectProjectSalaryDetailsByProjectIdAndMonth(projectId, workMonth);
    }
}
