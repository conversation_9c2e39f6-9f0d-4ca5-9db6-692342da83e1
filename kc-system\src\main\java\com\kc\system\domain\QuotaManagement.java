package com.kc.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 配额管理对象 quota_management
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public class QuotaManagement extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 评价年度 */
    @Excel(name = "评价年度")
    private String evaluationYear;

    /** 高分配额 */
    @Excel(name = "高分配额")
    private Integer highScoreQuota;

    /** 配额类型（MANUAL-手动设置，GROUP-配额组） */
    @Excel(name = "配额类型", readConverterExp = "MANUAL=手动设置,GROUP=配额组")
    private String quotaType;

    /** 部门名称（关联查询字段） */
    private String deptName;

    /** 部门负责人（关联查询字段） */
    private String deptLeader;

    /** 已使用配额（计算字段，不存储在数据库） */
    private Integer usedQuota;

    /** 剩余配额（计算字段，不存储在数据库） */
    private Integer remainingQuota;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setEvaluationYear(String evaluationYear) 
    {
        this.evaluationYear = evaluationYear;
    }

    public String getEvaluationYear() 
    {
        return evaluationYear;
    }

    public void setHighScoreQuota(Integer highScoreQuota)
    {
        this.highScoreQuota = highScoreQuota;
    }

    public Integer getHighScoreQuota()
    {
        return highScoreQuota;
    }

    public void setQuotaType(String quotaType)
    {
        this.quotaType = quotaType;
    }

    public String getQuotaType()
    {
        return quotaType;
    }

    public String getDeptName() 
    {
        return deptName;
    }

    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptLeader() 
    {
        return deptLeader;
    }

    public void setDeptLeader(String deptLeader)
    {
        this.deptLeader = deptLeader;
    }

    public Integer getUsedQuota()
    {
        return usedQuota;
    }

    public void setUsedQuota(Integer usedQuota)
    {
        this.usedQuota = usedQuota;
    }

    public Integer getRemainingQuota()
    {
        return remainingQuota;
    }

    public void setRemainingQuota(Integer remainingQuota)
    {
        this.remainingQuota = remainingQuota;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("evaluationYear", getEvaluationYear())
            .append("highScoreQuota", getHighScoreQuota())
            .append("quotaType", getQuotaType())
            .append("deptName", getDeptName())
            .append("deptLeader", getDeptLeader())
            .append("usedQuota", getUsedQuota())
            .append("remainingQuota", getRemainingQuota())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
