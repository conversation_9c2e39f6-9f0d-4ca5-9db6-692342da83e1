-- ===== 项目评价数据月份修复脚本 =====
-- 问题：用户填报时选择了错误的评价月份，部分应该是2025-07的数据被标记为2025-06
-- 解决方案：将错误的2025-06数据更新为2025-07，并处理重复数据

-- 1. 备份当前数据
CREATE TABLE project_evaluation_backup_20250715 AS 
SELECT * FROM project_evaluation WHERE evaluation_month IN ('2025-06', '2025-07');

-- 2. 查看当前数据分布
SELECT 
    '修复前数据统计' as step,
    evaluation_month, 
    evaluation_type, 
    COUNT(*) as count 
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
GROUP BY evaluation_month, evaluation_type 
ORDER BY evaluation_month, evaluation_type;

-- 3. 识别需要修复的数据
-- 基于创建时间判断：6月11日之后创建的2025-06数据很可能是错误的
SELECT 
    '需要修复的数据' as step,
    COUNT(*) as count,
    MIN(created_at) as earliest,
    MAX(created_at) as latest
FROM project_evaluation 
WHERE evaluation_month = '2025-06' 
AND created_at >= '2025-06-11 12:00:00';

-- 4. 查找重复评分记录（同一评价人对同一被评价人在同一项目的多次评分）
SELECT 
    '重复评分统计' as step,
    evaluator_id, 
    evaluatee_id, 
    project_id, 
    evaluation_type,
    evaluation_month,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id ORDER BY created_at) as record_ids,
    GROUP_CONCAT(score ORDER BY created_at) as scores,
    GROUP_CONCAT(created_at ORDER BY created_at) as create_times
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07')
GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 5. 修复方案A：将6月11日12:00之后创建的2025-06数据更新为2025-07
-- 注意：执行前请确认这些数据确实应该是2025-07的数据
/*
UPDATE project_evaluation 
SET evaluation_month = '2025-07'
WHERE evaluation_month = '2025-06' 
AND created_at >= '2025-06-11 12:00:00';
*/

-- 6. 处理重复数据 - 保留最新的记录，删除旧的记录
-- 创建临时表存储要保留的记录ID
/*
CREATE TEMPORARY TABLE records_to_keep AS
SELECT MAX(id) as keep_id
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07')
GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month;

-- 删除重复记录（保留最新的）
DELETE FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07')
AND id NOT IN (SELECT keep_id FROM records_to_keep);

DROP TEMPORARY TABLE records_to_keep;
*/

-- 7. 验证修复结果
SELECT 
    '修复后数据统计' as step,
    evaluation_month, 
    evaluation_type, 
    COUNT(*) as count 
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07') 
GROUP BY evaluation_month, evaluation_type 
ORDER BY evaluation_month, evaluation_type;

-- 8. 检查是否还有重复数据
SELECT 
    '修复后重复检查' as step,
    evaluator_id, 
    evaluatee_id, 
    project_id, 
    evaluation_type,
    evaluation_month,
    COUNT(*) as duplicate_count
FROM project_evaluation 
WHERE evaluation_month IN ('2025-06', '2025-07')
GROUP BY evaluator_id, evaluatee_id, project_id, evaluation_type, evaluation_month
HAVING COUNT(*) > 1;

-- 9. 如果需要回滚，可以使用备份表恢复数据
/*
-- 回滚操作（慎用）
DELETE FROM project_evaluation WHERE evaluation_month IN ('2025-06', '2025-07');
INSERT INTO project_evaluation SELECT * FROM project_evaluation_backup_20250715;
*/

-- 10. 清理备份表（确认修复成功后执行）
-- DROP TABLE project_evaluation_backup_20250715;
