package com.kc.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.kc.system.domain.QuotaManagement;

/**
 * 配额管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-XX
 */
public interface QuotaManagementMapper 
{
    /**
     * 查询配额管理
     * 
     * @param id 配额管理主键
     * @return 配额管理
     */
    public QuotaManagement selectQuotaManagementById(Long id);

    /**
     * 查询配额管理列表
     * 
     * @param quotaManagement 配额管理
     * @return 配额管理集合
     */
    public List<QuotaManagement> selectQuotaManagementList(QuotaManagement quotaManagement);

    /**
     * 新增配额管理
     * 
     * @param quotaManagement 配额管理
     * @return 结果
     */
    public int insertQuotaManagement(QuotaManagement quotaManagement);

    /**
     * 修改配额管理
     * 
     * @param quotaManagement 配额管理
     * @return 结果
     */
    public int updateQuotaManagement(QuotaManagement quotaManagement);

    /**
     * 删除配额管理
     * 
     * @param id 配额管理主键
     * @return 结果
     */
    public int deleteQuotaManagementById(Long id);

    /**
     * 批量删除配额管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQuotaManagementByIds(Long[] ids);

    /**
     * 根据部门ID和年度查询配额管理
     *
     * @param deptId 部门ID
     * @param year 评价年度
     * @return 配额管理
     */
    public QuotaManagement selectByDeptAndYear(@Param("deptId") Long deptId, @Param("year") String year);

    /**
     * 批量更新配额使用情况
     * 
     * @param quotaManagementList 配额管理列表
     * @return 结果
     */
    public int batchUpdateQuotaUsage(List<QuotaManagement> quotaManagementList);

    /**
     * 重置年度配额使用情况
     *
     * @param year 评价年度
     * @return 结果
     */
    public int resetYearQuotaUsage(@Param("year") String year);

    /**
     * 查询部门配额统计
     *
     * @param year 评价年度
     * @return 配额统计列表
     */
    public List<QuotaManagement> selectQuotaStatistics(@Param("year") String year);

    /**
     * 查询需要初始化配额的部门
     *
     * @param year 评价年度
     * @return 部门列表
     */
    public List<QuotaManagement> selectDeptsNeedInitQuota(@Param("year") String year);

    /**
     * 批量初始化部门配额
     * 
     * @param quotaManagementList 配额管理列表
     * @return 结果
     */
    public int batchInitDeptQuota(List<QuotaManagement> quotaManagementList);
}
