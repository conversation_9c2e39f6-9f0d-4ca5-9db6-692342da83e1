-- 修复工时记录重复数据的SQL脚本
-- 解决唯一约束冲突问题

-- 1. 查看当前重复数据情况
SELECT 
    user_name,
    work_month,
    source_effort_project_id,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id ORDER BY id) as record_ids,
    GROUP_CONCAT(project_id ORDER BY id) as project_ids,
    GROUP_CONCAT(involvement ORDER BY id) as involvements
FROM project_workload 
WHERE source_effort_project_id IS NOT NULL
GROUP BY user_name, work_month, source_effort_project_id
HAVING COUNT(*) > 1
ORDER BY user_name, work_month, source_effort_project_id;

-- 2. 查看用户 10111931 在 2025-06 的具体情况
SELECT 
    id,
    project_id,
    user_name,
    work_month,
    involvement,
    source_effort_project_id,
    created_at,
    updated_at
FROM project_workload 
WHERE user_name = '10111931' 
AND work_month = '2025-06'
ORDER BY id;

-- 3. 删除重复记录的方案（保留最新的记录）
-- 注意：执行前请备份数据！

-- 方案A：删除较旧的重复记录（保留ID最大的）
DELETE pw1 FROM project_workload pw1
INNER JOIN project_workload pw2 
WHERE pw1.user_name = pw2.user_name
  AND pw1.work_month = pw2.work_month
  AND pw1.source_effort_project_id = pw2.source_effort_project_id
  AND pw1.source_effort_project_id IS NOT NULL
  AND pw1.id < pw2.id;

-- 方案B：如果需要手动处理特定用户的数据
-- 删除用户 10111931 在 2025-06 中 source_effort_project_id = 279 的重复记录
-- （保留最新的一条）
/*
DELETE FROM project_workload 
WHERE user_name = '10111931' 
  AND work_month = '2025-06' 
  AND source_effort_project_id = 279
  AND id NOT IN (
    SELECT * FROM (
      SELECT MAX(id) 
      FROM project_workload 
      WHERE user_name = '10111931' 
        AND work_month = '2025-06' 
        AND source_effort_project_id = 279
    ) as temp
  );
*/

-- 4. 验证清理结果
SELECT 
    user_name,
    work_month,
    source_effort_project_id,
    COUNT(*) as count
FROM project_workload 
WHERE source_effort_project_id IS NOT NULL
GROUP BY user_name, work_month, source_effort_project_id
HAVING COUNT(*) > 1;

-- 5. 查看清理后的数据
SELECT 
    id,
    project_id,
    user_name,
    work_month,
    involvement,
    source_effort_project_id,
    created_at,
    updated_at
FROM project_workload 
WHERE user_name = '10111931' 
AND work_month = '2025-06'
ORDER BY id;

-- 注意事项：
-- 1. 执行删除操作前请务必备份数据库
-- 2. 建议先在测试环境执行
-- 3. 可以先执行查询语句确认要删除的数据
-- 4. 删除后需要重启应用以清除缓存
