-- 修复部门数据问题的SQL脚本

-- 1. 查找并显示所有有问题的数据
SELECT '=== 问题数据统计 ===' as info;

-- 统计project_participation表中用户名不存在的记录
SELECT 
    '用户名不存在' as issue_type,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT pp.user_name) as problematic_users
FROM project_participation pp
LEFT JOIN sys_user u ON pp.user_name = u.user_name
WHERE u.user_name IS NULL;

-- 统计用户dept_id为空的记录
SELECT 
    '用户部门ID为空' as issue_type,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT u.user_name) as problematic_users
FROM sys_user u
WHERE (u.dept_id IS NULL OR u.dept_id = '') AND u.del_flag = '0';

-- 统计部门不存在的记录
SELECT 
    '部门不存在' as issue_type,
    COUNT(*) as count,
    GROUP_CONCAT(DISTINCT CONCAT(u.user_name, '(dept_id:', u.dept_id, ')')) as problematic_users
FROM sys_user u
LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
WHERE u.dept_id IS NOT NULL 
  AND u.dept_id != ''
  AND u.del_flag = '0'
  AND (d.dept_id IS NULL OR d.del_flag = '2');

-- 2. 详细列出所有问题记录
SELECT '=== 详细问题列表 ===' as info;

-- 显示project_participation中用户名不存在的详细记录
SELECT 
    'USER_NOT_FOUND' as issue_type,
    pp.id,
    pp.user_name,
    pp.project_name,
    pp.month,
    'project_participation表中的用户在sys_user表中不存在' as description
FROM project_participation pp
LEFT JOIN sys_user u ON pp.user_name = u.user_name
WHERE u.user_name IS NULL
ORDER BY pp.user_name;

-- 显示用户部门ID为空的详细记录
SELECT 
    'DEPT_ID_NULL' as issue_type,
    u.user_id,
    u.user_name,
    u.nick_name,
    u.dept_id,
    '用户的部门ID为空' as description
FROM sys_user u
WHERE (u.dept_id IS NULL OR u.dept_id = '') 
  AND u.del_flag = '0'
ORDER BY u.user_name;

-- 显示部门不存在的详细记录
SELECT 
    'DEPT_NOT_FOUND' as issue_type,
    u.user_id,
    u.user_name,
    u.nick_name,
    u.dept_id,
    '用户的部门ID在sys_dept表中不存在或已删除' as description
FROM sys_user u
LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
WHERE u.dept_id IS NOT NULL 
  AND u.dept_id != ''
  AND u.del_flag = '0'
  AND (d.dept_id IS NULL OR d.del_flag = '2')
ORDER BY u.user_name;

-- 3. 修复建议（需要根据实际情况手动执行）
SELECT '=== 修复建议 ===' as info;

-- 建议1：为没有部门的用户分配默认部门
-- 首先查看系统中有哪些可用的部门
SELECT 
    '可用部门列表' as info,
    dept_id,
    dept_name,
    parent_id
FROM sys_dept 
WHERE del_flag = '0' 
ORDER BY dept_id;

-- 建议2：创建一个"未分配部门"作为默认部门
-- INSERT INTO sys_dept (dept_name, parent_id, ancestors, order_num, leader, phone, email, status, del_flag, create_by, create_time)
-- VALUES ('未分配部门', 100, '0,100', 99, NULL, NULL, NULL, '0', '0', 'admin', NOW());

-- 建议3：将没有部门的用户分配到默认部门（需要先创建默认部门）
-- UPDATE sys_user 
-- SET dept_id = (SELECT dept_id FROM sys_dept WHERE dept_name = '未分配部门' LIMIT 1)
-- WHERE (dept_id IS NULL OR dept_id = '') AND del_flag = '0';

-- 4. 验证修复结果的查询
SELECT '=== 验证查询 ===' as info;

-- 验证查询：检查修复后是否还有问题
SELECT 
    pp.user_name,
    pp.project_name,
    u.user_id,
    u.nick_name,
    u.dept_id,
    d.dept_name,
    CASE 
        WHEN u.user_name IS NULL THEN 'USER_NOT_FOUND'
        WHEN u.dept_id IS NULL OR u.dept_id = '' THEN 'DEPT_ID_NULL'
        WHEN d.dept_name IS NULL THEN 'DEPT_NOT_FOUND'
        ELSE 'OK'
    END as status
FROM project_participation pp
LEFT JOIN sys_user u ON pp.user_name = u.user_name AND u.del_flag = '0'
LEFT JOIN sys_dept d ON u.dept_id = d.dept_id AND d.del_flag = '0'
WHERE pp.month = '2024-12'  -- 替换为当前月份
ORDER BY status DESC, pp.user_name;
