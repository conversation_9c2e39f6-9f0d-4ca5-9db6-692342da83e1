-- 配额管理系统权限配置（安全版本）
-- 执行前请确保已经创建了配额管理相关的表

-- 1. 检查并删除可能存在的旧菜单
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%' OR perms LIKE 'system:quotaGroup:%'
);

DELETE FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%' OR perms LIKE 'system:quotaGroup:%';

-- 2. 插入配额管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
('配额管理', 2000, 6, 'quotaManagement', 'system/quotaManagement/index', '', 1, 0, 'C', '0', '0', 'system:quotaManagement:list', 'chart', 'admin', sysdate(), '配额管理菜单');

-- 获取配额管理主菜单ID
SET @quota_main_menu_id = LAST_INSERT_ID();

-- 3. 插入配额管理子菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
('配额查询', @quota_main_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:query', '#', 'admin', sysdate(), ''),
('配额新增', @quota_main_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:add', '#', 'admin', sysdate(), ''),
('配额修改', @quota_main_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:edit', '#', 'admin', sysdate(), ''),
('配额删除', @quota_main_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:remove', '#', 'admin', sysdate(), ''),
('配额导出', @quota_main_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:export', '#', 'admin', sysdate(), ''),
('批量设置', @quota_main_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:batch', '#', 'admin', sysdate(), ''),
('配额重置', @quota_main_menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:reset', '#', 'admin', sysdate(), ''),
('配额统计', @quota_main_menu_id, 8, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:statistics', '#', 'admin', sysdate(), '');

-- 4. 插入配额组管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
('配额组管理', 2000, 7, 'quotaGroup', 'system/quotaGroup/index', '', 1, 0, 'C', '0', '0', 'system:quotaGroup:list', 'peoples', 'admin', sysdate(), '配额组管理菜单');

-- 获取配额组管理主菜单ID
SET @quota_group_main_menu_id = LAST_INSERT_ID();

-- 5. 插入配额组管理子菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
('配额组查询', @quota_group_main_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:query', '#', 'admin', sysdate(), ''),
('配额组新增', @quota_group_main_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:add', '#', 'admin', sysdate(), ''),
('配额组修改', @quota_group_main_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:edit', '#', 'admin', sysdate(), ''),
('配额组删除', @quota_group_main_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:remove', '#', 'admin', sysdate(), ''),
('配额组导出', @quota_group_main_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:export', '#', 'admin', sysdate(), '');

-- 6. 为管理员角色（role_id=1）分配所有配额管理权限
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%' OR perms LIKE 'system:quotaGroup:%';

-- 7. 查询现有角色，方便手动分配权限
SELECT role_id, role_name, role_key, status 
FROM sys_role 
WHERE status = '0' 
ORDER BY role_id;

-- 8. 验证权限配置
SELECT 
    m.menu_name,
    m.perms,
    m.menu_type,
    m.visible,
    m.status
FROM sys_menu m 
WHERE m.perms LIKE 'system:quotaManagement:%' OR m.perms LIKE 'system:quotaGroup:%'
ORDER BY m.perms;

-- 9. 验证管理员角色权限分配
SELECT 
    r.role_name,
    m.menu_name,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.perms LIKE 'system:quotaManagement:%' OR m.perms LIKE 'system:quotaGroup:%'
ORDER BY r.role_name, m.perms;

-- 手动为其他角色分配权限的示例SQL：
-- 如果需要为其他角色分配权限，请根据实际情况修改role_id并执行：

-- 示例：为角色ID为2的角色分配配额管理权限
-- INSERT INTO sys_role_menu (role_id, menu_id) 
-- SELECT 2, menu_id FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%';

-- 示例：为角色ID为3的角色分配配额组管理权限
-- INSERT INTO sys_role_menu (role_id, menu_id) 
-- SELECT 3, menu_id FROM sys_menu WHERE perms LIKE 'system:quotaGroup:%';

-- 权限说明：
-- system:quotaManagement:list - 配额管理列表查看
-- system:quotaManagement:query - 配额查询
-- system:quotaManagement:add - 新增配额
-- system:quotaManagement:edit - 修改配额
-- system:quotaManagement:remove - 删除配额
-- system:quotaManagement:export - 导出配额数据
-- system:quotaManagement:batch - 批量设置配额
-- system:quotaManagement:reset - 重置配额使用情况
-- system:quotaManagement:statistics - 配额统计

-- system:quotaGroup:list - 配额组管理列表查看
-- system:quotaGroup:query - 配额组查询
-- system:quotaGroup:add - 新增配额组
-- system:quotaGroup:edit - 修改配额组
-- system:quotaGroup:remove - 删除配额组
-- system:quotaGroup:export - 导出配额组数据
