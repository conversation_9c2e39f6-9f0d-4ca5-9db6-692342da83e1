package com.kc.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kc.system.mapper.EmployeeSalaryMapper;
import com.kc.system.domain.EmployeeSalary;
import com.kc.system.service.IEmployeeSalaryService;
import com.kc.common.utils.StringUtils;
import com.kc.common.exception.ServiceException;

/**
 * 员工薪酬Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-17
 */
@Service
public class EmployeeSalaryServiceImpl implements IEmployeeSalaryService 
{
    @Autowired
    private EmployeeSalaryMapper employeeSalaryMapper;

    /**
     * 查询员工薪酬
     * 
     * @param id 员工薪酬主键
     * @return 员工薪酬
     */
    @Override
    public EmployeeSalary selectEmployeeSalaryById(Long id)
    {
        return employeeSalaryMapper.selectEmployeeSalaryById(id);
    }

    /**
     * 查询员工薪酬列表
     * 
     * @param employeeSalary 员工薪酬
     * @return 员工薪酬
     */
    @Override
    public List<EmployeeSalary> selectEmployeeSalaryList(EmployeeSalary employeeSalary)
    {
        return employeeSalaryMapper.selectEmployeeSalaryList(employeeSalary);
    }

    /**
     * 新增员工薪酬
     * 
     * @param employeeSalary 员工薪酬
     * @return 结果
     */
    @Override
    public int insertEmployeeSalary(EmployeeSalary employeeSalary)
    {
        return employeeSalaryMapper.insertEmployeeSalary(employeeSalary);
    }

    /**
     * 修改员工薪酬
     * 
     * @param employeeSalary 员工薪酬
     * @return 结果
     */
    @Override
    public int updateEmployeeSalary(EmployeeSalary employeeSalary)
    {
        return employeeSalaryMapper.updateEmployeeSalary(employeeSalary);
    }

    /**
     * 批量删除员工薪酬
     * 
     * @param ids 需要删除的员工薪酬主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeSalaryByIds(Long[] ids)
    {
        return employeeSalaryMapper.deleteEmployeeSalaryByIds(ids);
    }

    /**
     * 删除员工薪酬信息
     * 
     * @param id 员工薪酬主键
     * @return 结果
     */
    @Override
    public int deleteEmployeeSalaryById(Long id)
    {
        return employeeSalaryMapper.deleteEmployeeSalaryById(id);
    }

    /**
     * 导入薪资数据
     * 
     * @param salaryList 薪资数据列表
     * @param updateSupport 是否支持更新
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importSalary(List<EmployeeSalary> salaryList, boolean updateSupport, String operName) {
        if (StringUtils.isNull(salaryList) || salaryList.size() == 0) {
            throw new ServiceException("导入薪资数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        int updateNum = 0;
        int skipNum = 0;  // 添加跳过计数
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (EmployeeSalary salary : salaryList) {
            try {
                // 验证是否存在这个用户
                if (StringUtils.isEmpty(salary.getUserName())) {
                    failureNum++;
                    failureMsg.append("<br/>第 " + (successNum + failureNum + skipNum) + " 条数据员工编号为空");
                    continue;
                }

                // 验证月份格式
                if (!salary.getSalaryMonth().matches("^\\d{4}-(?:0[1-9]|1[0-2])$")) {
                    failureNum++;
                    failureMsg.append("<br/>第 " + (successNum + failureNum + skipNum) + " 条数据月份格式不正确，应为YYYY-MM格式");
                    continue;
                }
                
                // 验证是否已存在相同月份的记录
                EmployeeSalary existSalary = new EmployeeSalary();
                existSalary.setUserName(salary.getUserName());
                existSalary.setSalaryMonth(salary.getSalaryMonth());
                List<EmployeeSalary> existList = employeeSalaryMapper.selectEmployeeSalaryList(existSalary);
                
                if (existList != null && existList.size() > 0) {
                    if (updateSupport) {
                        salary.setId(existList.get(0).getId());
                        employeeSalaryMapper.updateEmployeeSalary(salary);
                        updateNum++;
                        successMsg.append("<br/>第 " + (successNum + failureNum + skipNum + 1) + " 条数据更新成功");
                    } else {
                        skipNum++;  // 计入跳过数量
                        successMsg.append("<br/>第 " + (successNum + failureNum + skipNum) + " 条数据已存在，已跳过");
                        continue;
                    }
                } else {
                    employeeSalaryMapper.insertEmployeeSalary(salary);
                    successNum++;
                    successMsg.append("<br/>第 " + (successNum + failureNum + skipNum) + " 条数据导入成功");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>第 " + (successNum + failureNum + skipNum) + " 条数据导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }

        StringBuilder resultMsg = new StringBuilder();
        if (failureNum > 0) {
            // 有失败的数据时
            resultMsg.append("导入结果：<br/>");
            resultMsg.append("共处理 " + (successNum + failureNum + updateNum + skipNum) + " 条数据<br/>");
            if (successNum > 0) {
                resultMsg.append("新增成功 " + successNum + " 条<br/>");
            }
            if (updateNum > 0) {
                resultMsg.append("更新成功 " + updateNum + " 条<br/>");
            }
            if (skipNum > 0) {
                resultMsg.append("已跳过 " + skipNum + " 条<br/>");
            }
            resultMsg.append("失败 " + failureNum + " 条<br/>");
            resultMsg.append("失败详情如下：" + failureMsg);
        } else {
            // 全部成功时
            resultMsg.append("恭喜您，数据已全部处理成功！<br/>");
            resultMsg.append("共处理 " + (successNum + updateNum + skipNum) + " 条，其中：<br/>");
            if (successNum > 0) {
                resultMsg.append("新增 " + successNum + " 条<br/>");
            }
            if (updateNum > 0) {
                resultMsg.append("更新 " + updateNum + " 条<br/>");
            }
            if (skipNum > 0) {
                resultMsg.append("跳过 " + skipNum + " 条<br/>");
            }
        }
        return resultMsg.toString();
    }
}
