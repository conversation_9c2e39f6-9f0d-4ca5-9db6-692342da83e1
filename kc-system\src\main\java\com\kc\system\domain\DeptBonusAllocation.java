package com.kc.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 部门奖金分配对象 dept_bonus_allocation
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class DeptBonusAllocation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称(冗余) */
    @Excel(name = "部门名称")
    private String deptName;

    /** 分配月份 格式：yyyy-MM */
    @Excel(name = "分配月份")
    private String allocationMonth;

    /** 绩效排名(1为最好) */
    @Excel(name = "绩效排名")
    private Integer performanceRank;

    /** 部门总奖金(可为负数) */
    @Excel(name = "部门总奖金")
    private BigDecimal totalBonus;

    /** 已分配奖金 */
    @Excel(name = "已分配奖金")
    private BigDecimal allocatedBonus;

    /** 剩余奖金 */
    @Excel(name = "剩余奖金")
    private BigDecimal remainingBonus;

    /** 分配状态(0-未分配,1-部分分配,2-已完成) */
    @Excel(name = "分配状态", readConverterExp = "0=未分配,1=部分分配,2=已完成")
    private String allocationStatus;

    /** 分配人ID(薪酬考核人员) */
    private Long allocatorId;

    /** 分配人姓名 */
    @Excel(name = "分配人姓名")
    private String allocatorName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setAllocationMonth(String allocationMonth) 
    {
        this.allocationMonth = allocationMonth;
    }

    public String getAllocationMonth() 
    {
        return allocationMonth;
    }
    public void setPerformanceRank(Integer performanceRank) 
    {
        this.performanceRank = performanceRank;
    }

    public Integer getPerformanceRank() 
    {
        return performanceRank;
    }
    public void setTotalBonus(BigDecimal totalBonus) 
    {
        this.totalBonus = totalBonus;
    }

    public BigDecimal getTotalBonus() 
    {
        return totalBonus;
    }
    public void setAllocatedBonus(BigDecimal allocatedBonus) 
    {
        this.allocatedBonus = allocatedBonus;
    }

    public BigDecimal getAllocatedBonus() 
    {
        return allocatedBonus;
    }
    public void setRemainingBonus(BigDecimal remainingBonus) 
    {
        this.remainingBonus = remainingBonus;
    }

    public BigDecimal getRemainingBonus() 
    {
        return remainingBonus;
    }
    public void setAllocationStatus(String allocationStatus) 
    {
        this.allocationStatus = allocationStatus;
    }

    public String getAllocationStatus() 
    {
        return allocationStatus;
    }
    public void setAllocatorId(Long allocatorId) 
    {
        this.allocatorId = allocatorId;
    }

    public Long getAllocatorId() 
    {
        return allocatorId;
    }
    public void setAllocatorName(String allocatorName) 
    {
        this.allocatorName = allocatorName;
    }

    public String getAllocatorName() 
    {
        return allocatorName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("allocationMonth", getAllocationMonth())
            .append("performanceRank", getPerformanceRank())
            .append("totalBonus", getTotalBonus())
            .append("allocatedBonus", getAllocatedBonus())
            .append("remainingBonus", getRemainingBonus())
            .append("allocationStatus", getAllocationStatus())
            .append("allocatorId", getAllocatorId())
            .append("allocatorName", getAllocatorName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
