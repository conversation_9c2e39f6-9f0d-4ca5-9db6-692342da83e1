package com.kc.system.mapper;

import java.util.List;
import java.util.Map;
import com.kc.system.domain.ProjectSalaryDetail;
import org.apache.ibatis.annotations.Param;

/**
 * 项目劳务费构成Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface ProjectSalaryDetailMapper 
{
    /**
     * 查询项目劳务费构成
     * 
     * @param id 项目劳务费构成主键
     * @return 项目劳务费构成
     */
    public ProjectSalaryDetail selectProjectSalaryDetailById(Long id);

    /**
     * 查询项目劳务费构成列表
     * 
     * @param projectSalaryDetail 项目劳务费构成
     * @return 项目劳务费构成集合
     */
    public List<ProjectSalaryDetail> selectProjectSalaryDetailList(ProjectSalaryDetail projectSalaryDetail);

    /**
     * 新增项目劳务费构成
     * 
     * @param projectSalaryDetail 项目劳务费构成
     * @return 结果
     */
    public int insertProjectSalaryDetail(ProjectSalaryDetail projectSalaryDetail);

    /**
     * 修改项目劳务费构成
     * 
     * @param projectSalaryDetail 项目劳务费构成
     * @return 结果
     */
    public int updateProjectSalaryDetail(ProjectSalaryDetail projectSalaryDetail);

    /**
     * 删除项目劳务费构成
     * 
     * @param id 项目劳务费构成主键
     * @return 结果
     */
    public int deleteProjectSalaryDetailById(Long id);

    /**
     * 批量删除项目劳务费构成
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectSalaryDetailByIds(Long[] ids);

    /**
     * 根据项目ID、用户名和月份查询记录
     * 
     * @param projectId 项目ID
     * @param userName 用户名
     * @param workMonth 工作月份
     * @return 劳务费明细
     */
    public ProjectSalaryDetail selectByProjectIdAndUserNameAndMonth(
        @Param("projectId") Long projectId,
        @Param("userName") String userName,
        @Param("workMonth") String workMonth
    );

    /**
     * 根据项目ID和工作月份查询劳务费明细
     */
    public List<Map<String, Object>> selectProjectSalaryDetailsByProjectIdAndMonth(
        @Param("projectId") Long projectId, 
        @Param("workMonth") String workMonth
    );
}
