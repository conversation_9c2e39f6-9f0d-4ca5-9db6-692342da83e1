<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.ProjectSalaryDetailMapper">
    
    <resultMap type="ProjectSalaryDetail" id="ProjectSalaryDetailResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="userName"    column="user_name"    />
        <result property="workMonth"    column="work_month"    />
        <result property="salaryAmount"    column="salary_amount"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProjectSalaryDetailVo">
        select id, project_id, user_name, work_month, salary_amount, create_time, update_time from project_salary_detail
    </sql>

    <select id="selectProjectSalaryDetailList" parameterType="ProjectSalaryDetail" resultMap="ProjectSalaryDetailResult">
        <include refid="selectProjectSalaryDetailVo"/>
        <where>  
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="workMonth != null  and workMonth != ''"> and work_month = #{workMonth}</if>
            <if test="salaryAmount != null "> and salary_amount = #{salaryAmount}</if>
        </where>
    </select>
    
    <select id="selectProjectSalaryDetailById" parameterType="Long" resultMap="ProjectSalaryDetailResult">
        <include refid="selectProjectSalaryDetailVo"/>
        where id = #{id}
    </select>

    <insert id="insertProjectSalaryDetail" parameterType="ProjectSalaryDetail" useGeneratedKeys="true" keyProperty="id">
        insert into project_salary_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="workMonth != null and workMonth != ''">work_month,</if>
            <if test="salaryAmount != null">salary_amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="workMonth != null and workMonth != ''">#{workMonth},</if>
            <if test="salaryAmount != null">#{salaryAmount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProjectSalaryDetail" parameterType="ProjectSalaryDetail">
        update project_salary_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="workMonth != null and workMonth != ''">work_month = #{workMonth},</if>
            <if test="salaryAmount != null">salary_amount = #{salaryAmount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectSalaryDetailById" parameterType="Long">
        delete from project_salary_detail where id = #{id}
    </delete>

    <delete id="deleteProjectSalaryDetailByIds" parameterType="String">
        delete from project_salary_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据项目ID、用户名和月份查询记录 -->
    <select id="selectByProjectIdAndUserNameAndMonth" resultMap="ProjectSalaryDetailResult">
        select id, project_id, user_name, work_month, salary_amount, create_time, update_time 
        from project_salary_detail
        where project_id = #{projectId}
        and user_name = #{userName}
        and work_month = #{workMonth}
        limit 1
    </select>

    <!-- 根据项目ID和工作月份查询劳务费明细 -->
    <select id="selectProjectSalaryDetailsByProjectIdAndMonth" resultType="java.util.Map">
        SELECT DISTINCT
            d.id,
            d.project_id,
            d.user_name,
            d.work_month,
            d.salary_amount,
            d.create_time,
            d.update_time,
            p.project_name,
            u.nick_name,
            m.role
        FROM 
            project_salary_detail d
            LEFT JOIN project_info p ON d.project_id = p.id
            LEFT JOIN sys_user u ON d.user_name = u.user_name
            LEFT JOIN project_members m ON d.project_id = m.project_id AND d.user_name = m.user_name
        WHERE 
            d.project_id = #{projectId}
            AND d.work_month = #{workMonth}
    </select>
</mapper>