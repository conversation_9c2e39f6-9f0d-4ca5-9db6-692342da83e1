package com.kc.system.service;

import java.util.List;
import java.util.Map;
import com.kc.system.domain.ProjectMembers;

/**
 * 项目成员关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-14
 */
public interface IProjectMembersService 
{
    /**
     * 查询项目成员关联
     * 
     * @param id 项目成员关联主键
     * @return 项目成员关联
     */
    public ProjectMembers selectProjectMembersById(Long id);

    /**
     * 查询项目成员关联列表
     * 
     * @param projectMembers 项目成员关联
     * @return 项目成员关联集合
     */
    public List<ProjectMembers> selectProjectMembersList(ProjectMembers projectMembers);

    /**
     * 新增项目成员关联
     * 
     * @param projectMembers 项目成员关联
     * @return 结果
     */
    public int insertProjectMembers(ProjectMembers projectMembers);

    /**
     * 修改项目成员关联
     * 
     * @param projectMembers 项目成员关联
     * @return 结果
     */
    public int updateProjectMembers(ProjectMembers projectMembers);

    /**
     * 批量删除项目成员关联
     * 
     * @param ids 需要删除的项目成员关联主键集合
     * @return 结果
     */
    public int deleteProjectMembersByIds(Long[] ids);

    /**
     * 删除项目成员关联信息
     * 
     * @param id 项目成员关联主键
     * @return 结果
     */
    public int deleteProjectMembersById(Long id);

    /**
     * 根据项目ID和用户名查询项目成员
     */
    public ProjectMembers selectProjectMemberByUserName(Long projectId, String userName);
    
    /**
     * 获取用户参与的所有项目
     * 
     * @param userName 用户名
     * @return 项目列表
     */
    public List<Map<String, Object>> selectUserProjects(String userName);

    /**
     * 获取部门下有项目的用户及其项目信息
     * 
     * @param deptIds 部门ID列表
     * @return 有项目的用户及其项目信息
     */
    public List<Map<String, Object>> selectDeptMembersWithProjects(List<Long> deptIds);
}
