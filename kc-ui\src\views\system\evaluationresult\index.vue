<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="员工编号" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入员工编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
            <el-form-item label="部门名称" prop="deptId">        <el-select          v-model="queryParams.deptId"          placeholder="请选择部门"          clearable          style="width: 100%"        >          <el-option            v-for="dept in deptOptions"            :key="dept.deptId"            :label="dept.deptName"            :value="dept.deptId"          />        </el-select>      </el-form-item>
      <el-form-item label="评价月份" prop="evaluationMonth">
        <el-date-picker
          v-model="queryParams.evaluationMonth"
          type="month"
          value-format="yyyy-MM"
          placeholder="请选择评价月份"
          clearable>
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="最终评分" prop="finalScore">
        <el-input
          v-model="queryParams.finalScore"
          placeholder="请输入最终评分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="机构负责人评分" prop="managerScore">
        <el-input
          v-model="queryParams.managerScore"
          placeholder="请输入机构负责人评分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="项目负责人平均评分" prop="projectLeaderScore">
        <el-input
          v-model="queryParams.projectLeaderScore"
          placeholder="请输入项目负责人平均评分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="用户角色：project_leader-仅项目负责人,project_member-仅项目成员,both-既是负责人又是成员,none-不参与项目" prop="userRole">
        <el-input
          v-model="queryParams.userRole"
          placeholder="请输入用户角色：project_leader-仅项目负责人,project_member-仅项目成员,both-既是负责人又是成员,none-不参与项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="创建时间" prop="createdAt">
        <el-date-picker clearable
          v-model="queryParams.createdAt"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="更新时间" prop="updatedAt">
        <el-date-picker clearable
          v-model="queryParams.updatedAt"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择更新时间">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 评分控制开关 -->
    <el-card class="evaluation-control-card" style="margin-bottom: 20px;">
      <div slot="header" class="clearfix">
        <span style="font-weight: bold;">考核评分控制</span>
      </div>
      <div class="control-content">
        <el-row :gutter="20" type="flex" align="middle">
          <el-col :span="6">
            <div class="control-item">
              <span class="control-label">评分功能状态：</span>
              <el-tag :type="evaluationEnabled ? 'success' : 'danger'" size="medium">
                {{ evaluationEnabled ? '已开启' : '已关闭' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="control-item">
              <el-switch
                v-model="evaluationEnabled"
                :loading="switchLoading"
                active-text="开启评分"
                inactive-text="关闭评分"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="handleEvaluationToggle">
              </el-switch>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="control-description">
              <el-alert
                :title="evaluationEnabled ? '评分功能已开启，用户可以进行项目负责人评分和机构成员评分' : '评分功能已关闭，所有评分操作将被禁用'"
                :type="evaluationEnabled ? 'success' : 'warning'"
                :closable="false"
                show-icon>
              </el-alert>
            </div>
          </el-col>

        </el-row>
      </div>
    </el-card>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:evaluation_result:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:evaluation_result:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:evaluation_result:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:evaluation_result:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportQuarterlyParticipation"
        >导出季度精力分配</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExportQuarterlyEvaluation"
        >导出季度评价</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="evaluation_resultList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键ID" align="center" prop="id" /> -->
      <!-- <el-table-column label="用户ID" align="center" prop="userId" /> -->
      <el-table-column label="员工编号" align="center" prop="userName" />
      <el-table-column label="姓名" align="center" prop="nickName" />
      <el-table-column label="部门名称" align="center" prop="deptName" />
      <el-table-column label="评价月份" align="center" prop="evaluationMonth" />
      <el-table-column label="项目负责人平均评分" align="center" prop="projectLeaderScore" />
      <el-table-column label="机构负责人评分" align="center" prop="managerScore" />
      <el-table-column label="最终评分" align="center" prop="finalScore" />

      <!-- <el-table-column label="用户角色" align="center" prop="userRole" /> -->
      <!-- <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updatedAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
          >计算明细</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改评价结果对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="员工编号" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入员工编号" />
        </el-form-item>
        <el-form-item label="姓名" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入员工姓名" />
        </el-form-item>
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" placeholder="请输入部门ID" />
        </el-form-item>
        <el-form-item label="评价月份" prop="evaluationMonth">
          <el-date-picker
            v-model="form.evaluationMonth"
            type="month"
            value-format="yyyy-MM"
            placeholder="请选择评价月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="最终评分" prop="finalScore">
          <el-input v-model="form.finalScore" placeholder="请输入最终评分" />
        </el-form-item>
        <el-form-item label="机构负责人评分" prop="managerScore">
          <el-input v-model="form.managerScore" placeholder="请输入机构负责人评分" />
        </el-form-item>
        <el-form-item label="项目负责人平均评分" prop="projectLeaderScore">
          <el-input v-model="form.projectLeaderScore" placeholder="请输入项目负责人平均评分" />
        </el-form-item>
        <el-form-item label="用户角色：project_leader-仅项目负责人,project_member-仅项目成员,both-既是负责人又是成员,none-不参与项目" prop="userRole">
          <el-input v-model="form.userRole" placeholder="请输入用户角色：project_leader-仅项目负责人,project_member-仅项目成员,both-既是负责人又是成员,none-不参与项目" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createdAt">
          <el-date-picker clearable
            v-model="form.createdAt"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新时间" prop="updatedAt">
          <el-date-picker clearable
            v-model="form.updatedAt"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择更新时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 计算明细对话框 -->
    <el-dialog :title="'评分计算明细 - ' + (detailData.nickName || '')" :visible.sync="detailOpen" width="800px" append-to-body>
      <div v-loading="detailLoading">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="精力分配情况" name="effort">
            <el-table
              :data="detailData.effortList || []"
              border
              stripe
              style="width: 100%">
              <el-table-column label="项目名称" prop="projectName" min-width="180" />
              <el-table-column label="精力分配" prop="participationRate" min-width="100">
                <template slot-scope="scope">
                  {{ scope.row.participationRate * 100 + '%' }}
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="comments" min-width="150" />
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="项目负责人评分情况" name="projectLeader">
            <el-table
              :data="detailData.projectLeaderScoreList || []"
              border
              stripe
              style="width: 100%">
              <el-table-column label="项目名称" prop="projectName" min-width="180" />
              <el-table-column label="项目负责人" prop="evaluatorName" min-width="100" />
              <el-table-column label="评分" prop="score" min-width="80" />
              <el-table-column label="精力分配" prop="participationRate" min-width="100">
                <template slot-scope="scope">
                  {{ scope.row.participationRate * 100 + '%' }}
                </template>
              </el-table-column>
              <el-table-column label="权重后得分" min-width="100">
                <template slot-scope="scope">
                  {{ (scope.row.score * scope.row.participationRate).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="评价日期" prop="createdAt" min-width="120" />
            </el-table>
            <div class="detail-summary" v-if="detailData.projectLeaderScoreList && detailData.projectLeaderScoreList.length > 0">
              <span>项目负责人评分加权平均值: </span>
              <span class="score">{{ detailData.projectLeaderScore }}</span>
              <span> ({{ detailData.projectLeaderScoreFormula || '暂无公式' }})</span>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="机构负责人评分情况" name="manager">
            <el-table
              :data="detailData.managerScoreList || []"
              border
              stripe
              style="width: 100%">
              <!-- <el-table-column label="评价人" prop="evaluatorName" min-width="100" /> -->
              <el-table-column label="评分" prop="score" min-width="80" />
              <el-table-column label="评价日期" prop="createdAt" min-width="120" />
              <el-table-column label="备注" prop="comments" min-width="150" />
            </el-table>
            <div class="detail-summary" v-if="detailData.managerScoreList && detailData.managerScoreList.length > 0">
              <span>机构负责人评分: </span>
              <span class="score">{{ detailData.managerScore }}</span>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="最终得分计算" name="final">
            <div class="detail-formula">
              <p class="formula-title">计算公式:</p>
              <p class="formula-content" v-if="detailData.userRole === 'project_member' || detailData.userRole === 'both'">
                最终得分 = 机构负责人评分 × 60% + 项目负责人评分 × 40%
              </p>
              <p class="formula-content" v-else-if="detailData.userRole === 'project_leader' || detailData.userRole === 'none'">
                最终得分 = 机构负责人评分
              </p>
              <p class="formula-content" v-else>
                未知计算公式
              </p>
              
              <p class="formula-calc" v-if="detailData.userRole === 'project_member' || detailData.userRole === 'both'">
                最终得分 = {{ detailData.managerScore }} × 60% + {{ detailData.projectLeaderScore }} × 40% = {{ detailData.finalScore }}
              </p>
              <p class="formula-calc" v-else-if="detailData.userRole === 'project_leader' || detailData.userRole === 'none'">
                最终得分 = {{ detailData.managerScore }} = {{ detailData.finalScore }}
              </p>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 项目负责人评分编辑对话框 -->
    <el-dialog :title="scoreTitle" :visible.sync="scoreOpen" width="500px" append-to-body>
      <el-form ref="scoreForm" :model="scoreForm" :rules="scoreRules" label-width="100px">
        <el-form-item label="项目名称">
          <span>{{ scoreForm.projectName }}</span>
        </el-form-item>
        <el-form-item label="被评价人">
          <span>{{ scoreForm.evaluateeName || detailData.nickName }}</span>
        </el-form-item>
        <el-form-item label="评价月份">
          <span>{{ scoreForm.evaluationMonth || detailData.evaluationMonth }}</span>
        </el-form-item>
        <el-form-item label="评分" prop="score">
          <el-input-number
            v-model="scoreForm.score"
            :min="0"
            :max="100"
            controls-position="right">
          </el-input-number>
          <span class="score-unit">分</span>
          <span class="score-limit">(0-100)</span>
        </el-form-item>
        <!-- <el-form-item label="评价意见" prop="comments">
          <el-input
            type="textarea"
            v-model="scoreForm.comments"
            placeholder="请输入评价意见"
            maxlength="500"
            show-word-limit
            :rows="4">
          </el-input>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitScoreForm">确 定</el-button>
        <el-button @click="cancelScore">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listEvaluation_result, getEvaluation_result, delEvaluation_result, addEvaluation_result, updateEvaluation_result, getEvaluationDetail } from "@/api/system/evaluation_result";
import { listDept } from "@/api/system/dept";
import { updateParticipation, delParticipation, listParticipation, listAllParticipation } from "@/api/system/participation";
import { updateEvaluation, delEvaluation, listEvaluation, exportDeptEvaluationData } from "@/api/system/evaluation";
import { getConfigKey, updateConfigByKey } from "@/api/system/config";
import { listUser } from "@/api/system/user";
import request from '@/utils/request';
import * as XLSX from 'xlsx';

export default {
  name: "Evaluation_result",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评价结果表格数据
      evaluation_resultList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        userName: null,
        nickName: null,
        deptId: null,
        evaluationMonth: null,
        finalScore: null,
        managerScore: null,
        projectLeaderScore: null,
        userRole: null,
        createdAt: null,
        updatedAt: null
      },
      // 表单参数
      form: {
        id: null,
        userId: null,
        userName: null,
        nickName: null,
        deptId: null,
        evaluationMonth: null,
        finalScore: null,
        managerScore: null,
        projectLeaderScore: null,
        userRole: null,
        createdAt: null,
        updatedAt: null
      },
      // 表单校验
      rules: {
        userId: [
          { required: true, message: "用户ID不能为空", trigger: "blur" }
        ],
        userName: [
          { required: true, message: "员工编号不能为空", trigger: "blur" }
        ],
        nickName: [
          { required: false, message: "员工姓名不能为空", trigger: "blur" }
        ],
        deptId: [
          { required: true, message: "部门ID不能为空", trigger: "blur" }
        ],
        evaluationMonth: [
          { required: true, message: "评价月份 格式：yyyy-MM不能为空", trigger: "blur" }
        ],
        finalScore: [
          { required: true, message: "最终评分不能为空", trigger: "blur" }
        ],
        userRole: [
          { required: true, message: "用户角色：project_leader-仅项目负责人,project_member-仅项目成员,both-既是负责人又是成员,none-不参与项目不能为空", trigger: "blur" }
        ],
        createdAt: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updatedAt: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ]
      },
      // 部门选项
      deptOptions: [],
      // 计算明细对话框是否显示
      detailOpen: false,
      // 计算明细加载状态
      detailLoading: false,
      // 明细数据
      detailData: {},
      // 当前激活的Tab页
      activeTab: 'effort',
      
      // 项目负责人评分编辑相关
      scoreOpen: false,
      scoreTitle: '',
      scoreForm: {
        id: null,
        projectId: null,
        projectName: '',
        evaluatorId: null,
        evaluateeId: null,
        evaluateeName: '',
        score: 80,
        evaluationMonth: '',
        evaluationType: 'project_leader',
        comments: ''
      },
      scoreRules: {
        score: [
          { required: true, message: "评分不能为空", trigger: "change" }
        ]
      },

      // 评分控制相关
      evaluationEnabled: true, // 评分功能是否开启
      switchLoading: false, // 开关加载状态
    };
  },
  created() {
    // 设置当前月份为默认评价月份
    const date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    this.queryParams.evaluationMonth = `${year}-${month}`;

    this.getList();
    this.getDeptList();
    this.loadEvaluationStatus();
  },
  methods: {
    /** 查询评价结果列表 */
    getList() {
      this.loading = true;
      listEvaluation_result(this.queryParams).then(response => {
        this.evaluation_resultList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询部门下拉列表 */
    getDeptList() {
      // 系统会自动应用数据权限过滤
      // SysDeptServiceImpl的selectDeptList方法上有@DataScope注解
      // 这样用户只能看到有权限的部门数据
      listDept().then(response => {
        this.deptOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        userId: null,
        userName: null,
        nickName: null,
        deptId: null,
        evaluationMonth: null,
        finalScore: null,
        managerScore: null,
        projectLeaderScore: null,
        userRole: null,
        createdAt: null,
        updatedAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加评价结果";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getEvaluation_result(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改评价结果";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateEvaluation_result(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEvaluation_result(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除评价结果编号为"' + ids + '"的数据项？').then(function() {
        return delEvaluation_result(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$confirm('请选择导出数据范围', '导出确认', {
        confirmButtonText: '导出全部',
        cancelButtonText: '仅导出当前页',
        distinguishCancelAndClose: true,
        type: 'info',
        closeOnClickModal: false
      }).then(() => {
        // 导出所有数据
        this.download('system/evaluation_result/export', {
          ...this.queryParams
        }, `evaluation_result_${new Date().getTime()}.xlsx`);
      }).catch((action) => {
        if (action === 'cancel') {
          // 导出当前页数据
          const exportCurrentPageParams = {
            ...this.queryParams,
            pageNum: this.queryParams.pageNum,
            pageSize: this.queryParams.pageSize,
            exportCurrentPage: true
          };
          
          this.download('system/evaluation_result/export', exportCurrentPageParams, 
            `evaluation_result_current_page_${new Date().getTime()}.xlsx`);
        }
      });
    },

    /** 导出季度精力分配 */
    async handleExportQuarterlyParticipation() {
      this.$confirm('确认导出2022-08季度精力分配数据？', '导出确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        try {
          this.$modal.loading("正在导出数据，请稍候...");

          // 1. 获取精力分配数据（使用不分页的API获取所有数据）
          const participationResponse = await listAllParticipation({ month: '2022-08' });
          const participationList = participationResponse.data || [];

          // 2. 获取所有用户数据（用于用户名转姓名）
          const userResponse = await listUser({ pageSize: 10000 }); // 确保获取所有用户
          const userList = userResponse.rows || [];
          const userMap = {};
          userList.forEach(user => {
            userMap[user.userName] = user.nickName || user.userName;
          });

          // 调试信息：检查用户映射
          console.log('用户映射表:', userMap);
          console.log('参与度数据示例:', participationList.slice(0, 2));

          // 3. 获取所有部门数据（用于部门ID转部门名称）
          const deptMap = {};
          this.deptOptions.forEach(dept => {
            deptMap[dept.deptId] = dept.deptName;
          });

          // 4. 转换数据格式
          const exportData = participationList.map(item => {
            const mappedName = userMap[item.userName];
            console.log(`用户名 ${item.userName} 映射到姓名: ${mappedName}`);

            return {
              '员工用户名': item.userName,
              '员工姓名': mappedName || item.userName,
              '项目名称': item.projectName,
              '项目考核系数': item.participationRate,
              '记录月份': '第二季度',
              '部门名称': deptMap[item.deptId] || item.deptId,
              '分配人用户名': item.assignerName,
              '备注': item.comments || '',
              '创建时间': item.createdAt ? this.parseTime(item.createdAt, '{y}-{m}-{d}') : '',
              '更新时间': item.updatedAt ? this.parseTime(item.updatedAt, '{y}-{m}-{d}') : ''
            };
          });

          console.log('导出数据示例:', exportData.slice(0, 2));

          // 5. 导出Excel
          this.exportToExcel(exportData, `季度精力分配_2022-08_${new Date().getTime()}`);

        } catch (error) {
          this.$modal.msgError("导出失败：" + error.message);
        } finally {
          this.$modal.closeLoading();
        }
      }).catch(() => {});
    },

    /** 导出季度评价 */
    async handleExportQuarterlyEvaluation() {
      this.$confirm('确认导出2022-08季度评价数据？', '导出确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        try {
          this.$modal.loading("正在导出数据，请稍候...");

          // 1. 获取季度评价数据（查询所有季度评价类型，设置大分页确保获取所有数据）
          const [managerResponse, parentManagerResponse] = await Promise.all([
            listEvaluation({
              evaluationMonth: '2022-08',
              evaluationType: 'quarterly_manager',
              pageSize: 10000  // 设置大分页确保获取所有数据
            }),
            listEvaluation({
              evaluationMonth: '2022-08',
              evaluationType: 'quarterly_parent_manager',
              pageSize: 10000  // 设置大分页确保获取所有数据
            })
          ]);

          const evaluationList = [
            ...(managerResponse.rows || []),
            ...(parentManagerResponse.rows || [])
          ];

          console.log('获取到的季度评价数据总数:', evaluationList.length);
          console.log('季度评价数据示例（前3条）:', evaluationList.slice(0, 3));

          console.log('季度评价数据示例:', evaluationList.slice(0, 2));

          // 2. 获取所有用户数据（用于用户ID转姓名和部门）
          const userResponse = await listUser({ pageSize: 10000 });
          const userList = userResponse.rows || [];
          const userIdMap = {};  // 用户ID映射姓名
          const userNameMap = {}; // 用户名映射姓名
          const userDeptMap = {}; // 用户ID映射部门ID
          userList.forEach(user => {
            userIdMap[user.userId] = user.nickName || user.userName;
            userNameMap[user.userName] = user.nickName || user.userName;
            userDeptMap[user.userId] = user.deptId; // 存储用户的部门ID
          });

          console.log('用户ID映射示例:', Object.keys(userIdMap).slice(0, 5));
          console.log('用户部门映射示例:', Object.keys(userDeptMap).slice(0, 5));
          console.log('用户部门映射数据示例:', Object.fromEntries(Object.entries(userDeptMap).slice(0, 3)));

          // 3. 获取所有部门数据（用于部门ID转部门名称）
          const deptResponse = await listDept({});
          const deptList = deptResponse.data || [];
          const deptMap = {};
          deptList.forEach(dept => {
            deptMap[dept.deptId] = dept.deptName;
          });

          console.log('部门映射示例:', Object.keys(deptMap).slice(0, 5));
          console.log('部门映射数据:', deptMap);

          // 4. 逐个查询用户详细信息并转换数据格式
          const exportData = [];

          // 更新加载提示
          this.$modal.closeLoading();
          this.$modal.loading(`正在处理数据，共${evaluationList.length}条记录...`);

          for (let i = 0; i < evaluationList.length; i++) {
            const item = evaluationList[i];
            console.log(`处理第${i+1}/${evaluationList.length}条评价记录:`, item);

            // 更新进度提示
            if (i % 5 === 0 || i === evaluationList.length - 1) {
              this.$modal.closeLoading();
              this.$modal.loading(`正在处理数据 ${i+1}/${evaluationList.length}...`);
            }

            // 根据数据结构确定如何获取用户姓名
            let evaluateeName = '未知用户';
            let evaluatorName = '未知评价人';
            let deptName = '未知部门';

            // 获取被评价人信息
            if (item.evaluateeId) {
              try {
                const evaluateeResponse = await this.getUserById(item.evaluateeId);
                if (evaluateeResponse && evaluateeResponse.data) {
                  const evaluateeUser = evaluateeResponse.data;
                  evaluateeName = evaluateeUser.nickName || evaluateeUser.userName || '未知用户';
                  // 通过用户的部门ID获取部门名称
                  if (evaluateeUser.deptId && deptMap[evaluateeUser.deptId]) {
                    deptName = deptMap[evaluateeUser.deptId];
                  }
                  console.log(`被评价人: ${evaluateeName}, 部门: ${deptName}`);
                }
              } catch (error) {
                console.error(`获取被评价人信息失败 (ID: ${item.evaluateeId}):`, error);
              }
            }

            // 获取评价人信息
            if (item.evaluatorId) {
              try {
                const evaluatorResponse = await this.getUserById(item.evaluatorId);
                if (evaluatorResponse && evaluatorResponse.data) {
                  const evaluatorUser = evaluatorResponse.data;
                  evaluatorName = evaluatorUser.nickName || evaluatorUser.userName || '未知评价人';
                  console.log(`评价人: ${evaluatorName}`);
                }
              } catch (error) {
                console.error(`获取评价人信息失败 (ID: ${item.evaluatorId}):`, error);
              }
            }

            // 根据是否有项目信息决定评价类型和评价意见
            let evaluationType = '';
            let comments = '';

            // 调试信息：检查项目信息
            console.log(`处理评价记录 - 项目ID: "${item.projectId}", 项目名称: "${item.projectName}", 评价类型: "${item.evaluationType}"`);
            console.log('完整的item对象:', JSON.stringify(item, null, 2));

            // 判断是否有项目信息
            // 检查项目ID（可能是数字或字符串，不为空、不为0、不为"0"、不为null、不为undefined）
            const hasProjectId = item.projectId &&
                                 item.projectId !== 0 &&
                                 item.projectId !== '0' &&
                                 item.projectId !== '' &&
                                 String(item.projectId).trim() !== '';

            // 检查项目名称（不为空、不为null、不为undefined）
            const hasProjectName = item.projectName &&
                                   item.projectName !== '' &&
                                   String(item.projectName).trim() !== '';

            console.log(`项目ID检查结果: ${hasProjectId}, 项目名称检查结果: ${hasProjectName}`);

            if (hasProjectId || hasProjectName) {
              // 有项目信息，显示为季度项目负责人评分
              evaluationType = '季度项目负责人评分';
              comments = '季度评价';
              console.log('✓ 设置为季度项目负责人评分');
            } else {
              // 没有项目信息，按原来的逻辑显示
              evaluationType = item.evaluationType === 'quarterly_manager' ? '季度机构负责人评价' :
                              item.evaluationType === 'quarterly_parent_manager' ? '季度父部门负责人评价' :
                              item.evaluationType === 'manager' ? '机构负责人评价' :
                              item.evaluationType === 'leader' ? '项目负责人评价' : item.evaluationType;
              comments = item.comments || '';
              console.log('✗ 设置为其他评价类型:', evaluationType);
            }

            exportData.push({
              '被评价员工姓名': evaluateeName,
              '评价人姓名': evaluatorName,
              '评价月份': item.evaluationMonth,
              '评价类型': evaluationType,
              '评分': item.score,
              '评价意见': comments,
              '部门名称': deptName,
              '项目ID': item.projectId || '',
              '项目名称': item.projectName || '',
              '创建时间': item.createTime ? this.parseTime(item.createTime, '{y}-{m}-{d}') : '',
              '更新时间': item.updateTime ? this.parseTime(item.updateTime, '{y}-{m}-{d}') : ''
            });
          }

          console.log('导出数据示例:', exportData.slice(0, 2));

          // 5. 后处理：最终检查和修正评价类型
          exportData.forEach((row, index) => {
            // 如果有项目ID或项目名称，但评价类型不是"季度项目负责人评分"，则修正
            if ((row['项目ID'] && row['项目ID'] !== '' && row['项目ID'] !== '0') ||
                (row['项目名称'] && row['项目名称'] !== '')) {
              if (row['评价类型'] !== '季度项目负责人评分') {
                console.log(`修正第${index + 1}行数据: 项目ID=${row['项目ID']}, 项目名称=${row['项目名称']}`);
                row['评价类型'] = '季度项目负责人评分';
                row['评价意见'] = '季度评价';
              }
            }
          });

          console.log('后处理完成，最终导出数据示例:', exportData.slice(0, 2));

          // 6. 导出Excel
          this.exportToExcel(exportData, `季度评价_2022-08_${new Date().getTime()}`);

        } catch (error) {
          this.$modal.msgError("导出失败：" + error.message);
        } finally {
          this.$modal.closeLoading();
        }
      }).catch(() => {});
    },

    /** 通用Excel导出方法 */
    exportToExcel(data, fileName) {
      if (!data || data.length === 0) {
        this.$modal.msgWarning("没有数据可导出");
        return;
      }

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(data);

      // 设置列宽
      const colWidths = [];
      if (data.length > 0) {
        Object.keys(data[0]).forEach(key => {
          const maxLength = Math.max(
            key.length,
            ...data.map(row => String(row[key] || '').length)
          );
          colWidths.push({ wch: Math.min(Math.max(maxLength, 10), 50) });
        });
      }
      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

      // 导出文件
      XLSX.writeFile(wb, `${fileName}.xlsx`);
    },

    /** 根据用户ID获取用户详细信息 */
    async getUserById(userId) {
      try {
        // 使用用户管理的API获取用户详细信息
        const response = await request({
          url: `/system/user/${userId}`,
          method: 'get'
        });
        return response;
      } catch (error) {
        console.error(`获取用户信息失败 (ID: ${userId}):`, error);
        throw error;
      }
    },

    /** 查看计算明细 */
    handleViewDetail(row) {
      this.detailOpen = true;
      this.detailLoading = true;
      this.detailData = {
        ...row
      };
      this.activeTab = 'effort';
      
      getEvaluationDetail({
        userId: row.userId,
        evaluationMonth: row.evaluationMonth
      }).then(response => {
        this.detailData = {
          ...row,
          ...response.data
        };
        
        this.detailLoading = false;
      }).catch(() => {
        this.detailLoading = false;
        this.$message.error('获取计算明细失败');
      });
    },
    /** 修改精力分配 */
    handleEffortEdit(row) {
      this.$prompt('请输入精力分配百分比', '修改精力分配', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^(100|[1-9]?[0-9])$/,
        inputValue: row.participationRate * 100,
        inputErrorMessage: '请输入1-100的整数'
      }).then(({ value }) => {
        this.detailLoading = true;
        // 调用实际API修改精力分配
        updateParticipation({
          id: row.id,
          participationRate: value / 100
        }).then(response => {
          this.$message.success('修改成功');
          // 重新加载详情数据
          this.handleViewDetail(this.detailData);
        }).catch(error => {
          this.$message.error('修改失败: ' + (error.message || '未知错误'));
          this.detailLoading = false;
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消修改'
        });        
      });
    },
    
    /** 删除精力分配 */
    handleEffortDelete(row) {
      if (!row) {
        this.$message.error('无效的记录');
        return;
      }
      
      // 确认删除
      this.$confirm(`确认删除"${row.projectName || '未知'}"项目的精力分配记录吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.detailLoading = true;
        
        // 检查是否有ID字段
        if (row.id) {
          // 直接使用ID删除
          delParticipation(row.id)
            .then(response => {
              this.$message.success('删除成功');
              // 重新加载详情数据
              this.handleViewDetail(this.detailData);
            })
            .catch(error => {
              this.$message.error('删除失败: ' + (error.message || '未知错误'));
            })
            .finally(() => {
              this.detailLoading = false;
            });
        } 
        else if (typeof row.projectId !== 'undefined') {
          // 没有ID字段，使用组合参数删除
          const projectId = row.projectId;
          const userName = this.detailData.userName;
          const month = this.detailData.evaluationMonth;
          
          // 使用新添加的deleteByParams接口
          request({
            url: '/system/participation/deleteByParams',
            method: 'delete',
            params: {
              userName: userName,
              projectId: projectId,
              month: month
            }
          })
            .then(response => {
              this.$message.success('删除成功');
              // 重新加载详情数据
              this.handleViewDetail(this.detailData);
            })
            .catch(error => {
              // 如果后端返回404，说明接口不存在
              if (error.response && error.response.status === 404) {
                this.$alert(
                  '系统未提供按组合参数删除的接口。请联系系统管理员升级后端服务。',
                  '功能不可用',
                  {
                    confirmButtonText: '我知道了',
                    type: 'warning'
                  }
                );
              } else {
                this.$message.error('删除失败: ' + (error.message || '未知错误'));
              }
            })
            .finally(() => {
              this.detailLoading = false;
            });
        }
        else {
          this.detailLoading = false;
          this.$message.error('无法识别记录ID或项目ID');
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });          
      });
    },
    
    /** 修改项目负责人评分 */
    handleLeaderScoreEdit(row) {
      // 检查评分记录是否有效
      if (!row) {
        this.$message.error('无效的评分记录');
        return;
      }
      
      // 获取评分记录ID
      // 由于后端在ProjectLeaderScoreDTO中没有设置id字段，我们需要通过API查询获取
      this.detailLoading = true;
      
      // 构建查询参数
      const params = {
        projectId: row.projectId,
        evaluateeId: this.detailData.userId,
        evaluationMonth: this.detailData.evaluationMonth,
        evaluationType: 'project_leader'
      };
      
      // 查询评分记录
      listEvaluation(params).then(response => {
        if (response.rows && response.rows.length > 0) {
          // 找到匹配的记录
          const record = response.rows.find(item => 
            item.projectId === row.projectId && 
            item.evaluateeId === this.detailData.userId
          );
          
          if (record) {
            // 构建评分表单
            this.scoreForm = {
              id: record.id,
              projectId: row.projectId,
              projectName: row.projectName,
              evaluatorId: row.evaluatorId,
              evaluateeId: this.detailData.userId,
              evaluateeName: this.detailData.nickName,
              score: row.score,
              evaluationMonth: this.detailData.evaluationMonth,
              evaluationType: 'project_leader',
              comments: row.comments || record.comments || ''
            };
            
            this.scoreTitle = "修改项目负责人评分";
            this.scoreOpen = true;
          } else {
            this.$message.error('未找到对应的评分记录');
          }
        } else {
          this.$message.error('未找到评分记录');
        }
        this.detailLoading = false;
      }).catch(error => {
        this.$message.error('获取评分记录失败: ' + (error.message || '未知错误'));
        this.detailLoading = false;
      });
    },
    
    /** 删除项目负责人评分 */
    handleLeaderScoreDelete(row) {
      // 检查评分记录是否有效
      if (!row) {
        this.$message.error('无效的评分记录');
        return;
      }
      
      // 获取评分记录ID
      this.detailLoading = true;
      
      // 构建查询参数
      const params = {
        projectId: row.projectId,
        evaluateeId: this.detailData.userId,
        evaluationMonth: this.detailData.evaluationMonth,
        evaluationType: 'project_leader'
      };
      
      // 查询评分记录
      listEvaluation(params).then(response => {
        if (response.rows && response.rows.length > 0) {
          // 找到匹配的记录
          const record = response.rows.find(item => 
            item.projectId === row.projectId && 
            item.evaluateeId === this.detailData.userId
          );
          
          if (record) {
            // 确认删除
            this.$modal.confirm(`确认删除"${row.projectName || '未知'}"项目的负责人评分记录吗？`).then(() => {
              delEvaluation(record.id)
                .then(response => {
                  this.$message.success('删除成功');
                  // 重新加载详情数据
                  this.handleViewDetail(this.detailData);
                })
                .catch(error => {
                  this.$message.error('删除失败: ' + (error.message || '未知错误'));
                })
                .finally(() => {
                  this.detailLoading = false;
                });
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除'
              });
              this.detailLoading = false;
            });
          } else {
            this.$message.error('未找到对应的评分记录');
            this.detailLoading = false;
          }
        } else {
          this.$message.error('未找到评分记录');
          this.detailLoading = false;
        }
      }).catch(error => {
        this.$message.error('获取评分记录失败: ' + (error.message || '未知错误'));
        this.detailLoading = false;
      });
    },
    
    /** 提交评分表单 */
    submitScoreForm() {
      this.$refs["scoreForm"].validate(valid => {
        if (valid) {
          // 确保分数在0-100范围内
          if (this.scoreForm.score < 0) {
            this.scoreForm.score = 0;
          }
          if (this.scoreForm.score > 100) {
            this.scoreForm.score = 100;
          }
          
          this.detailLoading = true;
          
          updateEvaluation(this.scoreForm)
            .then(response => {
              this.$message.success('修改成功');
              this.scoreOpen = false;
              // 重新加载详情数据
              this.handleViewDetail(this.detailData);
            })
            .catch(error => {
              this.$message.error('修改失败: ' + (error.message || '未知错误'));
            })
            .finally(() => {
              this.detailLoading = false;
            });
        }
      });
    },
    
    /** 取消评分 */
    cancelScore() {
      this.scoreOpen = false;
      this.resetScoreForm();
    },
    
    /** 重置评分表单 */
    resetScoreForm() {
      this.scoreForm = {
        id: null,
        projectId: null,
        projectName: '',
        evaluatorId: null,
        evaluateeId: null,
        evaluateeName: '',
        score: 80,
        evaluationMonth: '',
        evaluationType: 'project_leader',
        comments: ''
      };
      if (this.$refs.scoreForm) {
        this.$refs.scoreForm.resetFields();
      }
    },
    
    /** 修改机构负责人评分 */
    handleManagerScoreEdit(row) {
      // 检查评分记录是否有效
      if (!row) {
        this.$message.error('无效的评分记录');
        return;
      }
      
      // 获取评分记录ID
      this.detailLoading = true;
      
      // 构建查询参数
      const params = {
        evaluateeId: this.detailData.userId,
        evaluationMonth: this.detailData.evaluationMonth,
        evaluationType: 'manager'
      };
      
      // 查询评分记录
      listEvaluation(params).then(response => {
        if (response.rows && response.rows.length > 0) {
          // 找到匹配的记录 - 机构负责人评分通常只有一条记录
          const record = response.rows[0];
          
          if (record) {
            // 构建评分表单
            this.scoreForm = {
              id: record.id,
              projectId: record.projectId || 0,
              projectName: '机构负责人评分',
              evaluatorId: record.evaluatorId,
              evaluateeId: this.detailData.userId,
              evaluateeName: this.detailData.nickName,
              score: row.score,
              evaluationMonth: this.detailData.evaluationMonth,
              evaluationType: 'manager',
              comments: row.comments || record.comments || ''
            };
            
            this.scoreTitle = "修改机构负责人评分";
            this.scoreOpen = true;
          } else {
            this.$message.error('未找到对应的评分记录');
          }
        } else {
          this.$message.error('未找到评分记录');
        }
        this.detailLoading = false;
      }).catch(error => {
        this.$message.error('获取评分记录失败: ' + (error.message || '未知错误'));
        this.detailLoading = false;
      });
    },
    
    /** 删除机构负责人评分 */
    handleManagerScoreDelete(row) {
      // 检查评分记录是否有效
      if (!row) {
        this.$message.error('无效的评分记录');
        return;
      }
      
      // 获取评分记录ID
      this.detailLoading = true;
      
      // 构建查询参数
      const params = {
        evaluateeId: this.detailData.userId,
        evaluationMonth: this.detailData.evaluationMonth,
        evaluationType: 'manager'
      };
      
      // 查询评分记录
      listEvaluation(params).then(response => {
        if (response.rows && response.rows.length > 0) {
          // 找到匹配的记录 - 机构负责人评分通常只有一条记录
          const record = response.rows[0];
          
          if (record) {
            // 确认删除
            this.$modal.confirm(`确认删除机构负责人评分记录吗？`).then(() => {
              delEvaluation(record.id)
                .then(response => {
                  this.$message.success('删除成功');
                  // 重新加载详情数据
                  this.handleViewDetail(this.detailData);
                })
                .catch(error => {
                  this.$message.error('删除失败: ' + (error.message || '未知错误'));
                })
                .finally(() => {
                  this.detailLoading = false;
                });
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除'
              });
              this.detailLoading = false;
            });
          } else {
            this.$message.error('未找到对应的评分记录');
            this.detailLoading = false;
          }
        } else {
          this.$message.error('未找到评分记录');
          this.detailLoading = false;
        }
      }).catch(error => {
        this.$message.error('获取评分记录失败: ' + (error.message || '未知错误'));
        this.detailLoading = false;
      });
    },




    // 判断是否为默认评分
    isDefaultScore(score, scoreList) {
      // 如果没有评分记录或评分记录为空，则认为是默认评分
      if (!scoreList || scoreList.length === 0) {
        return true;
      }

      // 如果评分为80分且没有实际评分记录，则认为是默认评分
      if (score === 80 && scoreList.length === 0) {
        return true;
      }

      // 可以根据实际业务逻辑进一步判断
      // 例如：检查评分记录的创建时间、评分来源等
      return false;
    },

    // 加载评分功能状态
    loadEvaluationStatus() {
      getConfigKey('evaluation.enabled').then(response => {
        const configValue = response.msg || response.data;
        if (configValue === null || configValue === undefined || configValue === '') {
          this.evaluationEnabled = true;
        } else {
          this.evaluationEnabled = configValue === 'true';
        }
      }).catch(() => {
        // 如果获取失败，默认为开启状态
        this.evaluationEnabled = true;
      });
    },

    // 处理评分开关切换
    handleEvaluationToggle(value) {
      this.switchLoading = true;

      const configValue = value ? 'true' : 'false';
      const actionText = value ? '开启' : '关闭';

      updateConfigByKey('evaluation.enabled', configValue).then(() => {
        this.$message.success(`评分功能已${actionText}`);
        this.evaluationEnabled = value;

        // 通知其他页面更新状态
        this.$eventBus && this.$eventBus.$emit('evaluationStatusChanged', value);
        this.switchLoading = false;
      }).catch(error => {
        this.$message.error(`${actionText}评分功能失败: ${error.message || '未知错误'}`);
        // 恢复开关状态
        this.evaluationEnabled = !value;
        this.switchLoading = false;
      });
    },
  }
};
</script>

<style scoped>
/* 评分控制卡片样式 */
.evaluation-control-card {
  border: 1px solid #e6ebf5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-content {
  padding: 10px 0;
}

.control-item {
  display: flex;
  align-items: center;
  height: 40px;
}

.control-label {
  font-weight: 500;
  color: #606266;
  margin-right: 10px;
}

.control-description {
  padding-left: 10px;
}

.detail-summary {
  margin-top: 15px;
  font-size: 14px;
}

.detail-summary .score {
  font-weight: bold;
  color: #409EFF;
  font-size: 16px;
}

.empty-block {
  min-height: 60px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  border: 1px solid #EBEEF5;
  border-top: none;
}

.detail-formula {
  padding: 20px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.formula-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.formula-content {
  color: #333;
  line-height: 1.5;
  margin-bottom: 15px;
}

.formula-calc {
  color: #409EFF;
  font-weight: bold;
  padding: 10px;
  background-color: #ecf5ff;
  border-radius: 4px;
}

.score-unit {
  margin-left: 5px;
  color: #606266;
}

.score-limit {
  margin-left: 5px;
  color: #909399;
  font-size: 12px;
}

.config-description {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.config-description i {
  margin-right: 5px;
}

.config-description .sub-description {
  margin-top: 5px;
  font-size: 13px;
  color: #606266;
}

.date-range-container {
  margin: 5px 0;
  padding: 5px 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  overflow: visible;
  position: relative;
}

.date-range-display {
  text-align: center;
  margin: 10px 0;
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

.el-slider__runway {
  margin: 25px 0;
  height: 8px;
}

.el-slider__bar {
  background-color: #409EFF;
  height: 8px;
}

.el-slider__button {
  border: 2px solid #409EFF;
  width: 18px;
  height: 18px;
}

.el-slider__marks {
  width: 100%;
}

.el-slider__marks-text {
  font-size: 12px;
  margin-top: 14px;
  color: #606266;
  font-weight: bold;
}

/* 针对所有标记进行调整，确保显示完整 */
.el-slider__marks-text:nth-child(1) {
  left: 0% !important;
  transform: translateX(0%) !important;
}

.el-slider__marks-text:nth-child(2) {
  left: 30% !important;
  transform: translateX(-30%) !important;
}

.el-slider__marks-text:nth-child(3) {
  left: 63% !important;
  transform: translateX(-30%) !important;
}

.el-slider__marks-text:nth-child(4) {
  left: 97% !important;
  transform: translateX(-100%) !important;
}

.slider-tips {
  background-color: #ecf8ff;
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 4px;
  color: #409EFF;
  line-height: 1.4;
}

.slider-tips i {
  margin-right: 5px;
}

.el-dialog__body {
  padding: 20px 30px;
}

.el-form-item__label {
  font-weight: bold;
}
</style>
