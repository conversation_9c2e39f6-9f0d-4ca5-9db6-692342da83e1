-- ===== 执行方案一：保留最新记录 =====
-- 将project_participation表中month字段为2025-07的值改为2025-06
-- 处理冲突：保留最新记录，删除旧记录

-- ===== 第一步：创建备份表 =====
CREATE TABLE project_participation_backup_20250715 AS 
SELECT * FROM project_participation WHERE month IN ('2025-06', '2025-07');

-- 验证备份
SELECT 
    '备份验证' as step,
    month, 
    COUNT(*) as count
FROM project_participation_backup_20250715 
GROUP BY month 
ORDER BY month;

-- ===== 第二步：分析当前冲突数据 =====
-- 查看冲突统计
SELECT 
    '冲突数据统计' as analysis_type,
    COUNT(DISTINCT CONCAT(pp1.user_name, '-', pp1.project_id)) as conflict_combinations,
    COUNT(*) as total_conflict_records
FROM project_participation pp1
INNER JOIN project_participation pp2 ON pp1.user_name = pp2.user_name AND pp1.project_id = pp2.project_id
WHERE pp1.month = '2025-06' AND pp2.month = '2025-07';

-- ===== 第三步：创建要保留的记录列表 =====
CREATE TEMPORARY TABLE records_to_keep AS
SELECT 
    user_name,
    project_id,
    MAX(created_at) as latest_created,
    CASE 
        WHEN MAX(created_at) = MAX(CASE WHEN month = '2025-07' THEN created_at END) THEN '2025-07'
        ELSE '2025-06'
    END as keep_month
FROM project_participation 
WHERE month IN ('2025-06', '2025-07')
GROUP BY user_name, project_id;

-- 验证要保留的记录
SELECT 
    '保留记录统计' as step,
    keep_month,
    COUNT(*) as count
FROM records_to_keep 
GROUP BY keep_month;

-- ===== 第四步：删除冲突的旧记录 =====
-- 预览要删除的记录数量
SELECT 
    '即将删除的记录' as step,
    COUNT(*) as delete_count
FROM project_participation pp
INNER JOIN records_to_keep rtk ON pp.user_name = rtk.user_name AND pp.project_id = rtk.project_id
WHERE pp.month IN ('2025-06', '2025-07')
AND NOT (pp.month = rtk.keep_month AND pp.created_at = rtk.latest_created);

-- 执行删除操作
DELETE pp FROM project_participation pp
INNER JOIN records_to_keep rtk ON pp.user_name = rtk.user_name AND pp.project_id = rtk.project_id
WHERE pp.month IN ('2025-06', '2025-07')
AND NOT (pp.month = rtk.keep_month AND pp.created_at = rtk.latest_created);

-- ===== 第五步：将所有剩余的2025-07记录改为2025-06 =====
-- 预览要修改的记录
SELECT 
    '即将修改的记录' as step,
    COUNT(*) as update_count
FROM project_participation 
WHERE month = '2025-07';

-- 执行修改
UPDATE project_participation 
SET month = '2025-06'
WHERE month = '2025-07';

-- 清理临时表
DROP TEMPORARY TABLE records_to_keep;

-- ===== 第六步：验证修改结果 =====
-- 检查修改后的数据分布
SELECT 
    '修改后数据分布' as check_type,
    month, 
    COUNT(*) as count,
    MIN(created_at) as earliest_created,
    MAX(created_at) as latest_created
FROM project_participation 
WHERE month IN ('2025-06', '2025-07', '2025-08') 
GROUP BY month 
ORDER BY month;

-- 检查是否还有重复数据
SELECT 
    '重复数据检查' as check_type,
    CASE WHEN COUNT(*) = 0 THEN '无重复数据' ELSE CONCAT('仍有', COUNT(*), '组重复数据') END as result
FROM (
    SELECT 
        user_name,
        project_id,
        COUNT(*) as cnt
    FROM project_participation 
    WHERE month = '2025-06'
    GROUP BY user_name, project_id
    HAVING COUNT(*) > 1
) duplicates;

-- 对比修改前后的记录数
SELECT 
    '记录数对比' as check_type,
    '修改前总数(2025-06+2025-07)' as period,
    COUNT(*) as count
FROM project_participation_backup_20250715
WHERE month IN ('2025-06', '2025-07')
UNION ALL
SELECT 
    '记录数对比' as check_type,
    '修改后2025-06总数' as period,
    COUNT(*) as count
FROM project_participation
WHERE month = '2025-06'
UNION ALL
SELECT 
    '记录数对比' as check_type,
    '修改后2025-07剩余数' as period,
    COUNT(*) as count
FROM project_participation
WHERE month = '2025-07';

-- ===== 第七步：最终确认 =====
SELECT 
    '执行完成' as status,
    NOW() as completion_time,
    '方案一执行完成：保留最新记录，month字段统一为2025-06' as result;

-- ===== 回滚说明 =====
/*
如果需要回滚，可以执行以下语句：
DELETE FROM project_participation WHERE month IN ('2025-06', '2025-07');
INSERT INTO project_participation SELECT * FROM project_participation_backup_20250715;
*/
