package com.kc.system.mapper;

import java.util.List;
import java.util.Set;
import com.kc.system.domain.EmployeeSalary;
import org.apache.ibatis.annotations.Param;

/**
 * 员工薪酬Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-17
 */
public interface EmployeeSalaryMapper 
{
    /**
     * 查询员工薪酬
     * 
     * @param id 员工薪酬主键
     * @return 员工薪酬
     */
    public EmployeeSalary selectEmployeeSalaryById(Long id);

    /**
     * 查询员工薪酬列表
     * 
     * @param employeeSalary 员工薪酬
     * @return 员工薪酬集合
     */
    public List<EmployeeSalary> selectEmployeeSalaryList(EmployeeSalary employeeSalary);

    /**
     * 新增员工薪酬
     * 
     * @param employeeSalary 员工薪酬
     * @return 结果
     */
    public int insertEmployeeSalary(EmployeeSalary employeeSalary);

    /**
     * 修改员工薪酬
     * 
     * @param employeeSalary 员工薪酬
     * @return 结果
     */
    public int updateEmployeeSalary(EmployeeSalary employeeSalary);

    /**
     * 删除员工薪酬
     * 
     * @param id 员工薪酬主键
     * @return 结果
     */
    public int deleteEmployeeSalaryById(Long id);

    /**
     * 批量删除员工薪酬
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmployeeSalaryByIds(Long[] ids);

    /**
     * 根据月份集合查询薪资记录
     */
    List<EmployeeSalary> selectByMonths(@Param("months") Set<String> months);

    /**
     * 根据月份查询所有员工薪酬
     * 
     * @param workMonth 工作月份
     * @return 员工薪酬列表
     */
    List<EmployeeSalary> selectEmployeeSalaryByMonth(@Param("workMonth") String workMonth);
}
