<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.ProjectInfoMapper">
    
    <resultMap type="ProjectInfo" id="ProjectInfoResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="projectShortName"    column="project_short_name"    />
        <result property="remarks"    column="remarks"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
        <result property="leaderName"    column="leader_name"    />
        <result property="deptName"    column="dept_name"    />
        <collection property="assistantList" javaType="java.util.ArrayList" 
                    ofType="java.util.Map" column="id" 
                    select="selectAssistantsByProjectId"/>
        <collection property="memberList" javaType="java.util.ArrayList" 
                    ofType="java.util.Map" column="id" 
                    select="selectProjectMembersByProjectId"/>
    </resultMap>

    <sql id="selectProjectInfoVo">
        select distinct 
            p.id, p.dept_id, p.project_name, p.project_short_name, p.remarks, 
            p.created_at, p.updated_at,
            leader_user.nick_name as leader_name,
            d.dept_name
        from project_info p
        left join project_members leader on p.id = leader.project_id and leader.role = '负责人'
        left join sys_user leader_user on leader.user_name = leader_user.user_name
        left join sys_dept d on p.dept_id = d.dept_id
    </sql>

    <select id="selectProjectInfoList" parameterType="ProjectInfo" resultMap="ProjectInfoResult">
        <include refid="selectProjectInfoVo"/>
        <where>  
            <if test="deptId != null "> and p.dept_id = #{deptId}</if>
            <if test="projectName != null  and projectName != ''"> and p.project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectShortName != null  and projectShortName != ''"> and p.project_short_name like concat('%', #{projectShortName}, '%')</if>
            <if test="remarks != null  and remarks != ''"> and p.remarks = #{remarks}</if>
            <if test="createdAt != null "> and p.created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and p.updated_at = #{updatedAt}</if>
        </where>
    </select>
    
    <select id="selectProjectInfoById" parameterType="Long" resultMap="ProjectInfoResult">
        select distinct p.id, p.dept_id, p.project_name, p.project_short_name, p.remarks, 
               p.created_at, p.updated_at, u.nick_name as leader_name,
               d.dept_name, m.user_name as leader
        from project_info p
        left join project_members m on p.id = m.project_id and m.role = '负责人'
        left join sys_user u on m.user_name = u.user_name
        left join sys_dept d on p.dept_id = d.dept_id
        where p.id = #{id}
        limit 1
    </select>

    <insert id="insertProjectInfo" parameterType="ProjectInfo" useGeneratedKeys="true" keyProperty="id">
        insert into project_info (
            <if test="deptId != null">dept_id,</if>
            <if test="projectName != null">project_name,</if>
            <if test="projectShortName != null">project_short_name,</if>
            <if test="remarks != null">remarks,</if>
            created_at,
            updated_at
        ) values (
            <if test="deptId != null">#{deptId},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="projectShortName != null">#{projectShortName},</if>
            <if test="remarks != null">#{remarks},</if>
            SYSDATE(),
            SYSDATE()
        )
    </insert>

    <update id="updateProjectInfo" parameterType="ProjectInfo">
        update project_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="projectShortName != null">project_short_name = #{projectShortName},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            updated_at = SYSDATE(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectInfoById" parameterType="Long">
        delete from project_info where id = #{id}
    </delete>

    <delete id="deleteProjectInfoByIds" parameterType="String">
        delete from project_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAssistantsByProjectId" resultType="java.util.Map">
        select 
            m.user_name as userName,
            u.nick_name as nickName,
            d.dept_name as deptName
        from project_members m
        left join sys_user u on m.user_name = u.user_name
        left join sys_dept d on u.dept_id = d.dept_id
        where m.project_id = #{id}
        and m.role = '配合人员'
    </select>

    <select id="selectProjectMembersByProjectId" resultType="java.util.Map">
        select 
            m.role as role,
            u.nick_name as nickName
        from project_members m
        left join sys_user u on m.user_name = u.user_name
        where m.project_id = #{id}
        order by 
            case m.role 
                when '负责人' then 1 
                when '配合人员' then 2 
                else 3 
            end
    </select>

    <!-- 查询当前用户负责的项目列表 -->
    <select id="selectUserLeadProjectList" resultMap="ProjectInfoResult">
        select 
            p.id, p.dept_id, p.project_name, p.project_short_name, p.remarks, 
            p.created_at, p.updated_at,
            u.nick_name as leader_name,
            d.dept_name
        from project_info p
        inner join project_members m on p.id = m.project_id
        left join sys_user u on m.user_name = u.user_name
        left join sys_dept d on p.dept_id = d.dept_id
        where m.user_name = #{userName}
        and m.role = '负责人'
        order by p.created_at desc
    </select>

    <select id="selectUserProjectWorkloads" resultType="java.util.Map">
        SELECT 
            p.id as projectId,
            p.project_name as projectName,
            COALESCE(w.involvement, -1) as involvement
        FROM 
            project_info p
            INNER JOIN project_members m ON p.id = m.project_id
            LEFT JOIN project_workload w ON p.id = w.project_id 
                AND w.user_name = #{userName}
                AND w.work_month = #{month}
        WHERE 
            m.user_name = #{userName}
        ORDER BY 
            p.id
    </select>

    <select id="selectProjectInfoByName" parameterType="String" resultMap="ProjectInfoResult">
        <include refid="selectProjectInfoVo"/>
        where p.project_name = #{projectName}
        limit 1
    </select>

    <select id="selectProjectInfoByNameAndDept" resultMap="ProjectInfoResult">
        <include refid="selectProjectInfoVo"/>
        where p.project_name = #{projectName} 
        and p.dept_id = #{deptId}
        limit 1
    </select>

    <!-- 根据部门ID查询项目列表 -->
    <select id="selectProjectsByDeptId" resultMap="ProjectInfoResult">
        select 
            p.id, p.dept_id, p.project_name, p.project_short_name, p.remarks, 
            p.created_at, p.updated_at,
            (select u.nick_name from project_members pm
             left join sys_user u on pm.user_name = u.user_name
             where pm.project_id = p.id and pm.role = '负责人'
             limit 1) as leader_name,
            d.dept_name
        from project_info p
        left join sys_dept d on p.dept_id = d.dept_id
        where p.dept_id = #{deptId}
        order by p.created_at desc
    </select>
</mapper>