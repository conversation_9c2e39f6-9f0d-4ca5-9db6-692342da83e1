<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.QuotaManagementMapper">
    
    <resultMap type="QuotaManagement" id="QuotaManagementResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="evaluationYear"    column="evaluation_year"    />
        <result property="highScoreQuota"    column="high_score_quota"    />
        <result property="quotaType"    column="quota_type"    />
        <result property="deptName"    column="dept_name"    />
        <result property="deptLeader"    column="dept_leader"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQuotaManagementVo">
        select qm.id, qm.dept_id, qm.evaluation_year, qm.high_score_quota, qm.quota_type,
               qm.create_by, qm.create_time, qm.update_by, qm.update_time,
               sd.dept_name, sd.leader as dept_leader
        from quota_management qm
        left join sys_dept sd on qm.dept_id = sd.dept_id
    </sql>

    <select id="selectQuotaManagementList" parameterType="QuotaManagement" resultMap="QuotaManagementResult">
        <include refid="selectQuotaManagementVo"/>
        <where>  
            <if test="deptId != null "> and qm.dept_id = #{deptId}</if>
            <if test="evaluationYear != null  and evaluationYear != ''"> and qm.evaluation_year = #{evaluationYear}</if>
            <if test="quotaType != null  and quotaType != ''"> and qm.quota_type = #{quotaType}</if>
            <if test="deptName != null  and deptName != ''"> and sd.dept_name like concat('%', #{deptName}, '%')</if>
        </where>
        order by qm.evaluation_year desc, qm.dept_id
    </select>
    
    <select id="selectQuotaManagementById" parameterType="Long" resultMap="QuotaManagementResult">
        <include refid="selectQuotaManagementVo"/>
        where qm.id = #{id}
    </select>

    <select id="selectByDeptAndYear" resultMap="QuotaManagementResult">
        <include refid="selectQuotaManagementVo"/>
        where qm.dept_id = #{deptId} and qm.evaluation_year = #{year}
    </select>

    <select id="selectQuotaStatistics" parameterType="String" resultMap="QuotaManagementResult">
        <include refid="selectQuotaManagementVo"/>
        where qm.evaluation_year = #{year}
        order by qm.quota_type, qm.dept_id
    </select>

    <select id="selectDeptsNeedInitQuota" parameterType="String" resultType="QuotaManagement">
        select
            sd.dept_id as deptId,
            #{year} as evaluationYear,
            0 as highScoreQuota,
            'MANUAL' as quotaType,
            sd.dept_name as deptName
        from sys_dept sd
        where sd.del_flag = '0'
        and sd.status = '0'
        and sd.dept_id not in (
            select qm.dept_id from quota_management qm where qm.evaluation_year = #{year}
        )
    </select>
        
    <insert id="insertQuotaManagement" parameterType="QuotaManagement" useGeneratedKeys="true" keyProperty="id">
        insert into quota_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="evaluationYear != null and evaluationYear != ''">evaluation_year,</if>
            <if test="highScoreQuota != null">high_score_quota,</if>
            <if test="quotaType != null">quota_type,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="evaluationYear != null and evaluationYear != ''">#{evaluationYear},</if>
            <if test="highScoreQuota != null">#{highScoreQuota},</if>
            <if test="quotaType != null">#{quotaType},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateQuotaManagement" parameterType="QuotaManagement">
        update quota_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="highScoreQuota != null">high_score_quota = #{highScoreQuota},</if>
            <if test="quotaType != null">quota_type = #{quotaType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQuotaManagementById" parameterType="Long">
        delete from quota_management where id = #{id}
    </delete>

    <delete id="deleteQuotaManagementByIds" parameterType="String">
        delete from quota_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="batchUpdateQuotaUsage" parameterType="java.util.List">
        <!-- 已简化，不再需要批量更新使用情况 -->
    </update>

    <update id="resetYearQuotaUsage" parameterType="String">
        <!-- 已简化，不再需要重置使用情况 -->
    </update>

    <insert id="batchInitDeptQuota" parameterType="java.util.List">
        insert into quota_management (dept_id, evaluation_year, high_score_quota, quota_type, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId}, #{item.evaluationYear}, #{item.highScoreQuota}, #{item.quotaType}, #{item.createBy}, sysdate())
        </foreach>
    </insert>

</mapper>
