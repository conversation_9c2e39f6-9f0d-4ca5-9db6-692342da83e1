package com.kc.system.service;

import java.math.BigDecimal;
import java.util.List;
import com.kc.system.domain.DeptHighScoreQuota;

/**
 * 部门高分配额Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IDeptHighScoreQuotaService 
{
    /**
     * 查询部门高分配额
     * 
     * @param id 部门高分配额主键
     * @return 部门高分配额
     */
    public DeptHighScoreQuota selectDeptHighScoreQuotaById(Long id);

    /**
     * 查询部门高分配额列表
     * 
     * @param deptHighScoreQuota 部门高分配额
     * @return 部门高分配额集合
     */
    public List<DeptHighScoreQuota> selectDeptHighScoreQuotaList(DeptHighScoreQuota deptHighScoreQuota);

    /**
     * 新增部门高分配额
     * 
     * @param deptHighScoreQuota 部门高分配额
     * @return 结果
     */
    public int insertDeptHighScoreQuota(DeptHighScoreQuota deptHighScoreQuota);

    /**
     * 修改部门高分配额
     * 
     * @param deptHighScoreQuota 部门高分配额
     * @return 结果
     */
    public int updateDeptHighScoreQuota(DeptHighScoreQuota deptHighScoreQuota);

    /**
     * 批量删除部门高分配额
     * 
     * @param ids 需要删除的部门高分配额主键集合
     * @return 结果
     */
    public int deleteDeptHighScoreQuotaByIds(Long[] ids);

    /**
     * 删除部门高分配额信息
     * 
     * @param id 部门高分配额主键
     * @return 结果
     */
    public int deleteDeptHighScoreQuotaById(Long id);

    /**
     * 检查部门高分配额是否足够
     * @param deptId 部门ID
     * @param year 评价年度
     * @param requestCount 请求的高分数量
     * @return 是否有足够配额
     */
    boolean checkQuotaAvailable(Long deptId, String year, int requestCount);
    
    /**
     * 获取部门高分配额信息
     * @param deptId 部门ID
     * @param year 评价年度
     * @return 配额信息
     */
    DeptHighScoreQuota getDeptQuotaInfo(Long deptId, String year);
    
    /**
     * 使用高分配额
     * @param deptId 部门ID
     * @param userId 用户ID
     * @param year 评价年度
     * @param month 评价月份
     * @param score 评分
     * @param evaluatorId 评价人ID
     * @return 是否成功
     */
    boolean useQuota(Long deptId, Long userId, String year, String month, 
                    BigDecimal score, Long evaluatorId);
    
    /**
     * 释放高分配额（当评分被修改为95分以下时）
     * @param deptId 部门ID
     * @param userId 用户ID
     * @param year 评价年度
     * @param month 评价月份
     * @return 是否成功
     */
    boolean releaseQuota(Long deptId, Long userId, String year, String month);
    
    /**
     * 初始化部门年度配额
     * @param deptId 部门ID
     * @param year 评价年度
     * @return 配额信息
     */
    DeptHighScoreQuota initDeptQuota(Long deptId, String year);
}
