package com.kc.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Date;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kc.system.mapper.ProjectWorkloadMapper;
import com.kc.system.domain.ProjectWorkload;
import com.kc.system.service.IProjectWorkloadService;
import com.kc.system.domain.ProjectMembers;
import com.kc.system.domain.EmployeeSalary;
import com.kc.system.mapper.ProjectMembersMapper;
import com.kc.system.mapper.EmployeeSalaryMapper;
import java.util.HashMap;
import java.util.Arrays;
import java.util.HashSet;
import com.kc.system.domain.ProjectInfo;
import com.kc.system.mapper.ProjectInfoMapper;
import com.kc.system.domain.ProjectSalaryDetail;
import com.kc.system.mapper.ProjectSalaryDetailMapper;
import com.kc.common.utils.DateUtils;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目工时记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-15
 */
@Service
public class ProjectWorkloadServiceImpl implements IProjectWorkloadService 
{
    @Autowired
    private ProjectWorkloadMapper projectWorkloadMapper;

    @Autowired
    private ProjectMembersMapper projectMembersMapper;

    @Autowired
    private EmployeeSalaryMapper employeeSalaryMapper;

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ProjectSalaryDetailMapper projectSalaryDetailMapper;

    /**
     * 查询项目工时记录
     * 
     * @param id 项目工时记录主键
     * @return 项目工时记录
     */
    @Override
    public ProjectWorkload selectProjectWorkloadById(Long id)
    {
        return projectWorkloadMapper.selectProjectWorkloadById(id);
    }

    /**
     * 查询项目工时记录列表
     *
     * @param projectWorkload 项目工时记录
     * @return 项目工时记录
     */
    @Override
    public List<ProjectWorkload> selectProjectWorkloadList(ProjectWorkload projectWorkload)
    {
        return projectWorkloadMapper.selectProjectWorkloadList(projectWorkload);
    }

    /**
     * 查询用户实际工时记录
     *
     * @param projectWorkload 项目工时记录
     * @return 项目工时记录
     */
    @Override
    public List<ProjectWorkload> selectUserActualWorkload(ProjectWorkload projectWorkload)
    {
        return projectWorkloadMapper.selectUserActualWorkload(projectWorkload);
    }

    /**
     * 查询实际工时记录列表（只返回有实际记录的数据）
     *
     * @param projectWorkload 项目工时记录
     * @return 项目工时记录集合
     */
    @Override
    public List<ProjectWorkload> selectActualWorkloadList(ProjectWorkload projectWorkload) {
        return projectWorkloadMapper.selectActualWorkloadList(projectWorkload);
    }

    /**
     * 新增项目工时记录
     *
     * @param projectWorkload 项目工时记录
     * @return 结果
     */
    @Override
    public int insertProjectWorkload(ProjectWorkload projectWorkload)
    {
        // 校验工作量归集值：为0或为空时不插入数据
        if (projectWorkload.getInvolvement() == null ||
            projectWorkload.getInvolvement().compareTo(BigDecimal.ZERO) <= 0) {

            // 如果工作量归集为0或空，检查是否有已存在的记录需要删除
            ProjectWorkload existingRecord = projectWorkloadMapper.selectExistingWorkload(
                projectWorkload.getUserName(),
                projectWorkload.getWorkMonth(),
                projectWorkload.getSourceEffortProjectId()
            );

            if (existingRecord != null) {
                // 删除已存在的记录
                projectWorkloadMapper.deleteProjectWorkloadById(existingRecord.getId());
                return 1; // 返回1表示操作成功（删除了1条记录）
            }

            return 0; // 没有记录需要处理，返回0
        }

        // 检查是否已存在相同的记录（基于唯一约束字段）
        ProjectWorkload existingRecord = projectWorkloadMapper.selectExistingWorkload(
            projectWorkload.getUserName(),
            projectWorkload.getWorkMonth(),
            projectWorkload.getSourceEffortProjectId()
        );

        if (existingRecord != null) {
            // 如果记录已存在，更新现有记录
            projectWorkload.setId(existingRecord.getId());
            projectWorkload.setUpdatedAt(new Date());
            return projectWorkloadMapper.updateProjectWorkload(projectWorkload);
        } else {
            // 如果记录不存在，插入新记录
            projectWorkload.setCreatedAt(new Date());
            projectWorkload.setUpdatedAt(new Date());
            return projectWorkloadMapper.insertProjectWorkload(projectWorkload);
        }
    }

    /**
     * 修改项目工时记录
     * 
     * @param projectWorkload 项目工时记录
     * @return 结果
     */
    @Override
    public int updateProjectWorkload(ProjectWorkload projectWorkload)
    {
        return projectWorkloadMapper.updateProjectWorkload(projectWorkload);
    }

    /**
     * 批量删除项目工时记录
     * 
     * @param ids 需要删除的项目工时记录主键
     * @return 结果
     */
    @Override
    public int deleteProjectWorkloadByIds(Long[] ids)
    {
        return projectWorkloadMapper.deleteProjectWorkloadByIds(ids);
    }

    /**
     * 删除项目工时记录信息
     * 
     * @param id 项目工时记录主键
     * @return 结果
     */
    @Override
    public int deleteProjectWorkloadById(Long id)
    {
        return projectWorkloadMapper.deleteProjectWorkloadById(id);
    }

    /**
     * 获取项目统计数据
     * 
     * @param projectId 项目ID
     * @param workMonth 工作月份
     * @return 项目统计数据
     */
    @Override
    public List<Map<String, Object>> getProjectStats(Long projectId, String workMonth) {
        // 1. 如果没有指定月份，获取项目的所有工作月份
        Set<String> months;
        if (workMonth != null && !workMonth.isEmpty()) {
            months = new HashSet<>();
            months.add(workMonth);
        } else {
            months = projectWorkloadMapper.selectProjectMonths(projectId);
        }
        
        // 2. 获取项目统计基础数据
        List<Map<String, Object>> statsList = new ArrayList<>();
        for (String month : months) {
            Map<String, Object> stats = projectWorkloadMapper.selectProjectStats(projectId, month);
            if (stats != null) {
                statsList.add(stats);
            }
        }
        
        // 3. 获取薪资数据
        List<EmployeeSalary> salaries = new ArrayList<>();
        if (!months.isEmpty()) {
            salaries = employeeSalaryMapper.selectByMonths(months);
        }
        
        // 4. 将薪资数据按月份分组
        Map<String, Map<String, BigDecimal>> salaryByMonth = new HashMap<>();
        for (EmployeeSalary salary : salaries) {
            if (salary != null && salary.getSalaryMonth() != null && 
                salary.getUserName() != null && salary.getSalary() != null) {
                salaryByMonth.computeIfAbsent(salary.getSalaryMonth(), k -> new HashMap<>())
                             .put(salary.getUserName(), salary.getSalary());
            }
        }
        
        // 5. 处理每个月的统计数据
        for (Map<String, Object> stat : statsList) {
            String month = (String) stat.get("workMonth");
            Map<String, BigDecimal> monthSalaries = salaryByMonth.get(month);
            
            // 解析成员角色
            String memberRolesStr = (String) stat.get("memberRoles");
            Map<String, String> memberRoles = new HashMap<>();
            Map<String, String> nickNames = new HashMap<>();  // 添加昵称映射
            if (memberRolesStr != null) {
                String[] lines = memberRolesStr.split("\n");
                for (String line : lines) {
                    if (line != null && !line.trim().isEmpty()) {
                        String[] parts = line.split(":");
                        if (parts.length == 3) {  // 现在有三个部分：userName, nickName, role
                            String userName = parts[0].trim();
                            String nickName = parts[1].trim();
                            String role = parts[2].trim();
                            memberRoles.put(userName, role);
                            nickNames.put(userName, nickName);  // 保存昵称映射
                        }
                    }
                }
            }
            System.out.println("Final memberRoles map: " + memberRoles);

            // 检查数据完整性
            List<String> missingData = new ArrayList<>();
            boolean hasIncompleteMemberData = false;

            // 检查薪酬数据完整性
            for (Map.Entry<String, String> entry : memberRoles.entrySet()) {
                String userName = entry.getKey();
                String nickName = nickNames.get(userName);  // 获取昵称
                
                // 检查所有成员的薪酬
                if (monthSalaries == null || !monthSalaries.containsKey(userName)) {
                    missingData.add((nickName != null ? nickName : userName) + "的薪酬未填报");
                    hasIncompleteMemberData = true;
                }
            }

            // 解析工作量详情
            String workloadDetailsStr = (String) stat.get("workloadDetails");
            Map<String, BigDecimal> workloadMap = new HashMap<>();
            if (workloadDetailsStr != null && !hasIncompleteMemberData) {
                String[] details = workloadDetailsStr.split("\n");
                for (String detail : details) {
                    if (detail != null && !detail.trim().isEmpty()) {
                        String[] parts = detail.split(":");
                        if (parts.length == 3) {  // 现在有三个部分：userName, nickName, involvement
                            String userName = parts[0].trim();
                            String nickName = parts[1].trim();
                            String role = memberRoles.get(userName);
                            
                            if (role != null && !"配合人员".equals(role)) {
                                try {
                                    BigDecimal involvement = new BigDecimal(parts[2].trim());
                                    if (involvement.compareTo(BigDecimal.ZERO) == 0) {
                                        missingData.add((nickName != null ? nickName : userName) + "的参与度未填报");
                                        hasIncompleteMemberData = true;
                                    } else {
                                        workloadMap.put(userName, involvement.divide(BigDecimal.valueOf(100)));
                                    }
                                } catch (Exception e) {
                                    missingData.add((nickName != null ? nickName : userName) + "的参与度格式不正确");
                                    hasIncompleteMemberData = true;
                                }
                            }
                        }
                    }
                }
            }

            // 添加调试日志
            System.out.println("Workload map: " + workloadMap);

            // 计算整体劳务费
            if (monthSalaries != null && !memberRoles.isEmpty() && !hasIncompleteMemberData) {
                BigDecimal totalSalaryA = BigDecimal.ZERO; // 非配合人员劳务费
                BigDecimal totalSalaryB = BigDecimal.ZERO; // 配合人员劳务费

                // 遍历所有成员
                for (Map.Entry<String, String> entry : memberRoles.entrySet()) {
                    String userName = entry.getKey();
                    String role = entry.getValue();
                    BigDecimal salary = monthSalaries.get(userName);
                    
                    if (salary != null) {
                        if (!"配合人员".equals(role)) {
                            // 非配合人员：薪酬 × 参与度
                            BigDecimal involvement = workloadMap.getOrDefault(userName, BigDecimal.ZERO);
                            BigDecimal memberSalary = salary.multiply(involvement);
                            totalSalaryA = totalSalaryA.add(memberSalary);
                        } else {
                            // 配合人员：薪酬 ÷ 参与项目数
                            long projectCount = projectMembersMapper.countProjectsByUserNameAndRole(userName, "配合人员");
                            if (projectCount > 0) {
                                BigDecimal averageSalary = salary.divide(BigDecimal.valueOf(projectCount), 4, RoundingMode.HALF_UP);
                                totalSalaryB = totalSalaryB.add(averageSalary);
                            }
                        }
                    }
                }

                // 计算总劳务费
                BigDecimal totalSalary = totalSalaryA.add(totalSalaryB);
                if (totalSalary.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal totalSalaryInWan = totalSalary.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP);
                    stat.put("totalSalary", totalSalaryInWan);
                }
            }

            // 设置缺失数据信息
            if (hasIncompleteMemberData) {
                stat.put("missingData", String.join("\n", missingData));
            }
        }
        
        return statsList;
    }

    /**
     * 获取所有项目统计数据（不分页）
     */
    @Override
    @Transactional
    public List<Map<String, Object>> getAllProjectStats(String workMonth) {
        // 获取所有项目列表
        ProjectInfo projectInfo = new ProjectInfo();
        List<ProjectInfo> allProjects = projectInfoMapper.selectProjectInfoList(projectInfo);
        
        List<Map<String, Object>> allStats = new ArrayList<>();
        
        // 获取当月所有用户的薪酬数据
        Map<String, BigDecimal> monthSalaries = null;
        if (workMonth != null && !workMonth.isEmpty()) {
            List<EmployeeSalary> salaries = employeeSalaryMapper.selectEmployeeSalaryByMonth(workMonth);
            monthSalaries = salaries.stream()
                .collect(Collectors.toMap(
                    EmployeeSalary::getUserName,
                    EmployeeSalary::getSalary
                ));
        }
        
        // 为每个项目获取统计数据
        for (ProjectInfo project : allProjects) {
            Map<String, Object> projectStat = projectWorkloadMapper.selectProjectStats(
                project.getId(), 
                workMonth
            );
            
            // 如果获取到统计数据，添加项目名称并计算整体劳务费
            if (projectStat != null) {
                projectStat.put("projectName", project.getProjectName());
                
                // 解析成员角色信息
                String memberRolesStr = (String) projectStat.get("memberRoles");
                Map<String, String> memberRoles = new HashMap<>();
                Map<String, String> nickNames = new HashMap<>();
                
                if (memberRolesStr != null) {
                    String[] lines = memberRolesStr.split("\n");
                    for (String line : lines) {
                        if (line != null && !line.trim().isEmpty()) {
                            String[] parts = line.split(":");
                            if (parts.length == 3) {
                                String userName = parts[0].trim();
                                String nickName = parts[1].trim();
                                String role = parts[2].trim();
                                memberRoles.put(userName, role);
                                nickNames.put(userName, nickName);
                            }
                        }
                    }
                }

                // 检查数据完整性
                List<String> missingData = new ArrayList<>();
                boolean hasIncompleteMemberData = false;

                // 检查薪酬数据完整性
                for (Map.Entry<String, String> entry : memberRoles.entrySet()) {
                    String userName = entry.getKey();
                    String nickName = nickNames.get(userName);
                    
                    if (monthSalaries == null || !monthSalaries.containsKey(userName)) {
                        missingData.add((nickName != null ? nickName : userName) + "的薪酬未填报");
                        hasIncompleteMemberData = true;
                    }
                }

                // 计算整体劳务费
                if (monthSalaries != null && !memberRoles.isEmpty() && !hasIncompleteMemberData) {
                    BigDecimal totalSalaryA = BigDecimal.ZERO; // 非配合人员劳务费
                    BigDecimal totalSalaryB = BigDecimal.ZERO; // 配合人员劳务费

                    // 解析工作量详情
                    String workloadDetailsStr = (String) projectStat.get("workloadDetails");
                    Map<String, BigDecimal> workloadMap = new HashMap<>();
                    if (workloadDetailsStr != null) {
                        String[] details = workloadDetailsStr.split("\n");
                        for (String detail : details) {
                            if (detail != null && !detail.trim().isEmpty()) {
                                String[] parts = detail.split(":");
                                if (parts.length == 3) {
                                    String userName = parts[0].trim();
                                    BigDecimal involvement = new BigDecimal(parts[2].trim());
                                    workloadMap.put(userName, involvement.divide(BigDecimal.valueOf(100)));
                                }
                            }
                        }
                    }

                    // 计算每个成员的劳务费
                    for (Map.Entry<String, String> entry : memberRoles.entrySet()) {
                        String userName = entry.getKey();
                        String role = entry.getValue();
                        BigDecimal salary = monthSalaries.get(userName);
                        
                        if (salary != null) {
                            BigDecimal memberSalary;
                            if (!"配合人员".equals(role)) {
                                // 非配合人员：薪酬 × 参与度
                                BigDecimal involvement = workloadMap.getOrDefault(userName, BigDecimal.ZERO);
                                memberSalary = salary.multiply(involvement);
                                totalSalaryA = totalSalaryA.add(memberSalary);
                            } else {
                                // 配合人员：薪酬 ÷ 参与项目数
                                long projectCount = projectMembersMapper.countProjectsByUserNameAndRole(userName, "配合人员");
                                if (projectCount > 0) {
                                    memberSalary = salary.divide(BigDecimal.valueOf(projectCount), 4, RoundingMode.HALF_UP);
                                    totalSalaryB = totalSalaryB.add(memberSalary);
                                } else {
                                    continue;
                                }
                            }

                            // 更新或插入劳务费明细
                            ProjectSalaryDetail salaryDetail = new ProjectSalaryDetail();
                            salaryDetail.setProjectId(project.getId());
                            salaryDetail.setUserName(userName);
                            salaryDetail.setWorkMonth(workMonth);
                            salaryDetail.setSalaryAmount(memberSalary);

                            // 查询是否存在记录
                            ProjectSalaryDetail existingDetail = projectSalaryDetailMapper.selectByProjectIdAndUserNameAndMonth(
                                project.getId(), 
                                userName, 
                                workMonth
                            );

                            if (existingDetail != null) {
                                // 更新现有记录
                                salaryDetail.setId(existingDetail.getId());
                                salaryDetail.setUpdateTime(DateUtils.getNowDate());
                                projectSalaryDetailMapper.updateProjectSalaryDetail(salaryDetail);
                            } else {
                                // 插入新记录
                                salaryDetail.setCreateTime(DateUtils.getNowDate());
                                salaryDetail.setUpdateTime(DateUtils.getNowDate());
                                projectSalaryDetailMapper.insertProjectSalaryDetail(salaryDetail);
                            }
                        }
                    }

                    // 计算总劳务费
                    BigDecimal totalSalary = totalSalaryA.add(totalSalaryB);
                    if (totalSalary.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal totalSalaryInWan = totalSalary.divide(BigDecimal.valueOf(10000), 4, RoundingMode.HALF_UP);
                        projectStat.put("totalSalary", totalSalaryInWan);
                    }
                }

                // 设置缺失数据信息
                if (hasIncompleteMemberData) {
                    projectStat.put("missingData", String.join("\n", missingData));
                }

                allStats.add(projectStat);
            }
        }
        
        return allStats;
    }

    @Override
    public List<Map<String, Object>> selectUnfilledWorkloadCount(String workMonth) {
        return projectWorkloadMapper.selectUnfilledWorkloadCount(workMonth);
    }
}
