import request from '@/utils/request'

// 查询项目评价列表
export function listEvaluation(query) {
  return request({
    url: '/system/evaluation/list',
    method: 'get',
    params: query
  })
}

// 查询项目评价详细
export function getEvaluation(id) {
  return request({
    url: '/system/evaluation/' + id,
    method: 'get'
  })
}

// 新增项目评价
export function addEvaluation(data) {
  return request({
    url: '/system/evaluation',
    method: 'post',
    data: data
  })
}

// 修改项目评价
export function updateEvaluation(data) {
  return request({
    url: '/system/evaluation',
    method: 'put',
    data: data
  })
}

// 删除项目评价
export function delEvaluation(id) {
  return request({
    url: '/system/evaluation/' + id,
    method: 'delete'
  })
}

// 获取用户参与的项目评分列表
export function getUserProjectEvaluations(userId, evaluationMonth) {
  // 确保userId不为空
  if (!userId) {
    return Promise.reject(new Error('用户ID不能为空'));
  }
  
  return request({
    url: '/system/evaluation/user/project/' + encodeURIComponent(userId),
    method: 'get',
    params: evaluationMonth ? { evaluationMonth } : {}
  });
}

// 验证机构负责人评分前置条件
export function validateManagerEvaluation(evaluateeId, evaluationMonth) {
  return request({
    url: '/system/evaluation/validateManagerEvaluation',
    method: 'post',
    data: {
      evaluateeId: evaluateeId,
      evaluationMonth: evaluationMonth
    }
  })
}

// 导出部门评分数据
export function exportDeptEvaluationData(deptId, evaluationMonth, deptName, userIds = null) {
  const data = {
    deptId: deptId,
    evaluationMonth: evaluationMonth,
    deptName: deptName
  };

  // 如果提供了用户ID列表，则添加到请求数据中
  if (userIds && Array.isArray(userIds) && userIds.length > 0) {
    data.userIds = userIds;
  }

  return request({
    url: '/system/evaluation/exportDeptEvaluationData',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
