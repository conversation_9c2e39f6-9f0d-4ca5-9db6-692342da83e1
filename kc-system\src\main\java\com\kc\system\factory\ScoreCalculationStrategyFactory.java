package com.kc.system.factory;

import com.kc.system.dto.ScoreCalculationContext;
import com.kc.system.strategy.ScoreCalculationStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 评分计算策略工厂
 * 负责根据上下文选择合适的评分计算策略
 * 
 * <AUTHOR>
 */
@Component
public class ScoreCalculationStrategyFactory {
    
    private final Map<String, ScoreCalculationStrategy> strategyMap;
    
    @Autowired
    public ScoreCalculationStrategyFactory(List<ScoreCalculationStrategy> strategies) {
        this.strategyMap = strategies.stream()
                .collect(Collectors.toMap(
                    ScoreCalculationStrategy::getStrategyType,
                    Function.identity()
                ));
    }
    
    /**
     * 根据上下文获取合适的评分计算策略
     * 
     * @param context 评分计算上下文
     * @return 评分计算策略
     */
    public ScoreCalculationStrategy getStrategy(ScoreCalculationContext context) {
        // 按优先级顺序检查策略适用性
        
        // 1. 优先检查子部门成员策略
        ScoreCalculationStrategy subDeptStrategy = strategyMap.get("SUB_DEPT_MEMBER");
        if (subDeptStrategy != null && subDeptStrategy.isApplicable(context)) {
            return subDeptStrategy;
        }
        
        // 2. 检查标准员工策略（有项目参与）
        ScoreCalculationStrategy standardStrategy = strategyMap.get("STANDARD_EMPLOYEE");
        if (standardStrategy != null && standardStrategy.isApplicable(context)) {
            return standardStrategy;
        }
        
        // 3. 检查无项目员工策略
        ScoreCalculationStrategy noProjectStrategy = strategyMap.get("NO_PROJECT_EMPLOYEE");
        if (noProjectStrategy != null && noProjectStrategy.isApplicable(context)) {
            return noProjectStrategy;
        }
        
        // 4. 默认返回标准策略
        return strategyMap.get("STANDARD_EMPLOYEE");
    }
    
    /**
     * 根据策略类型获取策略
     * 
     * @param strategyType 策略类型
     * @return 评分计算策略
     */
    public ScoreCalculationStrategy getStrategyByType(String strategyType) {
        return strategyMap.get(strategyType);
    }
    
    /**
     * 获取所有可用策略
     * 
     * @return 所有策略列表
     */
    public List<ScoreCalculationStrategy> getAllStrategies() {
        return strategyMap.values().stream().collect(Collectors.toList());
    }
    
    /**
     * 获取适用于指定上下文的所有策略
     * 
     * @param context 评分计算上下文
     * @return 适用的策略列表
     */
    public List<ScoreCalculationStrategy> getApplicableStrategies(ScoreCalculationContext context) {
        return strategyMap.values().stream()
                .filter(strategy -> strategy.isApplicable(context))
                .collect(Collectors.toList());
    }
}
