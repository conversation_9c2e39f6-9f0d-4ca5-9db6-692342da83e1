package com.kc.system.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.kc.common.core.domain.entity.SysDept;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.kc.common.utils.DateUtils;
import com.kc.common.utils.SecurityUtils;
import com.kc.system.mapper.DeptBonusAllocationMapper;
import com.kc.system.domain.DeptBonusAllocation;
import com.kc.system.domain.dto.DeptBonusAllocationDTO;
import com.kc.system.service.IDeptBonusAllocationService;
import com.kc.system.service.ISysDeptService;

/**
 * 部门奖金分配Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Service
public class DeptBonusAllocationServiceImpl implements IDeptBonusAllocationService 
{
    @Autowired
    private DeptBonusAllocationMapper deptBonusAllocationMapper;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询部门奖金分配
     * 
     * @param id 部门奖金分配主键
     * @return 部门奖金分配
     */
    @Override
    public DeptBonusAllocation selectDeptBonusAllocationById(Long id)
    {
        return deptBonusAllocationMapper.selectDeptBonusAllocationById(id);
    }

    /**
     * 查询部门奖金分配列表
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 部门奖金分配
     */
    @Override
    public List<DeptBonusAllocation> selectDeptBonusAllocationList(DeptBonusAllocation deptBonusAllocation)
    {
        return deptBonusAllocationMapper.selectDeptBonusAllocationList(deptBonusAllocation);
    }

    /**
     * 根据部门ID和月份查询部门奖金分配
     */
    @Override
    public DeptBonusAllocation selectByDeptIdAndMonth(Long deptId, String allocationMonth)
    {
        return deptBonusAllocationMapper.selectByDeptIdAndMonth(deptId, allocationMonth);
    }

    /**
     * 根据月份查询所有部门奖金分配
     */
    @Override
    public List<DeptBonusAllocation> selectByMonth(String allocationMonth)
    {
        return deptBonusAllocationMapper.selectByMonth(allocationMonth);
    }

    /**
     * 新增部门奖金分配
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 结果
     */
    @Override
    public int insertDeptBonusAllocation(DeptBonusAllocation deptBonusAllocation)
    {
        deptBonusAllocation.setCreateTime(DateUtils.getNowDate());
        deptBonusAllocation.setCreateBy(SecurityUtils.getUsername());
        deptBonusAllocation.setRemainingBonus(deptBonusAllocation.getTotalBonus());
        return deptBonusAllocationMapper.insertDeptBonusAllocation(deptBonusAllocation);
    }

    /**
     * 批量分配部门奖金
     */
    @Override
    @Transactional
    public int batchAllocateDeptBonus(DeptBonusAllocationDTO deptBonusAllocationDTO)
    {
        String allocationMonth = deptBonusAllocationDTO.getAllocationMonth();
        
        // 检查是否已经分配过
        if (isMonthAllocated(allocationMonth)) {
            throw new RuntimeException("该月份已经分配过奖金，请先删除后重新分配");
        }

        List<DeptBonusAllocation> allocations = new ArrayList<>();
        String currentUser = SecurityUtils.getUsername();
        Long currentUserId = SecurityUtils.getUserId();
        Date now = DateUtils.getNowDate();

        for (DeptBonusAllocationDTO.DeptBonusItem item : deptBonusAllocationDTO.getDeptBonusList()) {
            // 获取部门信息
            SysDept dept = deptService.selectDeptById(item.getDeptId());
            if (dept == null) {
                throw new RuntimeException("部门不存在：" + item.getDeptId());
            }

            DeptBonusAllocation allocation = new DeptBonusAllocation();
            allocation.setDeptId(item.getDeptId());
            allocation.setDeptName(dept.getDeptName());
            allocation.setAllocationMonth(allocationMonth);
            allocation.setPerformanceRank(item.getPerformanceRank());
            allocation.setTotalBonus(item.getTotalBonus());
            allocation.setAllocatedBonus(BigDecimal.ZERO);
            allocation.setRemainingBonus(item.getTotalBonus());
            allocation.setAllocationStatus("0"); // 未分配
            allocation.setAllocatorId(currentUserId);
            allocation.setAllocatorName(currentUser);
            allocation.setCreateBy(currentUser);
            allocation.setCreateTime(now);
            allocation.setRemark(item.getRemark());

            allocations.add(allocation);
        }

        return deptBonusAllocationMapper.batchInsertDeptBonusAllocation(allocations);
    }

    /**
     * 修改部门奖金分配
     * 
     * @param deptBonusAllocation 部门奖金分配
     * @return 结果
     */
    @Override
    public int updateDeptBonusAllocation(DeptBonusAllocation deptBonusAllocation)
    {
        deptBonusAllocation.setUpdateTime(DateUtils.getNowDate());
        deptBonusAllocation.setUpdateBy(SecurityUtils.getUsername());
        return deptBonusAllocationMapper.updateDeptBonusAllocation(deptBonusAllocation);
    }

    /**
     * 更新部门奖金分配状态
     */
    @Override
    public int updateAllocationStatus(DeptBonusAllocation deptBonusAllocation)
    {
        return deptBonusAllocationMapper.updateAllocationStatus(
            deptBonusAllocation.getId(),
            deptBonusAllocation.getAllocatedBonus(),
            deptBonusAllocation.getRemainingBonus(),
            deptBonusAllocation.getAllocationStatus()
        );
    }

    /**
     * 批量删除部门奖金分配
     * 
     * @param ids 需要删除的部门奖金分配主键
     * @return 结果
     */
    @Override
    public int deleteDeptBonusAllocationByIds(Long[] ids)
    {
        return deptBonusAllocationMapper.deleteDeptBonusAllocationByIds(ids);
    }

    /**
     * 删除部门奖金分配信息
     * 
     * @param id 部门奖金分配主键
     * @return 结果
     */
    @Override
    public int deleteDeptBonusAllocationById(Long id)
    {
        return deptBonusAllocationMapper.deleteDeptBonusAllocationById(id);
    }

    /**
     * 根据月份删除部门奖金分配
     */
    @Override
    public int deleteByMonth(String allocationMonth)
    {
        return deptBonusAllocationMapper.deleteByMonth(allocationMonth);
    }

    /**
     * 检查指定月份是否已经分配过奖金
     */
    @Override
    public boolean isMonthAllocated(String allocationMonth)
    {
        List<DeptBonusAllocation> allocations = deptBonusAllocationMapper.selectByMonth(allocationMonth);
        return allocations != null && !allocations.isEmpty();
    }

    /**
     * 获取部门在指定月份的剩余奖金
     */
    @Override
    public BigDecimal getDeptRemainingBonus(Long deptId, String allocationMonth)
    {
        DeptBonusAllocation allocation = deptBonusAllocationMapper.selectByDeptIdAndMonth(deptId, allocationMonth);
        return allocation != null ? allocation.getRemainingBonus() : BigDecimal.ZERO;
    }
}
