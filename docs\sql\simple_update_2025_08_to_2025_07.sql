-- ===== 简单将2025-08改为2025-07 =====
-- 警告：此方案会产生重复数据，仅在确定要覆盖时使用

-- 第一步：创建备份
CREATE TABLE project_participation_backup_simple_20250715 AS 
SELECT * FROM project_participation WHERE month IN ('2025-07', '2025-08');

-- 第二步：查看修改前状态
SELECT 
    '修改前状态' as step,
    month, 
    COUNT(*) as count
FROM project_participation 
WHERE month IN ('2025-07', '2025-08') 
GROUP BY month;

-- 第三步：执行简单修改
UPDATE project_participation 
SET month = '2025-07'
WHERE month = '2025-08';

-- 第四步：查看修改后状态
SELECT 
    '修改后状态' as step,
    month, 
    COUNT(*) as count
FROM project_participation 
WHERE month IN ('2025-07', '2025-08') 
GROUP BY month;

-- 第五步：检查重复数据
SELECT 
    '重复数据检查' as step,
    user_name,
    project_id,
    COUNT(*) as duplicate_count
FROM project_participation 
WHERE month = '2025-07'
GROUP BY user_name, project_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC
LIMIT 10;

SELECT 
    '修改完成' as status,
    NOW() as completion_time,
    '警告：可能存在重复数据' as note;
