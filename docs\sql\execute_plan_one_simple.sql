-- ===== 方案一执行脚本：保留最新记录 =====
-- 执行前数据：2025-06(121条) + 2025-07(96条) + 2025-08(109条) = 326条
-- 冲突组合：52个用户-项目组合存在重复
-- 预期结果：保留最新记录，统一改为2025-06

-- 第一步：创建备份表
CREATE TABLE project_participation_backup_20250715 AS
SELECT * FROM project_participation WHERE month IN ('2025-06', '2025-07', '2025-08');

-- 第二步：创建要保留的记录列表
CREATE TEMPORARY TABLE records_to_keep AS
SELECT 
    user_name,
    project_id,
    MAX(created_at) as latest_created,
    CASE 
        WHEN MAX(created_at) = MAX(CASE WHEN month = '2025-07' THEN created_at END) THEN '2025-07'
        ELSE '2025-06'
    END as keep_month
FROM project_participation 
WHERE month IN ('2025-06', '2025-07')
GROUP BY user_name, project_id;

-- 第三步：删除冲突的旧记录
DELETE pp FROM project_participation pp
INNER JOIN records_to_keep rtk ON pp.user_name = rtk.user_name AND pp.project_id = rtk.project_id
WHERE pp.month IN ('2025-06', '2025-07')
AND NOT (pp.month = rtk.keep_month AND pp.created_at = rtk.latest_created);

-- 第四步：将所有剩余的2025-07记录改为2025-06
UPDATE project_participation 
SET month = '2025-06'
WHERE month = '2025-07';

-- 第五步：清理临时表
DROP TEMPORARY TABLE records_to_keep;

-- 第六步：验证结果
SELECT 
    '执行结果验证' as check_type,
    month, 
    COUNT(*) as count
FROM project_participation 
WHERE month IN ('2025-06', '2025-07', '2025-08') 
GROUP BY month 
ORDER BY month;

-- 检查重复数据
SELECT 
    '重复数据检查' as check_type,
    COUNT(*) as duplicate_groups
FROM (
    SELECT user_name, project_id, COUNT(*) as cnt
    FROM project_participation 
    WHERE month = '2025-06'
    GROUP BY user_name, project_id
    HAVING COUNT(*) > 1
) duplicates;

-- 显示完成状态
SELECT 
    '执行完成' as status,
    NOW() as completion_time,
    '所有2025-07数据已合并到2025-06，冲突记录已处理' as result;
