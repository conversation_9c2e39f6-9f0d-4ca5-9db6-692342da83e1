<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="按人员" name="person">
        <workload-statistics />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import WorkloadStatistics from './workloadstatistics.vue'

export default {
  name: "Statistics",
  components: {
    WorkloadStatistics
  },
  data() {
    return {
      activeName: 'person' // 默认激活按人员标签
    };
  },
  methods: {
    handleClick(tab, event) {
      // 处理标签页切换事件
      console.log(tab, event);
    }
  }
};
</script>

<style scoped>
/* 移除 app-container 的 padding */
/deep/ .app-container {
  padding: 0;
}

/* 标签页样式调整 */
.el-tabs__item {
  height: 40px;
  line-height: 40px;
}

.el-tab-pane {
  padding: 20px 0;
}
</style>
