package com.kc.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Calendar;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.kc.system.mapper.ProjectEvaluationMapper;
import com.kc.system.mapper.HighScoreRecordMapper;
import com.kc.system.mapper.SysUserMapper;
import com.kc.system.mapper.EvaluationResultMapper;
import com.kc.system.domain.ProjectEvaluation;
import com.kc.system.domain.HighScoreRecord;
import com.kc.system.domain.ProjectInfo;
import com.kc.system.domain.EvaluationResult;
import com.kc.system.service.IProjectEvaluationService;
import com.kc.system.service.ISysUserService;
import com.kc.system.service.IDeptHighScoreQuotaService;
import com.kc.system.service.IProjectInfoService;
import com.kc.system.service.ISysDeptService;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.common.core.domain.entity.SysDept;

/**
 * 项目评价Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
@Service
public class ProjectEvaluationServiceImpl implements IProjectEvaluationService
{
    private static final Logger log = LoggerFactory.getLogger(ProjectEvaluationServiceImpl.class);

    @Autowired
    private ProjectEvaluationMapper projectEvaluationMapper;

    @Autowired
    private HighScoreRecordMapper highScoreRecordMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private EvaluationResultMapper evaluationResultMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IDeptHighScoreQuotaService deptHighScoreQuotaService;

    @Autowired
    private IProjectInfoService projectInfoService;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询项目评价
     * 
     * @param id 项目评价主键
     * @return 项目评价
     */
    @Override
    public ProjectEvaluation selectProjectEvaluationById(Long id)
    {
        return projectEvaluationMapper.selectProjectEvaluationById(id);
    }

    /**
     * 查询项目评价列表
     * 
     * @param projectEvaluation 项目评价
     * @return 项目评价
     */
    @Override
    public List<ProjectEvaluation> selectProjectEvaluationList(ProjectEvaluation projectEvaluation)
    {
        List<ProjectEvaluation> list = projectEvaluationMapper.selectProjectEvaluationList(projectEvaluation);

        // 填充项目名称、评价人姓名和被评价人姓名（用于导出）
        for (ProjectEvaluation evaluation : list) {
            // 填充项目名称
            if (evaluation.getProjectId() != null && evaluation.getProjectId() != 0) {
                ProjectInfo projectInfo = projectInfoService.selectProjectInfoById(evaluation.getProjectId());
                if (projectInfo != null) {
                    evaluation.setProjectName(projectInfo.getProjectName());
                }
            } else {
                // 机构负责人评价时项目名称设置为空
                evaluation.setProjectName(null);
            }

            // 填充评价人姓名
            if (evaluation.getEvaluatorId() != null) {
                SysUser evaluator = userService.selectUserById(evaluation.getEvaluatorId());
                if (evaluator != null) {
                    evaluation.setEvaluatorName(evaluator.getNickName() != null ? evaluator.getNickName() : evaluator.getUserName());
                }
            }

            // 填充被评价人姓名
            if (evaluation.getEvaluateeId() != null) {
                SysUser evaluatee = userService.selectUserById(evaluation.getEvaluateeId());
                if (evaluatee != null) {
                    evaluation.setEvaluateeName(evaluatee.getNickName() != null ? evaluatee.getNickName() : evaluatee.getUserName());
                }
            }
        }

        return list;
    }

    /**
     * 新增项目评价
     *
     * @param projectEvaluation 项目评价
     * @return 结果
     */
    @Override
    public int insertProjectEvaluation(ProjectEvaluation projectEvaluation)
    {
        int result = projectEvaluationMapper.insertProjectEvaluation(projectEvaluation);

        // 如果是机构负责人评分，需要为该用户负责的项目创建项目负责人评分记录
        if (result > 0 && "manager".equals(projectEvaluation.getEvaluationType())) {
            createProjectLeaderEvaluationsForManager(projectEvaluation);
        }

        // 如果是机构负责人评分（包括manager和parent_manager）且分数>=95，记录到高分记录表
        if (result > 0 && ("manager".equals(projectEvaluation.getEvaluationType())
            || "parent_manager".equals(projectEvaluation.getEvaluationType()))
            && projectEvaluation.getScore() != null
            && projectEvaluation.getScore().compareTo(new BigDecimal("95")) >= 0) {

            handleHighScoreRecord(projectEvaluation, "insert");
        }

        return result;
    }

    /**
     * 修改项目评价
     *
     * @param projectEvaluation 项目评价
     * @return 结果
     */
    @Override
    public int updateProjectEvaluation(ProjectEvaluation projectEvaluation)
    {
        int result = projectEvaluationMapper.updateProjectEvaluation(projectEvaluation);

        // 如果是机构负责人评分，需要为该用户负责的项目创建项目负责人评分记录
        if (result > 0 && "manager".equals(projectEvaluation.getEvaluationType())) {
            createProjectLeaderEvaluationsForManager(projectEvaluation);
        }

        // 如果是机构负责人评分（包括manager和parent_manager），处理高分记录
        if (result > 0 && ("manager".equals(projectEvaluation.getEvaluationType())
            || "parent_manager".equals(projectEvaluation.getEvaluationType()))) {
            handleHighScoreRecord(projectEvaluation, "update");
        }

        return result;
    }

    /**
     * 批量删除项目评价
     * 
     * @param ids 需要删除的项目评价主键
     * @return 结果
     */
    @Override
    public int deleteProjectEvaluationByIds(Long[] ids)
    {
        return projectEvaluationMapper.deleteProjectEvaluationByIds(ids);
    }

    /**
     * 删除项目评价信息
     * 
     * @param id 项目评价主键
     * @return 结果
     */
    @Override
    public int deleteProjectEvaluationById(Long id)
    {
        return projectEvaluationMapper.deleteProjectEvaluationById(id);
    }

    /**
     * 获取用户参与的项目评分列表
     * 
     * @param userName 用户名
     * @param evaluationMonth 评价月份 (可选)
     * @return 项目评分列表
     */
    @Override
    public List<ProjectEvaluation> selectUserProjectEvaluations(String userName, String evaluationMonth)
    {
        return projectEvaluationMapper.selectUserProjectEvaluations(userName, evaluationMonth);
    }

    /**
     * 通过用户ID获取项目评分列表
     *
     * @param userId 用户ID
     * @param evaluationMonth 评价月份 (可选)
     * @return 项目评分列表
     */
    @Override
    public List<ProjectEvaluation> selectUserProjectEvaluationsByUserId(Long userId, String evaluationMonth)
    {
        return projectEvaluationMapper.selectUserProjectEvaluationsByUserId(userId, evaluationMonth);
    }

    /**
     * 为机构负责人评分创建对应的项目负责人评分记录
     * 当机构负责人对某用户评分时，如果该用户是项目负责人，
     * 需要为其负责的项目创建项目负责人评分记录（使用机构负责人评分）
     */
    private void createProjectLeaderEvaluationsForManager(ProjectEvaluation managerEvaluation) {
        try {
            Long evaluateeId = managerEvaluation.getEvaluateeId();
            String evaluationMonth = managerEvaluation.getEvaluationMonth();
            BigDecimal managerScore = managerEvaluation.getScore();
            Long evaluatorId = managerEvaluation.getEvaluatorId();

            log.info("为用户[{}]创建项目负责人评分记录，机构负责人评分={}", evaluateeId, managerScore);

            // 查询该用户负责的项目
            List<Map<String, Object>> leaderProjects = projectEvaluationMapper.getUserLeaderProjects(evaluateeId);

            if (leaderProjects == null || leaderProjects.isEmpty()) {
                log.info("用户[{}]不是任何项目的负责人，无需创建项目负责人评分记录", evaluateeId);
                return;
            }

            log.info("用户[{}]是{}个项目的负责人，开始创建项目负责人评分记录", evaluateeId, leaderProjects.size());

            // 为每个负责的项目创建项目负责人评分记录
            for (Map<String, Object> project : leaderProjects) {
                Long projectId = ((Number) project.get("project_id")).longValue();
                String projectName = (String) project.get("project_name");

                // 检查是否已存在该项目的评分记录
                ProjectEvaluation existingEvaluation = projectEvaluationMapper.selectByEvaluateeProjectAndMonth(
                    evaluateeId, projectId, evaluationMonth, "project_leader");

                if (existingEvaluation == null) {
                    // 创建新的项目负责人评分记录
                    ProjectEvaluation projectLeaderEvaluation = new ProjectEvaluation();
                    projectLeaderEvaluation.setProjectId(projectId);
                    projectLeaderEvaluation.setEvaluatorId(evaluatorId); // 使用机构负责人作为评价人
                    projectLeaderEvaluation.setEvaluateeId(evaluateeId);
                    projectLeaderEvaluation.setScore(managerScore); // 使用机构负责人评分
                    projectLeaderEvaluation.setEvaluationMonth(evaluationMonth);
                    projectLeaderEvaluation.setEvaluationType("project_leader");
                    projectLeaderEvaluation.setComments("系统自动生成：基于机构负责人评分的项目负责人自评替代");

                    int insertResult = projectEvaluationMapper.insertProjectEvaluation(projectLeaderEvaluation);
                    if (insertResult > 0) {
                        log.info("为用户[{}]项目[{}]({})创建项目负责人评分记录成功，评分={}",
                                evaluateeId, projectId, projectName, managerScore);
                    } else {
                        log.error("为用户[{}]项目[{}]({})创建项目负责人评分记录失败",
                                evaluateeId, projectId, projectName);
                    }
                } else {
                    log.info("用户[{}]项目[{}]({})已存在项目负责人评分记录，跳过创建",
                            evaluateeId, projectId, projectName);
                }
            }

        } catch (Exception e) {
            log.error("为机构负责人评分创建项目负责人评分记录时发生错误", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 处理高分记录
     *
     * @param projectEvaluation 项目评价
     * @param operation 操作类型：insert 或 update
     */
    private void handleHighScoreRecord(ProjectEvaluation projectEvaluation, String operation) {
        try {
            // 获取评价年度
            String evaluationYear = null;
            if (projectEvaluation.getEvaluationMonth() != null && projectEvaluation.getEvaluationMonth().length() >= 4) {
                evaluationYear = projectEvaluation.getEvaluationMonth().substring(0, 4);
            } else {
                Calendar calendar = Calendar.getInstance();
                evaluationYear = String.valueOf(calendar.get(Calendar.YEAR));
            }

            // 获取被评价用户信息
            SysUser evaluatee = userService.selectUserById(projectEvaluation.getEvaluateeId());
            if (evaluatee == null) {
                return;
            }

            // 获取评价人信息
            SysUser evaluator = userService.selectUserById(projectEvaluation.getEvaluatorId());

            if ("update".equals(operation)) {
                // 更新操作：先删除旧记录，再根据新分数决定是否插入
                HighScoreRecord existingRecord = highScoreRecordMapper.selectByUserAndYear(
                    projectEvaluation.getEvaluateeId(), evaluationYear);

                if (existingRecord != null) {
                    highScoreRecordMapper.deleteHighScoreRecordById(existingRecord.getId());
                }
            }

            // 如果分数>=95，使用配额管理系统处理高分记录
            if (projectEvaluation.getScore() != null
                && projectEvaluation.getScore().compareTo(new BigDecimal("95")) >= 0) {

                // 对于子部门成员，需要使用父部门的配额
                Long quotaDeptId = evaluatee.getDeptId();

                // 如果是parent_manager类型的评价，说明是父部门负责人评价子部门成员
                // 需要根据配额组设置来确定使用哪个部门的配额
                if ("parent_manager".equals(projectEvaluation.getEvaluationType())) {
                    // 对于配额组，使用配额组的配额；对于非配额组，使用被评价人所在部门的配额
                    quotaDeptId = evaluatee.getDeptId();
                }

                // 使用配额管理系统来处理高分记录和配额
                boolean success = deptHighScoreQuotaService.useQuota(
                    quotaDeptId,
                    projectEvaluation.getEvaluateeId(),
                    evaluationYear,
                    projectEvaluation.getEvaluationMonth(),
                    projectEvaluation.getScore(),
                    projectEvaluation.getEvaluatorId()
                );

                if (!success) {
                    System.err.println("配额不足，无法记录高分: 用户ID=" + projectEvaluation.getEvaluateeId()
                        + ", 部门ID=" + quotaDeptId + ", 年度=" + evaluationYear);
                }
            }
        } catch (Exception e) {
            // 记录错误但不影响主流程
            System.err.println("处理高分记录失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 验证机构负责人评分前置条件
     * 检查被评分人是否为项目工作人员，如果是，则检查项目负责人是否已评分
     *
     * @param evaluateeId 被评价人ID
     * @param evaluationMonth 评价月份
     * @return 验证结果，包含是否通过和详细信息
     */
    @Override
    public Map<String, Object> validateManagerEvaluationPreconditions(Long evaluateeId, String evaluationMonth) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 检查用户是否有项目精力分配
            List<Map<String, Object>> userProjectEfforts = projectEvaluationMapper.getUserProjectEffortDetails(evaluateeId, evaluationMonth);

            if (userProjectEfforts == null || userProjectEfforts.isEmpty()) {
                // 如果没有项目精力分配，可以直接评分
                result.put("valid", true);
                result.put("message", "验证通过");
                return result;
            }

            // 2. 如果有项目精力分配，检查每个项目的负责人是否已评分
            List<Map<String, Object>> missingEvaluations = new ArrayList<>();

            for (Map<String, Object> effort : userProjectEfforts) {
                Long projectId = ((Number) effort.get("project_id")).longValue();
                String projectName = (String) effort.get("project_name");

                // 获取项目负责人信息
                Map<String, Object> projectLeaderInfo = projectEvaluationMapper.getProjectLeaderInfo(projectId);

                // 检查被评价人是否就是该项目的负责人
                boolean isEvaluateeTheProjectLeader = false;
                if (projectLeaderInfo != null) {
                    Object leaderUserIdObj = projectLeaderInfo.get("leader_user_id");
                    if (leaderUserIdObj != null) {
                        Long leaderUserId = ((Number) leaderUserIdObj).longValue();
                        isEvaluateeTheProjectLeader = evaluateeId.equals(leaderUserId);
                    }
                }

                // 如果被评价人本身就是该项目的负责人，跳过该项目的检查
                if (isEvaluateeTheProjectLeader) {
                    continue;
                }

                // 检查该项目的负责人是否已对该用户评分
                boolean hasEvaluation = projectEvaluationMapper.checkProjectLeaderEvaluationForProject(
                    evaluateeId, projectId, evaluationMonth);

                if (!hasEvaluation) {
                    Map<String, Object> missingInfo = new HashMap<>();
                    missingInfo.put("projectId", projectId);
                    missingInfo.put("projectName", projectName);
                    missingInfo.put("leaderName", projectLeaderInfo != null ? projectLeaderInfo.get("leader_name") : "未知");
                    missingInfo.put("leaderUserName", projectLeaderInfo != null ? projectLeaderInfo.get("leader_user_name") : "未知");

                    missingEvaluations.add(missingInfo);
                }
            }

            if (missingEvaluations.isEmpty()) {
                // 所有项目负责人都已评分
                result.put("valid", true);
                result.put("message", "验证通过");
            } else {
                // 检查是否至少有一个项目负责人已评分
                int totalProjects = userProjectEfforts.size();
                int evaluatedProjects = totalProjects - missingEvaluations.size();

                if (evaluatedProjects > 0) {
                    // 至少有一个项目负责人已评分，允许机构负责人评分
                    result.put("valid", true);
                    result.put("message", "验证通过（部分项目负责人已评分）");
                    result.put("warning", String.format("注意：%d个项目中有%d个项目负责人尚未评分", totalProjects, missingEvaluations.size()));
                } else {
                    // 没有任何项目负责人评分
                    result.put("valid", false);
                    result.put("missingEvaluations", missingEvaluations);

                    // 构建详细的错误信息
                    StringBuilder message = new StringBuilder("以下项目的负责人尚未完成评分：\n");
                    for (Map<String, Object> missing : missingEvaluations) {
                        message.append("• 项目：").append(missing.get("projectName"))
                               .append("，负责人：").append(missing.get("leaderName"))
                               .append("\n");
                    }
                    message.append("请先完成项目负责人评分后再进行机构负责人评分。");
                    result.put("message", message.toString());
                }
            }

            return result;

        } catch (Exception e) {
            // 出现异常时，为了安全起见，允许评分
            System.err.println("验证机构负责人评分前置条件失败: " + e.getMessage());
            result.put("valid", true);
            result.put("message", "验证过程中出现异常，允许评分");
            return result;
        }
    }

    /**
     * 导出部门评分数据
     *
     * 数据结构说明：
     * - 每个用户的每个精力分配项目占一行
     * - 精力分配：该用户在该项目上的精力分配比例（小数格式，如0.30）
     * - 项目负责人评分：针对该用户在该项目上的评分
     * - 机构负责人评分：针对该用户的整体评分，只在该用户的第一个项目行显示，其他行为空
     *
     * 示例：
     * 张三被分配到项目A(0.30)和项目B(0.40)，机构负责人给张三打了90分，最终得分为87分
     * 导出结果：
     * 张三, 项目A, 0.30, 项目A负责人, 85, 90, 87
     * 张三, 项目B, 0.40, 项目B负责人, 88,   (空),   (空)
     *
     * @param response HTTP响应对象
     * @param deptId 部门ID
     * @param evaluationMonth 评价月份
     * @param deptName 部门名称
     * @param userIds 用户ID列表（可选，如果为null则导出所有用户）
     */
    @Override
    public void exportDeptEvaluationData(HttpServletResponse response, Long deptId, String evaluationMonth, String deptName, List<Long> userIds) {
        try {
            // 1. 获取部门所有用户
            SysUser userQuery = new SysUser();
            userQuery.setDeptId(deptId);
            List<SysUser> allDeptUsers = sysUserMapper.selectUserList(userQuery);

            // 2. 获取部门信息，确定机构负责人
            SysDept dept = deptService.selectDeptById(deptId);
            String deptLeaderUserName = null;
            if (dept != null && dept.getLeader() != null) {
                deptLeaderUserName = dept.getLeader();
            }

            // 3. 排除机构负责人，只保留需要被评分的用户
            List<SysUser> deptUsers = new ArrayList<>();
            for (SysUser user : allDeptUsers) {
                // 排除机构负责人（通过用户名匹配）
                if (deptLeaderUserName != null && deptLeaderUserName.equals(user.getUserName())) {
                    continue; // 跳过机构负责人
                }
                deptUsers.add(user);
            }

            // 4. 如果指定了用户ID列表，则只保留指定的用户
            if (userIds != null && !userIds.isEmpty()) {
                deptUsers = deptUsers.stream()
                    .filter(user -> userIds.contains(user.getUserId()))
                    .collect(java.util.stream.Collectors.toList());
                log.info("根据用户ID列表过滤，保留 {} 个用户", deptUsers.size());
            }

            // 5. 准备导出数据
            List<Map<String, Object>> exportData = new ArrayList<>();

            for (SysUser user : deptUsers) {
                Long userId = user.getUserId();
                String userName = user.getNickName() != null ? user.getNickName() : user.getUserName();

                // 3. 获取用户的精力分配项目
                List<Map<String, Object>> userProjectEfforts = projectEvaluationMapper.getUserProjectEffortDetails(userId, evaluationMonth);

                // 4. 获取机构负责人评分
                ProjectEvaluation managerEvaluation = new ProjectEvaluation();
                managerEvaluation.setEvaluateeId(userId);
                managerEvaluation.setEvaluationMonth(evaluationMonth);
                managerEvaluation.setEvaluationType("manager");
                List<ProjectEvaluation> managerEvals = projectEvaluationMapper.selectProjectEvaluationList(managerEvaluation);

                String managerScore = "";
                if (managerEvals != null && !managerEvals.isEmpty()) {
                    BigDecimal score = managerEvals.get(0).getScore();
                    managerScore = score != null ? score.toString() : "";
                }

                // 5. 获取最终得分
                String finalScore = "";
                try {
                    EvaluationResult resultQuery = new EvaluationResult();
                    resultQuery.setUserId(userId);
                    resultQuery.setEvaluationMonth(evaluationMonth);
                    List<EvaluationResult> finalScoreResults = evaluationResultMapper.selectEvaluationResultList(resultQuery);
                    if (finalScoreResults != null && !finalScoreResults.isEmpty()) {
                        BigDecimal score = finalScoreResults.get(0).getFinalScore();
                        if (score != null) {
                            finalScore = score.toString();
                        }
                    }
                } catch (Exception e) {
                    // 静默处理最终得分获取失败的情况
                }

                if (userProjectEfforts != null && !userProjectEfforts.isEmpty()) {
                    // 有精力分配项目的用户 - 机构负责人评分只在第一行显示
                    for (int i = 0; i < userProjectEfforts.size(); i++) {
                        Map<String, Object> effort = userProjectEfforts.get(i);
                        String projectName = (String) effort.get("project_name");
                        String projectLeader = (String) effort.get("leader_name");
                        Long projectId = ((Number) effort.get("project_id")).longValue();

                        // 获取精力分配比例
                        BigDecimal participationRate = (BigDecimal) effort.get("participation_rate");
                        String effortAllocation = "";
                        if (participationRate != null) {
                            // 保持小数格式，保留2位小数
                            effortAllocation = participationRate.setScale(2, RoundingMode.HALF_UP).toString();
                        }

                        // 获取项目负责人评分
                        String projectLeaderScore = "";
                        ProjectEvaluation projectEvalQuery = new ProjectEvaluation();
                        projectEvalQuery.setEvaluateeId(userId);
                        projectEvalQuery.setProjectId(projectId);
                        projectEvalQuery.setEvaluationMonth(evaluationMonth);
                        projectEvalQuery.setEvaluationType("project_leader");
                        List<ProjectEvaluation> projectEvals = projectEvaluationMapper.selectProjectEvaluationList(projectEvalQuery);
                        if (projectEvals != null && !projectEvals.isEmpty()) {
                            BigDecimal score = projectEvals.get(0).getScore();
                            projectLeaderScore = score != null ? score.toString() : "";
                        }

                        Map<String, Object> rowData = new HashMap<>();
                        rowData.put("姓名", userName);
                        rowData.put("项目名称", projectName != null ? projectName : "未知项目");
                        rowData.put("精力分配", effortAllocation);
                        rowData.put("项目负责人", projectLeader != null ? projectLeader : "未知");
                        rowData.put("项目负责人评分", projectLeaderScore);
                        // 机构负责人评分和最终得分只在该用户的第一个项目行显示，其他行为空
                        rowData.put("机构负责人评分", i == 0 ? managerScore : "");
                        rowData.put("最终得分", i == 0 ? finalScore : "");

                        exportData.add(rowData);
                    }
                } else {
                    // 没有精力分配项目的用户
                    Map<String, Object> rowData = new HashMap<>();
                    rowData.put("姓名", userName);
                    rowData.put("项目名称", "无项目");
                    rowData.put("精力分配", "0.00");
                    rowData.put("项目负责人", "");
                    rowData.put("项目负责人评分", "");
                    rowData.put("机构负责人评分", managerScore);
                    rowData.put("最终得分", finalScore);

                    exportData.add(rowData);
                }
            }

            // 5. 生成CSV文件
            generateCSVResponse(response, exportData, deptName, evaluationMonth);

        } catch (Exception e) {
            // 记录错误但不输出到控制台
            // 可以考虑使用日志框架记录错误
        }
    }

    /**
     * 生成CSV响应
     */
    private void generateCSVResponse(HttpServletResponse response, List<Map<String, Object>> data, String deptName, String evaluationMonth) throws IOException {
        String fileName = deptName + "_评分数据_" + evaluationMonth + ".csv";

        response.setContentType("text/csv;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        try (OutputStream out = response.getOutputStream()) {
            // 写入BOM以支持中文
            out.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});

            // 写入表头
            String header = "姓名,项目名称,精力分配,项目负责人,项目负责人评分,机构负责人评分,最终得分\n";
            out.write(header.getBytes("UTF-8"));

            // 写入数据
            for (Map<String, Object> row : data) {
                StringBuilder line = new StringBuilder();
                line.append("\"").append(row.get("姓名")).append("\",");
                line.append("\"").append(row.get("项目名称")).append("\",");
                line.append("\"").append(row.get("精力分配")).append("\",");
                line.append("\"").append(row.get("项目负责人")).append("\",");
                line.append("\"").append(row.get("项目负责人评分")).append("\",");
                line.append("\"").append(row.get("机构负责人评分")).append("\",");
                line.append("\"").append(row.get("最终得分")).append("\"");
                line.append("\n");

                out.write(line.toString().getBytes("UTF-8"));
            }

            out.flush();
        }
    }
}
