<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="统计月份" prop="workMonth">
        <el-date-picker
          v-model="queryParams.workMonth"
          type="month"
          placeholder="选择月份"
          value-format="yyyy-MM"
          style="width: 140px"
          @change="handleMonthChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-check"
          size="mini"
          @click="handleCheck"
          :loading="checkLoading"
        >{{ checkLoading ? '校验中' : '校验工作量归集' }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:workload:export']"
          :loading="exportLoading"
        >{{ exportLoading ? '正在导出' : '导出统计' }}</el-button>
      </el-col>

      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAddMember"
          v-hasPermi="['system:members:add']"
        >添加项目成员</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          size="mini"
          @click="fetchUnfilledWorkloadCount"
        >查看未填报工时</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 提示信息 -->
    <div v-if="unfilledWorkloadInfo" class="unfilled-info">
      <el-alert
        title="未填报工作量归集信息"
        type="warning"
        :description="unfilledWorkloadInfo"
        show-icon
      ></el-alert>
    </div>

    <!-- 工作量归集统计表格 -->
    <el-table
      v-loading="loading"
      :data="workloadList"
      style="width: 100%"
    >
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="员工编号" align="center" prop="userName" width="100" />
      <el-table-column label="姓名" align="center" prop="nickName" min-width="100" />
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="150" />
      <el-table-column label="项目角色" align="center" prop="role" width="100" />
      <el-table-column label="统计月份" align="center" prop="workMonth" width="120" />
      <el-table-column label="工作量归集" align="center" prop="involvement" width="120">
        <template slot-scope="scope">
          {{ scope.row.involvement ? scope.row.involvement.toFixed(2) : '0.00' }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { listWorkload, getWorkload, delWorkload, addWorkload, updateWorkload, getUnfilledWorkloadCount } from "@/api/system/workload";
import { listInfo } from "@/api/system/info";
import { listUser } from "@/api/system/user";
import { getUserProjectWorkloads } from "@/api/system/info";
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';


export default {
  name: "WorkloadStatistics",
  components: {
    Pagination: () => import("@/components/Pagination")
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工作量归集统计数据
      workloadList: [],
      // 当前选中的项目ID
      currentProjectId: null,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        workMonth: this.getCurrentMonth() // 恢复为当前月份
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "成员不能为空", trigger: "blur" }
        ],
        workMonth: [
          { required: true, message: "工作月份不能为空", trigger: "blur" }
        ],
        involvement: [
          { required: true, message: "项目参与度不能为空", trigger: "blur" }
        ]
      },
      // 添加选中的成员信息
      selectedMember: null,
      // 用户所有项目工时数据
      userProjectWorkloads: [],
      exportLoading: false,
      unfilledWorkloadInfo: '', // 用于存储未填报工时的信息
      checkLoading: false,
      checkPassed: false,

    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 获取当前月份 YYYY-MM 格式 */
    getCurrentMonth() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${year}-${month}`;
    },
    /** 查询工作量归集统计列表 */
    getList() {
      this.loading = true;
      // 修改请求参数，添加 pageSize 参数获取所有数据
      const params = {
        ...this.queryParams,
        pageSize: 999999 // 设置一个较大的数值以获取所有数据
      };

      listWorkload(params).then(response => {
        // 后端已经返回正确的数据，直接使用
        this.workloadList = response.rows.filter(item => {
          // 只过滤掉配合人员
          return item.role !== '配合人员';
        }).map(item => ({
          ...item,
          nickName: item.nickName || '',
          projectName: item.projectName || '',
          role: item.role || '',
          workMonth: item.workMonth || '',
          involvement: parseFloat(item.involvement) || 0
        }));

        this.loading = false;
      }).catch(error => {
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.workMonth = this.getCurrentMonth(); // 重置时设为当前月份
      this.handleQuery();
    },
    /** 月份变化处理 */
    handleMonthChange() {
      this.handleQuery();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        userName: null,
        workMonth: null,
        involvement: null,
        createdAt: null,
        updatedAt: null,
        role: "参与"  // 设置默认角色
      };

      // 重置两个表单
      this.resetForm("form");
      this.resetForm("memberForm");
    },
    /** 新增按钮操作 */
    handleAdd() {
      if (!this.selectedMember || !this.selectedMember.userName) {
        this.$modal.msgWarning("请选择填报对象");
        return;
      }

      this.reset();
      this.form.projectId = this.currentProjectId;
      this.form.userName = this.selectedMember.userName;

      // 设置当前月份并获取工时数据
      const now = new Date();
      const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      this.form.workMonth = currentMonth;

      // 获取用户所有项目工时
      this.getUserProjectWorkloads(this.selectedMember.userName, currentMonth);

      this.open = true;
      this.title = "添加项目工时记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getWorkload(id).then(response => {
        this.form = response.data;

        // 获取用户所有项目工时
        this.getUserProjectWorkloads(this.form.userName, this.form.workMonth);

        this.open = true;
        this.title = "修改项目工时记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id == null) {
            // 新增时才检查重复填报
            const hasMonthRecord = this.workloadList.some(item =>
              item.workMonth === this.form.workMonth
            );

            if (hasMonthRecord) {
              this.$modal.msgError("该月份已填报工时，请勿重复填报");
              return;
            }
          }

          // 计算所有项目参与度之和
          const totalInvolvement = this.userProjectWorkloads.reduce((sum, project) => {
            // 如果是当前编辑的项目，使用表单中的值
            if (project.projectId === this.currentProjectId) {
              return sum + (this.form.involvement || 0);
            }
            // 如果是正在修改的记录，跳过原值的计算
            if (this.form.id != null && project.projectId === this.form.projectId) {
              return sum;
            }
            // 其他项目使用原有的值，-1 表示未填报，计为 0
            return sum + (project.involvement === -1 ? 0 : project.involvement);
          }, 0);

          // 检查总参与度是否超过1
          if (totalInvolvement > 1) {
            this.$modal.msgError("所有项目参与度之和不能超过1，当前总和为：" + totalInvolvement.toFixed(2));
            return;
          }

          if (this.form.id != null) {
            updateWorkload(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWorkload(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除项目工时记录编号为"' + ids + '"的数据项？').then(function() {
        return delWorkload(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 获取用户所有项目工时
    getUserProjectWorkloads(userName, month) {
      getUserProjectWorkloads(userName, month).then(response => {
        this.userProjectWorkloads = response.data;

        // 找到当前项目的工时记录
        const currentProject = this.userProjectWorkloads.find(p => p.projectId === this.currentProjectId);

        if (currentProject && currentProject.involvement !== -1) {
          // 如果当月有数据，使用当月数据
          this.form.involvement = currentProject.involvement;
        } else {
          // 如果当月没有数据，获取上月数据
          const lastMonth = this.getLastMonth(month);
          getUserProjectWorkloads(userName, lastMonth).then(lastMonthResponse => {
            const lastMonthProject = lastMonthResponse.data.find(p => p.projectId === this.currentProjectId);
            // 如果有上月数据，使用上月数据，否则默认为0
            this.form.involvement = lastMonthProject && lastMonthProject.involvement !== -1 ?
              lastMonthProject.involvement : 0;
          });
        }
      });
    },
    // 获取上个月的日期字符串 (yyyy-MM)
    getLastMonth(currentMonth) {
      const [year, month] = currentMonth.split('-').map(Number);
      const date = new Date(year, month - 1, 1); // 月份从0开始
      date.setMonth(date.getMonth() - 1);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有工作量归集统计数据?').then(() => {
        this.exportLoading = true;
        this.$modal.loading("正在导出数据，请稍候...");

        try {
          // 准备导出数据
          const exportData = this.workloadList.map((item, index) => {
            return {
              '序号': index + 1,
              '员工编号': item.userName,
              '姓名': item.nickName,
              '项目名称': item.projectName,
              '项目角色': item.role,
              '统计月份': item.workMonth,
              '工作量归集': item.involvement ? item.involvement.toFixed(2) : '0.00'
            };
          });

          // 创建工作簿
          const wb = XLSX.utils.book_new();
          // 创建工作表
          const ws = XLSX.utils.json_to_sheet(exportData);
          // 设置列宽
          const colWidth = [
            { wch: 8 },  // 序号
            { wch: 12 }, // 员工编号
            { wch: 15 }, // 姓名
            { wch: 30 }, // 项目名称
            { wch: 12 }, // 项目角色
            { wch: 12 }, // 统计月份
            { wch: 12 }  // 工作量归集
          ];
          ws['!cols'] = colWidth;

          // 添加工作表到工作簿
          XLSX.utils.book_append_sheet(wb, ws, '工作量归集统计');

          // 生成文件名
          const fileName = `工作量归集统计_${this.queryParams.workMonth || '全部'}_${new Date().getTime()}.xlsx`;

          // 导出文件
          XLSX.writeFile(wb, fileName);

          this.$modal.closeLoading();
          this.$modal.msgSuccess("导出成功");
        } catch (error) {
          this.$modal.closeLoading();
          this.$modal.msgError("导出失败");
        } finally {
          this.exportLoading = false;
        }
      }).catch(() => {
        this.exportLoading = false;
      });
    },
    /** 获取未填报工时的信息 */
    fetchUnfilledWorkloadCount() {
      const workMonth = this.queryParams.workMonth; // 获取当前选择的工作月份
      getUnfilledWorkloadCount(workMonth).then(response => {
        const data = response.data;
        if (data && data.length > 0) {
          const info = data.map(item => {
            return `${item.projectName}: ${item.unfilledCount}人未填报工时 (未填报人员: ${item.unfilledUsers})`;
          }).join('<br/>');
          this.unfilledWorkloadInfo = info; // 设置提示信息
        } else {
          this.unfilledWorkloadInfo = '本月所有项目均已填报工时。';
        }
      }).catch(error => {
        this.$modal.msgError('获取未填报工时信息失败：' + error.message);
      });
    },


    /** 校验数据 */
    async handleCheck() {
      this.checkLoading = true;
      this.checkPassed = false;

      try {
        let hasError = false;
        let errorMessages = [];

        // 按用户名分组工时数据
        const userWorkloads = new Map();
        this.workloadList.forEach(workload => {
          if (!userWorkloads.has(workload.userName)) {
            userWorkloads.set(workload.userName, []);
          }
          userWorkloads.get(workload.userName).push(workload);
        });

        // 检查每个用户的工时
        for (let [userName, workloads] of userWorkloads) {
          // 计算用户当月所有项目参与度之和
          const totalInvolvement = workloads.reduce((sum, workload) => {
            return sum + (workload.involvement || 0);
          }, 0);

          const nickName = workloads[0].nickName;
          if (Math.abs(totalInvolvement - 1) > 0.01) { // 使用小数点精度比较
            hasError = true;
            const message = totalInvolvement > 1
              ? `${nickName}的工作量归集总和(${totalInvolvement.toFixed(2)})超过了1`
              : `${nickName}的工作量归集总和(${totalInvolvement.toFixed(2)})小于1`;
            errorMessages.push(message);
          }
        }

        this.checkPassed = !hasError;

        if (hasError) {
          // 修改错误信息的显示方式
          this.$msgbox({
            title: '校验结果',
            message: `<div class="check-error-messages">
              <p>以下员工的工作量归集数据有问题：</p>
              <ul>
                ${errorMessages.map(msg => `<li>${msg}</li>`).join('')}
              </ul>
            </div>`,
            dangerouslyUseHTMLString: true,  // 允许使用 HTML 字符串
            type: 'error',
            showCancelButton: false,
            confirmButtonText: '确定'
          });
        } else {
          // 校验通过显示成功提示
          this.$msgbox({
            title: '校验结果',
            message: '校验通过，可以导出数据',
            type: 'success',
            showCancelButton: false,
            confirmButtonText: '确定'
          });
        }
      } catch (error) {
        this.$msgbox({
          title: '校验结果',
          message: '校验过程发生错误',
          type: 'error',
          showCancelButton: false,
          confirmButtonText: '确定'
        });
      } finally {
        this.checkLoading = false;
      }
    },
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

/* 添加开关样式 */
.el-switch {
  margin-top: 5px;
}

.el-switch__label {
  color: #606266;
}

.unfilled-info {
  margin-top: 10px;
}

/* 添加弹框内容样式 */
.check-error-messages p {
  margin: 0 0 10px 0;
  color: #f56c6c;
}

.check-error-messages ul {
  margin: 0;
  padding-left: 20px;
}

.check-error-messages li {
  line-height: 1.8;
  color: #f56c6c;
}


</style>
