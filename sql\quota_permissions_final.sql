-- 配额管理系统权限配置（最终版本）
-- 这个版本可以直接执行，不需要手动替换ID

-- 1. 清理旧的权限配置
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%' OR perms LIKE 'system:quotaGroup:%'
);

DELETE FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%' OR perms LIKE 'system:quotaGroup:%';

-- 2. 插入所有菜单（一次性插入，使用固定的menu_id）
-- 注意：这里使用了较大的menu_id值，避免与现有菜单冲突
-- 如果这些ID已被使用，请调整为未使用的ID

-- 配额管理顶级菜单栏
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
(3000, '配额管理', 0, 5, 'quota', NULL, '', 1, 0, 'M', '0', '0', '', 'chart', 'admin', now(), '配额管理菜单栏');

-- 配额管理子菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
(3001, '配额设置', 3000, 1, 'quotaManagement', 'system/quotaManagement/index', '', 1, 0, 'C', '0', '0', 'system:quotaManagement:list', 'edit', 'admin', now(), '配额设置菜单');

-- 配额管理子菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES 
(3002, '配额查询', 3001, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:query', '#', 'admin', now(), ''),
(3003, '配额新增', 3001, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:add', '#', 'admin', now(), ''),
(3004, '配额修改', 3001, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:edit', '#', 'admin', now(), ''),
(3005, '配额删除', 3001, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:remove', '#', 'admin', now(), ''),
(3006, '配额导出', 3001, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:export', '#', 'admin', now(), ''),
(3007, '批量设置', 3001, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:batch', '#', 'admin', now(), ''),
(3008, '配额重置', 3001, 7, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:reset', '#', 'admin', now(), ''),
(3009, '配额统计', 3001, 8, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:statistics', '#', 'admin', now(), '');

-- 配额组管理子菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
(3010, '配额组管理', 3000, 2, 'quotaGroup', 'system/quotaGroup/index', '', 1, 0, 'C', '0', '0', 'system:quotaGroup:list', 'peoples', 'admin', now(), '配额组管理菜单');

-- 配额组管理子菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES 
(3011, '配额组查询', 3010, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:query', '#', 'admin', now(), ''),
(3012, '配额组新增', 3010, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:add', '#', 'admin', now(), ''),
(3013, '配额组修改', 3010, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:edit', '#', 'admin', now(), ''),
(3014, '配额组删除', 3010, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:remove', '#', 'admin', now(), ''),
(3015, '配额组导出', 3010, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:export', '#', 'admin', now(), '');

-- 3. 为管理员角色分配所有权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(1, 3000), (1, 3001), (1, 3002), (1, 3003), (1, 3004), (1, 3005), (1, 3006), (1, 3007), (1, 3008), (1, 3009),
(1, 3010), (1, 3011), (1, 3012), (1, 3013), (1, 3014), (1, 3015);

-- 4. 为其他角色分配权限（可选）
-- 如果需要为其他角色分配权限，请取消注释并修改role_id

-- 为角色ID为2的角色分配配额管理权限
-- INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- (2, 3001), (2, 3002), (2, 3003), (2, 3004), (2, 3005), (2, 3006), (2, 3007), (2, 3008), (2, 3009);

-- 为角色ID为3的角色分配配额组管理权限
-- INSERT INTO sys_role_menu (role_id, menu_id) VALUES
-- (3, 3010), (3, 3011), (3, 3012), (3, 3013), (3, 3014), (3, 3015);

-- 5. 验证权限配置
SELECT
    m.menu_id,
    m.menu_name,
    m.perms,
    m.menu_type,
    m.parent_id,
    m.visible,
    m.status
FROM sys_menu m
WHERE m.menu_id BETWEEN 3000 AND 3015
ORDER BY m.menu_id;

-- 6. 验证角色权限分配
SELECT
    r.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.menu_id BETWEEN 3000 AND 3015
ORDER BY r.role_id, m.menu_id;

-- 7. 检查是否有菜单ID冲突
SELECT menu_id, menu_name FROM sys_menu WHERE menu_id BETWEEN 3000 AND 3015;

-- 如果上面的查询返回了结果，说明这些ID已被使用
-- 请修改上面的menu_id值为未使用的ID

-- 权限说明：
-- 配额管理权限：
-- system:quotaManagement:list - 配额管理列表查看
-- system:quotaManagement:query - 配额查询
-- system:quotaManagement:add - 新增配额
-- system:quotaManagement:edit - 修改配额
-- system:quotaManagement:remove - 删除配额
-- system:quotaManagement:export - 导出配额数据
-- system:quotaManagement:batch - 批量设置配额
-- system:quotaManagement:reset - 重置配额使用情况
-- system:quotaManagement:statistics - 配额统计

-- 配额组管理权限：
-- system:quotaGroup:list - 配额组管理列表查看
-- system:quotaGroup:query - 配额组查询
-- system:quotaGroup:add - 新增配额组
-- system:quotaGroup:edit - 修改配额组
-- system:quotaGroup:remove - 删除配额组
-- system:quotaGroup:export - 导出配额组数据

-- 菜单ID分配：
-- 3000 - 配额管理顶级菜单栏
-- 3001 - 配额设置菜单
-- 3002-3009 - 配额设置子菜单（按钮权限）
-- 3010 - 配额组管理菜单
-- 3011-3015 - 配额组管理子菜单（按钮权限）
