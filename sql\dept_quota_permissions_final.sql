-- 部门高分配额权限配置SQL（最终版）
-- 基于实际菜单结构：考核评价 > 机构成员评分 (menu_id: 2071)

-- 1. 确认机构成员评分菜单存在
SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    menu_type
FROM sys_menu 
WHERE menu_id = 2071 AND menu_name = '机构成员评分';

-- 2. 添加高分配额相关的功能权限到机构成员评分菜单下
INSERT INTO sys_menu (
    menu_name, 
    parent_id, 
    order_num, 
    path, 
    component, 
    query, 
    is_frame, 
    is_cache, 
    menu_type, 
    visible, 
    status, 
    perms, 
    icon, 
    create_by, 
    create_time, 
    update_by, 
    update_time, 
    remark
) VALUES 
('高分配额查询', 2071, 10, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:query', '#', 'admin', sysdate(), '', null, '查询部门高分配额信息'),
('高分配额检查', 2071, 11, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:check', '#', 'admin', sysdate(), '', null, '检查高分配额是否可用'),
('高分配额管理', 2071, 12, '', '', '', 1, 0, 'F', '0', '0', 'system:deptQuota:manage', '#', 'admin', sysdate(), '', null, '管理高分配额（使用和释放）');

-- 3. 获取刚插入的权限菜单ID
SET @quota_query_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:query' AND parent_id = 2071 ORDER BY menu_id DESC LIMIT 1);
SET @quota_check_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:check' AND parent_id = 2071 ORDER BY menu_id DESC LIMIT 1);
SET @quota_manage_id = (SELECT menu_id FROM sys_menu WHERE perms = 'system:deptQuota:manage' AND parent_id = 2071 ORDER BY menu_id DESC LIMIT 1);

-- 4. 查询当前系统的角色信息
SELECT role_id, role_name, role_key, status FROM sys_role WHERE status = '0' ORDER BY role_sort;

-- 5. 为超级管理员角色分配所有权限（角色ID=1）
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(1, @quota_query_id),
(1, @quota_check_id),
(1, @quota_manage_id);

-- 6. 为机构负责人角色分配权限（角色ID=105）
-- 机构负责人需要管理部门成员的高分配额
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(105, @quota_query_id),
(105, @quota_check_id),
(105, @quota_manage_id);

-- 7. 为管理部门负责人角色分配权限（角色ID=107）
-- 管理部门负责人也可能需要查看配额信息
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(107, @quota_query_id),
(107, @quota_check_id),
(107, @quota_manage_id);

-- 8. 为项目负责人角色分配查询权限（角色ID=104）
-- 项目负责人可能需要查看配额信息，但不需要管理权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES
(104, @quota_query_id),
(104, @quota_check_id);

-- 8. 验证权限配置结果
SELECT 
    CONCAT(pp.menu_name, ' > ', p.menu_name, ' > ', m.menu_name) as menu_path,
    r.role_name,
    m.menu_name as permission_name,
    m.perms,
    m.menu_type
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
LEFT JOIN sys_menu pp ON p.parent_id = pp.menu_id
WHERE m.perms LIKE '%deptQuota%'
ORDER BY r.role_name, m.order_num;

-- 9. 查看机构成员评分菜单下的所有权限
SELECT 
    m.menu_id,
    m.menu_name,
    m.perms,
    m.menu_type,
    m.order_num,
    m.visible,
    m.status
FROM sys_menu m
WHERE m.parent_id = 2071
ORDER BY m.order_num;

-- 10. 检查是否有重复的权限配置
SELECT 
    perms,
    COUNT(*) as count
FROM sys_menu 
WHERE perms LIKE '%deptQuota%'
GROUP BY perms
HAVING COUNT(*) > 1;

-- 注意事项：
-- 1. 机构成员评分菜单ID: 2071
-- 2. 父菜单: 考核评价 (menu_id: 2039)
-- 3. 请根据实际的角色配置调整role_id
-- 4. 执行前请备份数据库
-- 5. 建议先在测试环境验证

-- 常用角色参考（请根据实际项目调整）：
-- 查看所有角色：SELECT role_id, role_name, role_key FROM sys_role WHERE status = '0';

-- 如果需要删除已配置的权限（回滚用）：
/*
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE perms LIKE '%deptQuota%'
);
DELETE FROM sys_menu WHERE perms LIKE '%deptQuota%';
*/
