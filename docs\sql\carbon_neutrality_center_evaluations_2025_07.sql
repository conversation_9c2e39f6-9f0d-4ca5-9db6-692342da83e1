-- ===== 补充碳中和研究中心人员的评分记录 =====
-- 评价月份：2025-07
-- 基于用户提供的精力分配明细和评分状态生成

-- 用户信息：
-- 柴潇 - 用户ID: 176, 碳中和研究中心
-- 敬旭业 - 用户ID: 209, 碳中和研究中心
-- 李超群 - 用户ID: 210, 碳中和研究中心

-- 项目信息：
-- 项目257: 化学链燃烧发电关键技术 - 负责人: 刘海洋(181)
-- 项目270: 绿色氢氨醇技术路线及生物质化学链气化制甲醇关键技术研究 - 负责人: 段琦玮(155)
-- 项目313: 高比例宽负荷生物质掺烧关键技术研究及应用 - 负责人: 袁世通(170)
-- 项目314: 二氧化碳高价值利用应用场景布局研究及实验验证 - 负责人: 袁世通(170)

-- ===== 第一步：添加机构负责人评分记录 =====

-- 柴潇的机构负责人评分（2025-07）
INSERT INTO `project_evaluation` VALUES (6001, 0, 170, 176, 94.00, '2025-07', 'manager', '系统默认评分', NOW(), NOW());

-- 敬旭业的机构负责人评分（2025-07）
INSERT INTO `project_evaluation` VALUES (6002, 0, 170, 209, 95.00, '2025-07', 'manager', '系统默认评分', NOW(), NOW());

-- 李超群的机构负责人评分（2025-07）
INSERT INTO `project_evaluation` VALUES (6003, 0, 170, 210, 96.00, '2025-07', 'manager', '系统默认评分', NOW(), NOW());

-- ===== 第二步：添加柴潇（176）的项目负责人评分记录 =====

-- 1. 绿色氢氨醇技术路线及生物质化学链气化制甲醇关键技术研究（项目270）- 负责人：段琦玮（155）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (6004, 270, 155, 176, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 高比例宽负荷生物质掺烧关键技术研究及应用（项目313）- 负责人：袁世通（170）
-- 精力分配：35.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (6005, 313, 170, 176, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 二氧化碳高价值利用应用场景布局研究及实验验证（项目314）- 负责人：袁世通（170）
-- 精力分配：35.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (6006, 314, 170, 176, 95.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 验证插入的记录 =====
SELECT 
    '新增评分记录验证' as verification_type,
    pe.id,
    CASE 
        WHEN pe.project_id = 0 THEN '机构负责人评分'
        ELSE pi.project_name
    END as project_name,
    su_evaluator.nick_name as evaluator_name,
    su_evaluatee.nick_name as evaluatee_name,
    pe.score,
    pe.evaluation_month,
    pe.evaluation_type,
    pe.comments
FROM project_evaluation pe
LEFT JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE pe.id BETWEEN 6001 AND 6006
ORDER BY pe.id;

-- ===== 统计各人的评分完成情况 =====

-- 柴潇的评分统计
SELECT 
    '柴潇评分统计' as person,
    pe.evaluation_type,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '柴潇'
AND pe.evaluation_month = '2025-07'
GROUP BY pe.evaluation_type;

-- 敬旭业的评分统计
SELECT 
    '敬旭业评分统计' as person,
    pe.evaluation_type,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '敬旭业'
AND pe.evaluation_month = '2025-07'
GROUP BY pe.evaluation_type;

-- 李超群的评分统计
SELECT 
    '李超群评分统计' as person,
    pe.evaluation_type,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '李超群'
AND pe.evaluation_month = '2025-07'
GROUP BY pe.evaluation_type;

-- ===== 柴潇的详细评分明细 =====
SELECT 
    '柴潇详细评分明细' as detail_type,
    CASE 
        WHEN pe.project_id = 0 THEN '机构负责人评分'
        ELSE pi.project_name
    END as project_name,
    su_evaluator.nick_name as evaluator_name,
    pe.score,
    pe.evaluation_type,
    CASE 
        WHEN pi.project_name LIKE '%化学链燃烧发电%' THEN '20.0%'
        WHEN pi.project_name LIKE '%绿色氢氨醇%' THEN '10.0%'
        WHEN pi.project_name LIKE '%高比例宽负荷%' THEN '35.0%'
        WHEN pi.project_name LIKE '%二氧化碳高价值%' THEN '35.0%'
        ELSE '机构评分'
    END as effort_allocation,
    pe.created_at
FROM project_evaluation pe
LEFT JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE su_evaluatee.nick_name = '柴潇'
AND pe.evaluation_month = '2025-07'
ORDER BY pe.evaluation_type, pe.score DESC;

-- ===== 最终评分计算 =====

-- 柴潇最终评分计算
SELECT 
    '柴潇最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score,
    '机构负责人评分 * 0.4 + 项目负责人平均分 * 0.6' as calculation_formula
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '柴潇' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2 
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id 
         WHERE su2.nick_name = '柴潇' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 敬旭业最终评分计算（仅机构负责人评分）
SELECT 
    '敬旭业最终评分计算' as person,
    manager_score,
    NULL as project_leader_avg,
    manager_score as final_score,
    '仅机构负责人评分（无项目参与）' as calculation_formula
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '敬旭业' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score
) as scores;

-- 李超群最终评分计算（仅机构负责人评分）
SELECT 
    '李超群最终评分计算' as person,
    manager_score,
    NULL as project_leader_avg,
    manager_score as final_score,
    '仅机构负责人评分（无项目参与）' as calculation_formula
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '李超群' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score
) as scores;

-- ===== 柴潇按精力分配比例加权的项目负责人评分计算 =====
-- 考虑精力分配比例的加权平均计算（包含已评分的化学链燃烧发电项目）
SELECT 
    '柴潇加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (92 * 0.20 + 92 * 0.10 + 94 * 0.35 + 95 * 0.35) / (0.20 + 0.10 + 0.35 + 0.35), 2
    ) as weighted_project_leader_score,
    '化学链燃烧发电(92*0.2) + 绿色氢氨醇(92*0.1) + 高比例宽负荷(94*0.35) + 二氧化碳高价值(95*0.35)' as calculation_detail
FROM dual;

-- ===== 柴潇加权最终评分计算 =====
-- 使用加权项目负责人评分计算最终得分
SELECT 
    '柴潇加权最终评分' as person,
    94 as manager_score,
    93.7 as weighted_project_leader_avg,
    ROUND(94 * 0.4 + 93.7 * 0.6, 2) as weighted_final_score,
    '使用精力分配比例加权的最终评分' as note
FROM dual;

-- ===== 碳中和研究中心评分汇总 =====
SELECT 
    '碳中和研究中心评分汇总' as summary_type,
    su.nick_name as person_name,
    COUNT(CASE WHEN pe.evaluation_type = 'manager' THEN 1 END) as manager_evaluations,
    COUNT(CASE WHEN pe.evaluation_type = 'project_leader' THEN 1 END) as project_leader_evaluations,
    AVG(CASE WHEN pe.evaluation_type = 'manager' THEN pe.score END) as avg_manager_score,
    AVG(CASE WHEN pe.evaluation_type = 'project_leader' THEN pe.score END) as avg_project_leader_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name IN ('柴潇', '敬旭业', '李超群')
AND pe.evaluation_month = '2025-07'
GROUP BY su.nick_name
ORDER BY su.nick_name;

-- ===== 执行完成确认 =====
SELECT 
    '执行完成' as status,
    NOW() as completion_time,
    '碳中和研究中心3人的评分记录已添加完成' as result,
    '柴潇: 机构+项目评分, 敬旭业/李超群: 仅机构评分' as note;
