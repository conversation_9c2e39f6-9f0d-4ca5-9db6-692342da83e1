package com.kc.system.service.impl;

import com.kc.common.core.domain.entity.SysDept;
import com.kc.system.service.IDeptPermissionService;
import com.kc.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 部门权限服务实现
 * 
 * <AUTHOR>
 */
@Service
public class DeptPermissionServiceImpl implements IDeptPermissionService {
    
    private static final Logger log = LoggerFactory.getLogger(DeptPermissionServiceImpl.class);
    
    @Autowired
    private ISysDeptService deptService;
    
    @Override
    public List<SysDept> getUserManagedDepts(Long userId, String userName) {
        // 获取直接负责的部门
        List<SysDept> directDepts = getUserDirectManagedDepts(userName);
        
        // 获取通过父部门权限管理的子部门
        List<SysDept> inheritedDepts = getUserInheritedManagedDepts(userName);
        
        // 合并去重
        List<SysDept> allDepts = new ArrayList<>(directDepts);
        for (SysDept inheritedDept : inheritedDepts) {
            if (!allDepts.stream().anyMatch(d -> d.getDeptId().equals(inheritedDept.getDeptId()))) {
                allDepts.add(inheritedDept);
            }
        }
        
        return allDepts;
    }
    
    @Override
    public List<SysDept> getUserDirectManagedDepts(String userName) {
        // 获取所有部门
        List<SysDept> allDepts = deptService.selectDeptList(new SysDept());
        
        // 过滤出用户直接负责的部门
        return allDepts.stream()
                .filter(dept -> userName.equals(dept.getLeader()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SysDept> getUserInheritedManagedDepts(String userName) {
        // 获取用户直接负责的部门
        List<SysDept> directDepts = getUserDirectManagedDepts(userName);
        
        // 获取所有部门
        List<SysDept> allDepts = deptService.selectDeptList(new SysDept());
        
        List<SysDept> inheritedDepts = new ArrayList<>();
        
        // 对于每个直接负责的部门，查找其子部门
        for (SysDept directDept : directDepts) {
            List<SysDept> childDepts = findChildDepts(allDepts, directDept.getDeptId());
            inheritedDepts.addAll(childDepts);
        }
        
        return inheritedDepts;
    }
    
    @Override
    public boolean hasPermissionToManageDept(String userName, Long deptId) {
        return isDirectLeaderOfDept(userName, deptId) || isInheritedLeaderOfDept(userName, deptId);
    }
    
    @Override
    public boolean isDirectLeaderOfDept(String userName, Long deptId) {
        SysDept dept = deptService.selectDeptById(deptId);
        return dept != null && userName.equals(dept.getLeader());
    }
    
    @Override
    public boolean isInheritedLeaderOfDept(String userName, Long deptId) {
        SysDept targetDept = deptService.selectDeptById(deptId);
        if (targetDept == null || targetDept.getAncestors() == null) {
            return false;
        }
        
        // 检查ancestors中是否有用户直接负责的部门
        String[] ancestorIds = targetDept.getAncestors().split(",");
        for (String ancestorId : ancestorIds) {
            if (!"0".equals(ancestorId)) {
                try {
                    Long ancestorDeptId = Long.parseLong(ancestorId);
                    if (isDirectLeaderOfDept(userName, ancestorDeptId)) {
                        return true;
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid ancestor ID: {}", ancestorId);
                }
            }
        }
        
        return false;
    }
    
    @Override
    public List<SysDept> getDeptsForEmployeeEvaluation(String userName) {
        // 获取用户直接负责的部门
        List<SysDept> directDepts = getUserDirectManagedDepts(userName);
        
        if (directDepts.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 判断用户的角色类型
        boolean isSubDeptLeader = isUserSubDeptLeader(userName, directDepts);
        
        if (isSubDeptLeader) {
            // 如果是子部门负责人，只返回直接负责的部门
            log.info("用户 {} 是子部门负责人，只能查看直接负责的部门", userName);
            return directDepts;
        } else {
            // 如果是父部门负责人，返回直接负责的部门及其子部门
            log.info("用户 {} 是父部门负责人，可以查看负责的部门及其子部门", userName);
            return getUserManagedDepts(null, userName);
        }
    }
    
    @Override
    public List<SysDept> getDeptsForBonusAllocation(String userName) {
        // 奖金分配的权限规则可能与员工评分不同
        // 这里先使用相同的逻辑，后续可以根据需要调整
        return getDeptsForEmployeeEvaluation(userName);
    }
    
    /**
     * 判断用户是否为子部门负责人
     * 规则：如果用户负责的所有部门都有父部门（不是顶级部门），则认为是子部门负责人
     */
    private boolean isUserSubDeptLeader(String userName, List<SysDept> directDepts) {
        for (SysDept dept : directDepts) {
            // 如果有任何一个部门是顶级部门或父部门ID为0，则不是纯子部门负责人
            if (dept.getParentId() == null || dept.getParentId() == 0) {
                return false;
            }
            
            // 检查父部门是否有其他负责人
            SysDept parentDept = deptService.selectDeptById(dept.getParentId());
            if (parentDept != null && parentDept.getLeader() != null && !userName.equals(parentDept.getLeader())) {
                // 有其他人负责父部门，当前用户是子部门负责人
                continue;
            } else {
                // 父部门也是当前用户负责，不是纯子部门负责人
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 查找指定部门的所有子部门
     */
    private List<SysDept> findChildDepts(List<SysDept> allDepts, Long parentDeptId) {
        return allDepts.stream()
                .filter(dept -> dept.getAncestors() != null && 
                               dept.getAncestors().contains(parentDeptId.toString()))
                .collect(Collectors.toList());
    }
}
