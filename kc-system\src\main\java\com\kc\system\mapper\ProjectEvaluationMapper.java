package com.kc.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.kc.system.domain.ProjectEvaluation;

/**
 * 项目评价Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-01
 */
public interface ProjectEvaluationMapper 
{
    /**
     * 查询项目评价
     * 
     * @param id 项目评价主键
     * @return 项目评价
     */
    public ProjectEvaluation selectProjectEvaluationById(Long id);

    /**
     * 查询项目评价列表
     * 
     * @param projectEvaluation 项目评价
     * @return 项目评价集合
     */
    public List<ProjectEvaluation> selectProjectEvaluationList(ProjectEvaluation projectEvaluation);

    /**
     * 新增项目评价
     * 
     * @param projectEvaluation 项目评价
     * @return 结果
     */
    public int insertProjectEvaluation(ProjectEvaluation projectEvaluation);

    /**
     * 修改项目评价
     * 
     * @param projectEvaluation 项目评价
     * @return 结果
     */
    public int updateProjectEvaluation(ProjectEvaluation projectEvaluation);

    /**
     * 删除项目评价
     * 
     * @param id 项目评价主键
     * @return 结果
     */
    public int deleteProjectEvaluationById(Long id);

    /**
     * 批量删除项目评价
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectEvaluationByIds(Long[] ids);

    /**
     * 获取用户参与的项目评分列表
     * 
     * @param userName 用户名
     * @param evaluationMonth 评价月份 (可选)
     * @return 项目评分列表
     */
    public List<ProjectEvaluation> selectUserProjectEvaluations(@Param("userName") String userName, @Param("evaluationMonth") String evaluationMonth);
    
    /**
     * 通过用户ID获取项目评分列表
     * 
     * @param userId 用户ID
     * @param evaluationMonth 评价月份 (可选)
     * @return 项目评分列表
     */
    public List<ProjectEvaluation> selectUserProjectEvaluationsByUserId(@Param("userId") Long userId, @Param("evaluationMonth") String evaluationMonth);
    
    /**
     * 根据项目ID删除项目评价
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    public int deleteProjectEvaluationByProjectId(Long projectId);
    
    /**
     * 根据项目ID统计项目评价记录数
     *
     * @param projectId 项目ID
     * @return 记录数
     */
    public int countProjectEvaluationByProjectId(Long projectId);

    /**
     * 检查用户是否有项目精力分配
     *
     * @param userId 用户ID
     * @param evaluationMonth 评价月份
     * @return true表示有项目精力分配，false表示没有
     */
    public boolean checkUserHasProjectEffort(@Param("userId") Long userId, @Param("evaluationMonth") String evaluationMonth);

    /**
     * 检查项目负责人是否已对该用户进行评分
     *
     * @param userId 用户ID
     * @param evaluationMonth 评价月份
     * @return true表示项目负责人已评分，false表示未评分
     */
    public boolean checkProjectLeaderEvaluationExists(@Param("userId") Long userId, @Param("evaluationMonth") String evaluationMonth);

    /**
     * 获取用户的项目精力分配详情
     *
     * @param userId 用户ID
     * @param evaluationMonth 评价月份
     * @return 项目精力分配详情列表
     */
    public List<Map<String, Object>> getUserProjectEffortDetails(@Param("userId") Long userId, @Param("evaluationMonth") String evaluationMonth);

    /**
     * 检查特定项目的负责人是否已对该用户进行评分
     *
     * @param userId 用户ID
     * @param projectId 项目ID
     * @param evaluationMonth 评价月份
     * @return true表示已评分，false表示未评分
     */
    public boolean checkProjectLeaderEvaluationForProject(@Param("userId") Long userId, @Param("projectId") Long projectId, @Param("evaluationMonth") String evaluationMonth);

    /**
     * 获取项目负责人信息
     *
     * @param projectId 项目ID
     * @return 项目负责人信息
     */
    public Map<String, Object> getProjectLeaderInfo(@Param("projectId") Long projectId);

    /**
     * 获取用户负责的项目列表
     *
     * @param userId 用户ID
     * @return 用户负责的项目列表
     */
    public List<Map<String, Object>> getUserLeaderProjects(@Param("userId") Long userId);

    /**
     * 根据被评价人、项目、月份和评价类型查询评分记录
     *
     * @param evaluateeId 被评价人ID
     * @param projectId 项目ID
     * @param evaluationMonth 评价月份
     * @param evaluationType 评价类型
     * @return 评分记录
     */
    public ProjectEvaluation selectByEvaluateeProjectAndMonth(@Param("evaluateeId") Long evaluateeId,
                                                              @Param("projectId") Long projectId,
                                                              @Param("evaluationMonth") String evaluationMonth,
                                                              @Param("evaluationType") String evaluationType);
}
