<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.EvaluationConfigMapper">
    
    <resultMap type="EvaluationConfig" id="EvaluationConfigResult">
        <id property="id" column="id" />
        <result property="configType" column="config_type" />
        <result property="enabled" column="enabled" />
        <result property="startDate" column="start_date" />
        <result property="endDate" column="end_date" />
        <result property="month" column="month" />
        <result property="createBy" column="create_by" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateTime" column="update_time" />
        <result property="remark" column="remark" />
    </resultMap>

    <sql id="selectEvaluationConfigVo">
        select id, config_type, enabled, start_date, end_date, month, create_by, create_time, update_by, update_time, remark
        from evaluation_config
    </sql>

    <select id="selectEvaluationConfigList" parameterType="EvaluationConfig" resultMap="EvaluationConfigResult">
        <include refid="selectEvaluationConfigVo"/>
        <where>  
            <if test="configType != null  and configType != ''"> and config_type = #{configType}</if>
            <if test="enabled != null  and enabled != ''"> and enabled = #{enabled}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="month != null  and month != ''"> and month = #{month}</if>
        </where>
    </select>
    
    <select id="selectEvaluationConfigById" parameterType="Long" resultMap="EvaluationConfigResult">
        <include refid="selectEvaluationConfigVo"/>
        where id = #{id}
    </select>
    
    <select id="selectEvaluationConfigByTypeAndMonth" resultMap="EvaluationConfigResult">
        <include refid="selectEvaluationConfigVo"/>
        where config_type = #{configType} and month = #{month}
    </select>
    
    <select id="checkConfigTypeAndMonthUnique" parameterType="EvaluationConfig" resultMap="EvaluationConfigResult">
        <include refid="selectEvaluationConfigVo"/>
        where config_type = #{configType} and month = #{month}
        <if test="id != null">
            and id != #{id}
        </if>
        limit 1
    </select>
        
    <insert id="insertEvaluationConfig" parameterType="EvaluationConfig" useGeneratedKeys="true" keyProperty="id">
        insert into evaluation_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configType != null and configType != ''">config_type,</if>
            <if test="enabled != null and enabled != ''">enabled,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="month != null and month != ''">month,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configType != null and configType != ''">#{configType},</if>
            <if test="enabled != null and enabled != ''">#{enabled},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="month != null and month != ''">#{month},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEvaluationConfig" parameterType="EvaluationConfig">
        update evaluation_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configType != null and configType != ''">config_type = #{configType},</if>
            <if test="enabled != null and enabled != ''">enabled = #{enabled},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="month != null and month != ''">month = #{month},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEvaluationConfigById" parameterType="Long">
        delete from evaluation_config where id = #{id}
    </delete>

    <delete id="deleteEvaluationConfigByIds" parameterType="String">
        delete from evaluation_config where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 