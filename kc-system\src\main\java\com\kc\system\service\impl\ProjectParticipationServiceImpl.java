package com.kc.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kc.system.mapper.ProjectParticipationMapper;
import com.kc.system.domain.ProjectParticipation;
import com.kc.system.service.IProjectParticipationService;

/**
 * 项目参与度分配Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
public class ProjectParticipationServiceImpl implements IProjectParticipationService 
{
    @Autowired
    private ProjectParticipationMapper projectParticipationMapper;

    /**
     * 查询项目参与度分配
     * 
     * @param id 项目参与度分配主键
     * @return 项目参与度分配
     */
    @Override
    public ProjectParticipation selectProjectParticipationById(Long id)
    {
        return projectParticipationMapper.selectProjectParticipationById(id);
    }

    /**
     * 查询项目参与度分配列表
     * 
     * @param projectParticipation 项目参与度分配
     * @return 项目参与度分配
     */
    @Override
    public List<ProjectParticipation> selectProjectParticipationList(ProjectParticipation projectParticipation)
    {
        return projectParticipationMapper.selectProjectParticipationList(projectParticipation);
    }

    /**
     * 新增项目参与度分配
     *
     * @param projectParticipation 项目参与度分配
     * @return 结果
     */
    @Override
    public int insertProjectParticipation(ProjectParticipation projectParticipation)
    {
        // 校验精力分配值：为0或为空时不插入数据
        if (projectParticipation.getParticipationRate() == null ||
            projectParticipation.getParticipationRate().compareTo(BigDecimal.ZERO) <= 0) {

            // 如果精力分配为0或空，检查是否有已存在的记录需要删除
            ProjectParticipation existingRecord = projectParticipationMapper.selectExistingParticipation(
                projectParticipation.getUserName(),
                projectParticipation.getProjectId(),
                projectParticipation.getMonth()
            );

            if (existingRecord != null) {
                // 删除已存在的记录
                projectParticipationMapper.deleteProjectParticipationById(existingRecord.getId());
                return 1; // 返回1表示操作成功（删除了1条记录）
            }

            return 0; // 没有记录需要处理，返回0
        }

        // 检查是否已存在相同的记录（基于唯一约束字段）
        ProjectParticipation existingRecord = projectParticipationMapper.selectExistingParticipation(
            projectParticipation.getUserName(),
            projectParticipation.getProjectId(),
            projectParticipation.getMonth()
        );

        if (existingRecord != null) {
            // 如果记录已存在，更新现有记录
            projectParticipation.setId(existingRecord.getId());
            projectParticipation.setUpdatedAt(new Date());
            return projectParticipationMapper.updateProjectParticipation(projectParticipation);
        } else {
            // 如果记录不存在，插入新记录
            projectParticipation.setCreatedAt(new Date());
            projectParticipation.setUpdatedAt(new Date());
            return projectParticipationMapper.insertProjectParticipation(projectParticipation);
        }
    }

    /**
     * 修改项目参与度分配
     * 
     * @param projectParticipation 项目参与度分配
     * @return 结果
     */
    @Override
    public int updateProjectParticipation(ProjectParticipation projectParticipation)
    {
        return projectParticipationMapper.updateProjectParticipation(projectParticipation);
    }

    /**
     * 批量删除项目参与度分配
     * 
     * @param ids 需要删除的项目参与度分配主键
     * @return 结果
     */
    @Override
    public int deleteProjectParticipationByIds(Long[] ids)
    {
        return projectParticipationMapper.deleteProjectParticipationByIds(ids);
    }

    /**
     * 删除项目参与度分配信息
     *
     * @param id 项目参与度分配主键
     * @return 结果
     */
    @Override
    public int deleteProjectParticipationById(Long id)
    {
        return projectParticipationMapper.deleteProjectParticipationById(id);
    }

    /**
     * 查询项目参与度分配列表（关联用户和部门信息）
     *
     * @param projectParticipation 项目参与度分配
     * @return 项目参与度分配集合（包含用户和部门信息）
     */
    @Override
    public List<Map<String, Object>> selectProjectParticipationWithUserInfo(ProjectParticipation projectParticipation)
    {
        return projectParticipationMapper.selectProjectParticipationWithUserInfo(projectParticipation);
    }
}
