package com.kc.system.service;

import com.kc.system.dto.ScoreCalculationContext;
import com.kc.system.dto.ScoreCalculationResult;
import java.math.BigDecimal;
import java.util.List;

/**
 * 评分计算服务接口
 * 
 * <AUTHOR>
 */
public interface IScoreCalculationService {
    
    /**
     * 计算用户评分
     * 
     * @param userId 用户ID
     * @param evaluationMonth 评价月份
     * @return 评分计算结果
     */
    ScoreCalculationResult calculateUserScore(Long userId, String evaluationMonth);
    
    /**
     * 批量计算用户评分
     * 
     * @param userIds 用户ID列表
     * @param evaluationMonth 评价月份
     * @return 评分计算结果列表
     */
    List<ScoreCalculationResult> batchCalculateUserScores(List<Long> userIds, String evaluationMonth);
    
    /**
     * 根据上下文计算评分
     * 
     * @param context 评分计算上下文
     * @return 评分计算结果
     */
    ScoreCalculationResult calculateScore(ScoreCalculationContext context);
    
    /**
     * 构建评分计算上下文
     * 
     * @param userId 用户ID
     * @param evaluationMonth 评价月份
     * @return 评分计算上下文
     */
    ScoreCalculationContext buildCalculationContext(Long userId, String evaluationMonth);
    
    /**
     * 获取可用的计算策略列表
     * 
     * @return 策略类型列表
     */
    List<String> getAvailableStrategies();
    
    /**
     * 预览评分计算结果（不保存）
     * 
     * @param context 评分计算上下文
     * @return 预览结果
     */
    ScoreCalculationResult previewCalculation(ScoreCalculationContext context);
}
