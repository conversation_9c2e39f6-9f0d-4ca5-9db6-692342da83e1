-- 配额管理表 - 支持手动设置部门配额

-- 1. 创建配额管理表（简化版）
CREATE TABLE `quota_management` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `evaluation_year` varchar(4) NOT NULL COMMENT '评价年度',
  `high_score_quota` int(11) NOT NULL DEFAULT '0' COMMENT '高分配额',
  `quota_type` varchar(20) DEFAULT 'MANUAL' COMMENT '配额类型（MANUAL-手动设置，GROUP-配额组）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dept_year` (`dept_id`, `evaluation_year`),
  KEY `idx_dept_id` (`dept_id`),
  KEY `idx_evaluation_year` (`evaluation_year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额管理表';

-- 2. 创建索引优化查询性能
CREATE INDEX `idx_quota_management_dept_year` ON `quota_management` (`dept_id`, `evaluation_year`);
CREATE INDEX `idx_quota_management_year_type` ON `quota_management` (`evaluation_year`, `quota_type`);

-- 3. 迁移现有配额数据到新表
INSERT INTO `quota_management` (
    `dept_id`,
    `evaluation_year`,
    `high_score_quota`,
    `quota_type`,
    `create_by`
)
SELECT
    dept_id,
    evaluation_year,
    high_score_quota,
    'MANUAL' as quota_type,
    'system' as create_by
FROM dept_high_score_quota;

-- 4. 为配额组部门设置特殊标记
UPDATE quota_management
SET quota_type = 'GROUP'
WHERE dept_id IN (200, 210) AND evaluation_year = '2025';

-- 5. 配额管理相关菜单和权限
-- 配额管理主菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
('配额管理', 2000, 6, 'quotaManagement', 'system/quotaManagement/index', '', 1, 0, 'C', '0', '0', 'system:quotaManagement:list', 'chart', 'admin', sysdate(), '配额管理菜单');

-- 获取配额管理菜单ID（假设为最新插入的ID）
SET @quota_menu_id = LAST_INSERT_ID();

-- 配额管理子菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
('配额查询', @quota_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:query', '#', 'admin', sysdate(), ''),
('配额新增', @quota_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:add', '#', 'admin', sysdate(), ''),
('配额修改', @quota_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:edit', '#', 'admin', sysdate(), ''),
('配额删除', @quota_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:remove', '#', 'admin', sysdate(), ''),
('配额导出', @quota_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:export', '#', 'admin', sysdate(), ''),
('批量设置', @quota_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:batch', '#', 'admin', sysdate(), ''),
('配额统计', @quota_menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaManagement:statistics', '#', 'admin', sysdate(), '');

-- 配额组管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
('配额组管理', 2000, 7, 'quotaGroup', 'system/quotaGroup/index', '', 1, 0, 'C', '0', '0', 'system:quotaGroup:list', 'peoples', 'admin', sysdate(), '配额组管理菜单');

-- 获取配额组管理菜单ID
SET @quota_group_menu_id = LAST_INSERT_ID();

-- 配额组管理子菜单权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark)
VALUES
('配额组查询', @quota_group_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:query', '#', 'admin', sysdate(), ''),
('配额组新增', @quota_group_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:add', '#', 'admin', sysdate(), ''),
('配额组修改', @quota_group_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:edit', '#', 'admin', sysdate(), ''),
('配额组删除', @quota_group_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:remove', '#', 'admin', sysdate(), ''),
('配额组导出', @quota_group_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:quotaGroup:export', '#', 'admin', sysdate(), '');

-- 6. 为管理员角色分配权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'system:quotaManagement:%' OR perms LIKE 'system:quotaGroup:%';

-- 7. 验证查询
-- 查看配额管理数据
SELECT
    qm.id,
    qm.dept_id,
    sd.dept_name,
    qm.evaluation_year,
    qm.high_score_quota,
    qm.quota_type
FROM quota_management qm
LEFT JOIN sys_dept sd ON qm.dept_id = sd.dept_id
ORDER BY qm.evaluation_year DESC, qm.dept_id;

-- 查看各种配额类型的统计
SELECT
    quota_type,
    COUNT(*) as dept_count,
    SUM(high_score_quota) as total_quota
FROM quota_management
WHERE evaluation_year = '2025'
GROUP BY quota_type;

-- 注意事项：
-- 1. quota_type字段用于区分配额类型：
--    - MANUAL: 手动设置的配额
--    - GROUP: 配额组共享配额
-- 2. 薪酬管理员只需要为每个部门设置一个配额数字
-- 3. 配额使用情况基于实时计算，不需要维护复杂状态
-- 4. 配额组部门标记为GROUP类型，使用共享配额逻辑
 