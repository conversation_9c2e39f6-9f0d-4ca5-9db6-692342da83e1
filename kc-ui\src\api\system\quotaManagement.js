import request from '@/utils/request'

// 查询配额管理列表
export function listQuotaManagement(query) {
  return request({
    url: '/system/quotaManagement/list',
    method: 'get',
    params: query
  })
}

// 查询配额管理详细
export function getQuotaManagement(id) {
  return request({
    url: '/system/quotaManagement/' + id,
    method: 'get'
  })
}

// 新增配额管理
export function addQuotaManagement(data) {
  return request({
    url: '/system/quotaManagement',
    method: 'post',
    data: data
  })
}

// 修改配额管理
export function updateQuotaManagement(data) {
  return request({
    url: '/system/quotaManagement',
    method: 'put',
    data: data
  })
}

// 删除配额管理
export function delQuotaManagement(id) {
  return request({
    url: '/system/quotaManagement/' + id,
    method: 'delete'
  })
}

// 根据部门ID和年度查询配额
export function getQuotaByDept(deptId, year) {
  return request({
    url: `/system/quotaManagement/dept/${deptId}/${year}`,
    method: 'get'
  })
}

// 检查配额是否可用
export function checkQuotaAvailable(deptId, year, count) {
  return request({
    url: `/system/quotaManagement/check/${deptId}/${year}/${count}`,
    method: 'get'
  })
}

// 批量设置配额
export function batchSetQuota(data) {
  return request({
    url: '/system/quotaManagement/batchSet',
    method: 'post',
    data: data
  })
}

// 重置年度配额使用情况
export function resetYearQuota(year) {
  return request({
    url: `/system/quotaManagement/reset/${year}`,
    method: 'post'
  })
}

// 查询配额统计
export function getQuotaStatistics(year) {
  return request({
    url: `/system/quotaManagement/statistics/${year}`,
    method: 'get'
  })
}

// 初始化年度配额
export function initYearQuota(year) {
  return request({
    url: `/system/quotaManagement/init/${year}`,
    method: 'post'
  })
}

// 更新部门人数
export function updateEmployeeCount(deptId, year) {
  return request({
    url: `/system/quotaManagement/updateEmployeeCount/${deptId}/${year}`,
    method: 'post'
  })
}

// 批量更新部门人数
export function batchUpdateEmployeeCount(year) {
  return request({
    url: `/system/quotaManagement/batchUpdateEmployeeCount/${year}`,
    method: 'post'
  })
}

// 获取配额详情
export function getQuotaDetail(deptId, year) {
  return request({
    url: `/system/quotaManagement/detail/${deptId}/${year}`,
    method: 'get'
  })
}

// 检查部门是否属于配额组
export function checkDeptInQuotaGroup(deptId) {
  return request({
    url: `/system/quotaManagement/checkQuotaGroup/${deptId}`,
    method: 'get'
  })
}
