-- ===== 补充王翠、王万堃的项目负责人评分记录 =====
-- 评价月份：2025-07
-- 基于用户提供的精力分配明细和评分状态生成

-- 用户信息：
-- 王翠 - 用户ID: 177, 所属部门: 风能利用先进技术研究所, 机构负责人评分: 95分
-- 王万堃 - 用户ID: 180, 所属部门: 风能利用先进技术研究所, 机构负责人评分: 92分

-- 项目信息：
-- 项目259: 海洋能源多能高效耦合及控制策略研究 - 负责人: 刘燕(168)
-- 项目260: 漂浮式海上风电多体耦合分析及数字孪生系统建模技术研究 - 负责人: 刘燕(168)
-- 项目315: "海洋能+"多能耦合能源系统实验装置建设及关键技术研究 - 负责人: 刘燕(168)

-- ===== 王翠（177）的未评分项目 =====

-- 1. 海洋能源多能高效耦合及控制策略研究（项目259）- 负责人：刘燕（168）
-- 精力分配：50.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (4001, 259, 168, 177, 95.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 漂浮式海上风电多体耦合分析及数字孪生系统建模技术研究（项目260）- 负责人：刘燕（168）
-- 精力分配：30.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (4002, 260, 168, 177, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. "海洋能+"多能耦合能源系统实验装置建设及关键技术研究（项目315）- 负责人：刘燕（168）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (4003, 315, 168, 177, 93.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 王万堃（180）的未评分项目 =====

-- 1. 海洋能源多能高效耦合及控制策略研究（项目259）- 负责人：刘燕（168）
-- 精力分配：5.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (4004, 259, 168, 180, 90.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 漂浮式海上风电多体耦合分析及数字孪生系统建模技术研究（项目260）- 负责人：刘燕（168）
-- 精力分配：30.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (4005, 260, 168, 180, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. "海洋能+"多能耦合能源系统实验装置建设及关键技术研究（项目315）- 负责人：刘燕（168）
-- 精力分配：65.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (4006, 315, 168, 180, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 验证插入的记录 =====
SELECT 
    '新增评分记录验证' as verification_type,
    pe.id,
    pi.project_name,
    su_evaluator.nick_name as evaluator_name,
    su_evaluatee.nick_name as evaluatee_name,
    pe.score,
    pe.evaluation_month,
    pe.evaluation_type,
    pe.comments
FROM project_evaluation pe
INNER JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE pe.id BETWEEN 4001 AND 4006
ORDER BY pe.id;

-- ===== 统计各人的项目负责人评分完成情况 =====

-- 王翠的项目负责人评分统计
SELECT 
    '王翠项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '王翠'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 王万堃的项目负责人评分统计
SELECT 
    '王万堃项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '王万堃'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- ===== 详细评分明细 =====

-- 王翠的详细评分明细
SELECT 
    '王翠详细评分明细' as detail_type,
    pi.project_name,
    su_evaluator.nick_name as evaluator_name,
    pe.score,
    CASE 
        WHEN pi.project_name LIKE '%海洋能源多能高效耦合%' THEN '50.0%'
        WHEN pi.project_name LIKE '%漂浮式海上风电%' THEN '30.0%'
        WHEN pi.project_name LIKE '%海洋能+%' THEN '20.0%'
        ELSE '未知'
    END as effort_allocation,
    pe.created_at
FROM project_evaluation pe
INNER JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE su_evaluatee.nick_name = '王翠'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07'
ORDER BY pe.score DESC;

-- 王万堃的详细评分明细
SELECT 
    '王万堃详细评分明细' as detail_type,
    pi.project_name,
    su_evaluator.nick_name as evaluator_name,
    pe.score,
    CASE 
        WHEN pi.project_name LIKE '%海洋能源多能高效耦合%' THEN '5.0%'
        WHEN pi.project_name LIKE '%漂浮式海上风电%' THEN '30.0%'
        WHEN pi.project_name LIKE '%海洋能+%' THEN '65.0%'
        ELSE '未知'
    END as effort_allocation,
    pe.created_at
FROM project_evaluation pe
INNER JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE su_evaluatee.nick_name = '王万堃'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07'
ORDER BY pe.score DESC;

-- ===== 最终评分计算 =====

-- 王翠最终评分计算
SELECT 
    '王翠最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score,
    '机构负责人评分 * 0.4 + 项目负责人平均分 * 0.6' as calculation_formula
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '王翠' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2 
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id 
         WHERE su2.nick_name = '王翠' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 王万堃最终评分计算
SELECT 
    '王万堃最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score,
    '机构负责人评分 * 0.4 + 项目负责人平均分 * 0.6' as calculation_formula
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '王万堃' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2 
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id 
         WHERE su2.nick_name = '王万堃' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- ===== 按精力分配比例加权的项目负责人评分计算 =====

-- 王翠加权项目负责人评分
SELECT 
    '王翠加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (95 * 0.50 + 94 * 0.30 + 93 * 0.20) / (0.50 + 0.30 + 0.20), 2
    ) as weighted_project_leader_score,
    '海洋能源多能高效耦合(95*0.5) + 漂浮式海上风电(94*0.3) + 海洋能+多能耦合(93*0.2)' as calculation_detail
FROM dual;

-- 王万堃加权项目负责人评分
SELECT 
    '王万堃加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (90 * 0.05 + 92 * 0.30 + 94 * 0.65) / (0.05 + 0.30 + 0.65), 2
    ) as weighted_project_leader_score,
    '海洋能源多能高效耦合(90*0.05) + 漂浮式海上风电(92*0.3) + 海洋能+多能耦合(94*0.65)' as calculation_detail
FROM dual;

-- ===== 加权最终评分计算 =====

-- 王翠加权最终评分
SELECT 
    '王翠加权最终评分' as person,
    95 as manager_score,
    94.4 as weighted_project_leader_avg,
    ROUND(95 * 0.4 + 94.4 * 0.6, 2) as weighted_final_score,
    '使用精力分配比例加权的最终评分' as note
FROM dual;

-- 王万堃加权最终评分
SELECT 
    '王万堃加权最终评分' as person,
    92 as manager_score,
    93.4 as weighted_project_leader_avg,
    ROUND(92 * 0.4 + 93.4 * 0.6, 2) as weighted_final_score,
    '使用精力分配比例加权的最终评分' as note
FROM dual;

-- ===== 对比分析 =====

-- 王翠评分对比分析
SELECT 
    '王翠评分对比分析' as analysis_type,
    '简单平均' as method1,
    94.0 as simple_avg_project_score,
    ROUND(95 * 0.4 + 94.0 * 0.6, 2) as simple_final_score,
    '加权平均' as method2,
    94.4 as weighted_avg_project_score,
    ROUND(95 * 0.4 + 94.4 * 0.6, 2) as weighted_final_score
FROM dual;

-- 王万堃评分对比分析
SELECT 
    '王万堃评分对比分析' as analysis_type,
    '简单平均' as method1,
    92.0 as simple_avg_project_score,
    ROUND(92 * 0.4 + 92.0 * 0.6, 2) as simple_final_score,
    '加权平均' as method2,
    93.4 as weighted_avg_project_score,
    ROUND(92 * 0.4 + 93.4 * 0.6, 2) as weighted_final_score
FROM dual;

-- ===== 执行完成确认 =====
SELECT 
    '执行完成' as status,
    NOW() as completion_time,
    '王翠和王万堃的项目负责人评分记录已添加完成' as result,
    '王翠预期最终得分: 94.4分, 王万堃预期最终得分: 92.04分' as expected_scores;
