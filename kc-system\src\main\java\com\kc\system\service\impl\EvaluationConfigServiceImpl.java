package com.kc.system.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Calendar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.kc.system.mapper.EvaluationConfigMapper;
import com.kc.system.domain.EvaluationConfig;
import com.kc.system.service.IEvaluationConfigService;
import com.kc.common.utils.DateUtils;
import com.kc.common.utils.StringUtils;
import com.kc.common.constant.UserConstants;

/**
 * 评价系统配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-01
 */
@Service
public class EvaluationConfigServiceImpl implements IEvaluationConfigService 
{
    @Autowired
    private EvaluationConfigMapper evaluationConfigMapper;

    /**
     * 查询评价系统配置
     * 
     * @param id 评价系统配置主键
     * @return 评价系统配置
     */
    @Override
    public EvaluationConfig selectEvaluationConfigById(Long id)
    {
        return evaluationConfigMapper.selectEvaluationConfigById(id);
    }

    /**
     * 查询评价系统配置列表
     * 
     * @param evaluationConfig 评价系统配置
     * @return 评价系统配置
     */
    @Override
    public List<EvaluationConfig> selectEvaluationConfigList(EvaluationConfig evaluationConfig)
    {
        return evaluationConfigMapper.selectEvaluationConfigList(evaluationConfig);
    }

    /**
     * 根据配置类型和月份查询配置
     * 
     * @param configType 配置类型
     * @param month 月份
     * @return 评价系统配置
     */
    @Override
    public EvaluationConfig selectEvaluationConfigByTypeAndMonth(String configType, String month)
    {
        return evaluationConfigMapper.selectEvaluationConfigByTypeAndMonth(configType, month);
    }

    /**
     * 检查当前时间是否允许执行特定配置类型的操作
     *
     * @param configType 配置类型
     * @param month 月份
     * @return true=允许操作，false=不允许操作
     */
    @Override
    public boolean checkOperationAllowed(String configType, String month)
    {
        // 获取对应配置
        EvaluationConfig config = selectEvaluationConfigByTypeAndMonth(configType, month);

        if (config == null)
        {
            // 如果没有配置，默认允许操作（这样即使禁用了填报周期也能正常操作）
            return true;
        }

        // 检查是否启用
        if (!"Y".equals(config.getEnabled()))
        {
            // 如果配置被禁用，则允许任何时候填报
            return true;
        }
        
        // 获取当前时间
        Date now = DateUtils.getNowDate();
        
        // 创建日历实例用于日期操作
        Calendar calNow = Calendar.getInstance();
        calNow.setTime(now);
        
        // 获取当前日期的日
        int currentDay = calNow.get(Calendar.DAY_OF_MONTH);
        
        // 获取配置的开始日期和结束日期
        Calendar calStart = Calendar.getInstance();
        Calendar calEnd = Calendar.getInstance();
        
        if (config.getStartDate() != null) {
            calStart.setTime(config.getStartDate());
        }
        
        if (config.getEndDate() != null) {
            calEnd.setTime(config.getEndDate());
        }
        
        // 获取开始日期和结束日期的日
        int startDay = config.getStartDate() != null ? calStart.get(Calendar.DAY_OF_MONTH) : 1;
        int endDay = config.getEndDate() != null ? calEnd.get(Calendar.DAY_OF_MONTH) : 31;
        
        // 检查当前日是否在配置的开始日和结束日之间
        if (startDay <= endDay) {
            // 正常情况: 如 1-15
            return currentDay >= startDay && currentDay <= endDay;
        } else {
            // 跨月情况: 如 28-5 (从28号到下个月5号)
            return currentDay >= startDay || currentDay <= endDay;
        }
    }

    /**
     * 新增评价系统配置
     * 
     * @param evaluationConfig 评价系统配置
     * @return 结果
     */
    @Override
    public int insertEvaluationConfig(EvaluationConfig evaluationConfig)
    {
        evaluationConfig.setCreateTime(DateUtils.getNowDate());
        return evaluationConfigMapper.insertEvaluationConfig(evaluationConfig);
    }

    /**
     * 修改评价系统配置
     * 
     * @param evaluationConfig 评价系统配置
     * @return 结果
     */
    @Override
    public int updateEvaluationConfig(EvaluationConfig evaluationConfig)
    {
        evaluationConfig.setUpdateTime(DateUtils.getNowDate());
        return evaluationConfigMapper.updateEvaluationConfig(evaluationConfig);
    }

    /**
     * 批量删除评价系统配置
     * 
     * @param ids 需要删除的评价系统配置主键
     * @return 结果
     */
    @Override
    public int deleteEvaluationConfigByIds(Long[] ids)
    {
        return evaluationConfigMapper.deleteEvaluationConfigByIds(ids);
    }

    /**
     * 删除评价系统配置信息
     * 
     * @param id 评价系统配置主键
     * @return 结果
     */
    @Override
    public int deleteEvaluationConfigById(Long id)
    {
        return evaluationConfigMapper.deleteEvaluationConfigById(id);
    }
    
    /**
     * 校验配置类型和月份组合是否唯一
     * 
     * @param evaluationConfig 评价系统配置
     * @return 结果
     */
    @Override
    public boolean checkConfigTypeAndMonthUnique(EvaluationConfig evaluationConfig)
    {
        Long id = StringUtils.isNull(evaluationConfig.getId()) ? -1L : evaluationConfig.getId();
        EvaluationConfig info = evaluationConfigMapper.checkConfigTypeAndMonthUnique(evaluationConfig);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }
} 