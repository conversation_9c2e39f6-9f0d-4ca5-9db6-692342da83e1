package com.kc.framework.aspectj;

import java.util.ArrayList;
import java.util.List;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import com.kc.common.annotation.DataScope;
import com.kc.common.constant.UserConstants;
import com.kc.common.core.domain.BaseEntity;
import com.kc.common.core.domain.entity.SysRole;
import com.kc.common.core.domain.entity.SysUser;
import com.kc.common.core.domain.model.LoginUser;
import com.kc.common.core.text.Convert;
import com.kc.common.utils.SecurityUtils;
import com.kc.common.utils.StringUtils;
import com.kc.framework.security.context.PermissionContextHolder;
import org.springframework.beans.BeanUtils;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataScopeAspect
{
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable
    {
        clearDataScope(point);
        handleDataScope(point, controllerDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope)
    {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser))
        {
            SysUser currentUser = loginUser.getUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin())
            {
                String permission = StringUtils.defaultIfEmpty(controllerDataScope.permission(), PermissionContextHolder.getContext());
                dataScopeFilter(joinPoint, currentUser, controllerDataScope.deptAlias(), controllerDataScope.userAlias(), permission);
            }
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint 切点
     * @param user 用户
     * @param deptAlias 部门别名
     * @param userAlias 用户别名
     * @param permission 权限字符
     */
    public static void dataScopeFilter(JoinPoint joinPoint, SysUser user, String deptAlias, String userAlias, String permission)
    {
        // 如果是超级管理员，则不进行数据过滤
        if (SecurityUtils.isAdmin(user.getUserId())) {
            Object params = joinPoint.getArgs()[0];
            if (params instanceof BaseEntity) {
                BaseEntity baseEntity = (BaseEntity) params;
                baseEntity.getParams().put(DATA_SCOPE, "");
            }
            return;
        }

        StringBuilder sqlString = new StringBuilder();
        List<String> conditions = new ArrayList<>();

        // 获取用户的角色列表
        List<SysRole> roles = user.getRoles();
        if (roles != null) {
            for (SysRole role : roles) {
                String dataScope = role.getDataScope();
                if (DATA_SCOPE_ALL.equals(dataScope)) {
                    // 如果有全部数据权限，直接返回不添加过滤条件
                    Object params = joinPoint.getArgs()[0];
                    if (params instanceof BaseEntity) {
                        BaseEntity baseEntity = (BaseEntity) params;
                        baseEntity.getParams().put(DATA_SCOPE, "");
                    }
                    return;
                }
                if (DATA_SCOPE_CUSTOM.equals(dataScope)) {
                    conditions.add(StringUtils.format(
                        "{}.dept_id IN (SELECT dept_id FROM sys_role_dept WHERE role_id = {})",
                        deptAlias, role.getRoleId()
                    ));
                } else if (DATA_SCOPE_DEPT.equals(dataScope)) {
                    conditions.add(StringUtils.format(
                        "{}.dept_id = {}",
                        deptAlias, user.getDeptId()
                    ));
                } else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope)) {
                    conditions.add(StringUtils.format(
                        "{}.dept_id IN (SELECT dept_id FROM sys_dept WHERE dept_id = {} OR find_in_set({}, ancestors))",
                        deptAlias, user.getDeptId(), user.getDeptId()
                    ));
                } else if (DATA_SCOPE_SELF.equals(dataScope)) {
                    if (StringUtils.isNotBlank(userAlias)) {
                        conditions.add(StringUtils.format(
                            "{}.user_id = {}",
                            userAlias, user.getUserId()
                        ));
                    }
                }
            }
        }

        // 如果没有任何角色数据权限，则只能查看自己的数据
        if (conditions.isEmpty()) {
            conditions.add(StringUtils.format(
                "{}.user_id = {}",
                userAlias, user.getUserId()
            ));
        }

        // 组装最终的SQL条件
        sqlString.append(" AND (").append(String.join(" OR ", conditions)).append(")");

        Object params = joinPoint.getArgs()[0];
        if (params instanceof BaseEntity) {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, sqlString.toString());
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint)
    {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params) && params instanceof BaseEntity)
        {
            BaseEntity baseEntity = (BaseEntity) params;
            baseEntity.getParams().put(DATA_SCOPE, "");
        }
    }
}

