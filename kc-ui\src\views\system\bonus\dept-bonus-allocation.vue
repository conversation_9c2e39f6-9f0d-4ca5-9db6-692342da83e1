<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分配月份" prop="allocationMonth">
        <el-date-picker
          v-model="queryParams.allocationMonth"
          type="month"
          value-format="yyyy-MM"
          placeholder="选择月份">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分配状态" prop="allocationStatus">
        <el-select v-model="queryParams.allocationStatus" placeholder="请选择分配状态" clearable>
          <el-option label="未分配" value="0" />
          <el-option label="部分分配" value="1" />
          <el-option label="已完成" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:deptBonus:add']"
        >批量分配</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit-outline"
          size="mini"
          @click="handleSupplementAdd"
          v-hasPermi="['system:deptBonus:add']"
        >补填部门</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:deptBonus:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:deptBonus:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deptBonusList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="部门名称" align="center" prop="deptName" />
      <el-table-column label="分配月份" align="center" prop="allocationMonth" width="100" />
      <el-table-column label="绩效排名" align="center" prop="performanceRank" width="100">
        <template slot-scope="scope">
          <el-tag :type="getRankTagType(scope.row.performanceRank)">
            第{{ scope.row.performanceRank }}名
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="部门总奖金" align="center" prop="totalBonus" width="120">
        <template slot-scope="scope">
          <span :class="scope.row.totalBonus >= 0 ? 'text-success' : 'text-danger'">
            {{ formatMoney(scope.row.totalBonus) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="已分配奖金" align="center" prop="allocatedBonus" width="120">
        <template slot-scope="scope">
          {{ formatMoney(scope.row.allocatedBonus) }}
        </template>
      </el-table-column>
      <el-table-column label="剩余奖金" align="center" prop="remainingBonus" width="120">
        <template slot-scope="scope">
          <span :class="scope.row.remainingBonus >= 0 ? 'text-success' : 'text-danger'">
            {{ formatMoney(scope.row.remainingBonus) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="分配状态" align="center" prop="allocationStatus" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.allocation_status" :value="scope.row.allocationStatus"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="分配人" align="center" prop="allocatorName" width="100" /> -->
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:deptBonus:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:deptBonus:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改部门奖金分配对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分配月份" prop="allocationMonth">
              <el-date-picker
                v-model="form.allocationMonth"
                type="month"
                value-format="yyyy-MM"
                placeholder="选择月份"
                :disabled="form.id != null">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 单个记录修改模式 -->
        <div v-if="form.id != null">
          <el-row>
            <el-col :span="24">
              <el-form-item label="部门名称">
                <el-input v-model="form.deptName" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="绩效排名" prop="performanceRank">
                <el-input-number
                  v-model="form.performanceRank"
                  :min="1"
                  :max="100"
                  controls-position="right"
                  placeholder="请输入绩效排名"
                  style="width: 100%;">
                </el-input-number>
                <!-- <div style="margin-top: 5px; color: #606266; font-size: 12px;">数字越小排名越好</div> -->
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门总奖金(元)" prop="totalBonus">
                <el-input-number
                  v-model="form.totalBonus"
                  :precision="2"
                  :step="1000"
                  controls-position="right"
                  placeholder="请输入奖金金额"
                  style="width: 100%;">
                </el-input-number>
                <!-- <div style="margin-top: 5px; color: #606266; font-size: 12px;">可以为负数（扣款）</div> -->
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 批量分配模式 -->
        <div v-else>
          <el-divider content-position="left">部门奖金分配</el-divider>

          <!-- <el-alert
            title="填写说明：绩效排名数字越小表示排名越好，奖金金额可以为负数（表示扣款）"
            type="info"
            :closable="false"
            show-icon
            style="margin-bottom: 15px;">
          </el-alert> -->

          <!-- 补填模式下显示已分配排名提示 -->
          <el-alert
            v-if="title.includes('补填') && allocatedRanks && allocatedRanks.length > 0"
            :title="`已分配排名：${sortedAllocatedRanks} (请避免使用这些排名)`"
            type="warning"
            :closable="false"
            show-icon
            style="margin-bottom: 15px;">
          </el-alert>

          <!-- 快速操作 -->
          <!-- <div style="margin-bottom: 15px; padding: 10px; border: 1px dashed #dcdfe6; border-radius: 4px;">
            <div style="margin-bottom: 10px; font-weight: bold; color: #606266;">快速操作：</div>
            <el-row :gutter="10">
              <el-col :span="8">
                <el-button size="mini" @click="autoSetRanks">自动设置排名</el-button>
              </el-col>
              <el-col :span="8">
                <el-button size="mini" @click="clearAllData">清空所有数据</el-button>
              </el-col>
              <el-col :span="8">
                <el-button size="mini" @click="setUniformBonus">统一设置奖金</el-button>
              </el-col>
            </el-row>
          </div> -->

        <el-table :data="form.deptBonusList" style="width: 100%">
          <el-table-column label="部门名称" prop="deptName" width="150" />
          <el-table-column label="绩效排名" width="120">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.performanceRank"
                :min="1"
                :max="100"
                size="mini"
                placeholder="排名"
                @change="validateRankUniqueness(scope.row, scope.$index)"
                :class="{ 'rank-error': scope.row.rankError }"
                style="width: 100px">
              </el-input-number>
              <div v-if="scope.row.rankError" class="rank-error-text">
                {{ scope.row.rankError }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="部门总奖金(元)" width="160">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.totalBonus"
                :precision="2"
                :step="1000"
                placeholder="奖金金额"
                size="mini"
                style="width: 140px">
              </el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="备注">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.remark"
                placeholder="请输入备注（可选）"
                size="mini">
              </el-input>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80">
            <template slot-scope="scope">
              <el-tag
                v-if="scope.row.totalBonus !== 0 && scope.row.performanceRank > 0"
                type="success"
                size="mini">已填写</el-tag>
              <el-tag
                v-else
                type="info"
                size="mini">未填写</el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 统计信息 -->
        <div style="margin-top: 15px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
          <el-row :gutter="20">
            <el-col :span="8">
              <div style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #409EFF;">{{ validDeptCount }}</div>
                <div style="color: #606266; font-size: 12px;">已填写部门数</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold;" :class="totalBonusAmount >= 0 ? 'text-success' : 'text-danger'">
                  {{ formatMoney(totalBonusAmount) }}
                </div>
                <div style="color: #606266; font-size: 12px;">总奖金金额</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div style="text-align: center;">
                <div style="font-size: 24px; font-weight: bold; color: #E6A23C;">{{ form.deptBonusList ? form.deptBonusList.length : 0 }}</div>
                <div style="color: #606266; font-size: 12px;">总部门数</div>
              </div>
            </el-col>
          </el-row>
          <div style="margin-top: 10px; color: #606266; font-size: 12px; text-align: center;">
            <i class="el-icon-info"></i>
            提示：只有填写了绩效排名和奖金金额的部门才会被提交
          </div>
          <!-- 排名错误提示 -->
          <div v-if="hasRankErrors()" style="margin-top: 10px; padding: 8px; background-color: #fef0f0; border: 1px solid #fbc4c4; border-radius: 4px; text-align: center;">
            <i class="el-icon-warning" style="color: #F56C6C;"></i>
            <span style="color: #F56C6C; font-size: 12px; margin-left: 5px;">
              存在重复排名，请修正后再提交
            </span>
          </div>
        </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDeptBonus, getDeptBonus, delDeptBonus, addDeptBonus, updateDeptBonus, batchAllocateDeptBonus, checkMonth, listDeptBonusByMonth } from "@/api/system/deptBonus";
import { listDept } from "@/api/system/dept";

export default {
  name: "DeptBonusAllocation",
  dicts: ['allocation_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 部门奖金分配表格数据
      deptBonusList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        allocationMonth: null,
        deptName: null,
        allocationStatus: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        allocationMonth: [
          { required: true, message: "分配月份不能为空", trigger: "change" }
        ],
        performanceRank: [
          { required: true, message: "绩效排名不能为空", trigger: "blur" },
          { type: 'number', min: 1, max: 100, message: "绩效排名必须在1-100之间", trigger: "blur" }
        ],
        totalBonus: [
          { required: true, message: "部门总奖金不能为空", trigger: "blur" }
        ]
      },
      // 部门列表
      deptList: [],
      // 已分配的排名（补填时使用）
      allocatedRanks: []
    };
  },
  computed: {
    // 已填写的部门数量
    validDeptCount() {
      if (!this.form.deptBonusList) return 0;
      return this.form.deptBonusList.filter(item =>
        item.totalBonus !== 0 && item.performanceRank > 0
      ).length;
    },
    // 总奖金金额
    totalBonusAmount() {
      if (!this.form.deptBonusList) return 0;
      return this.form.deptBonusList.reduce((total, item) => {
        return total + (item.totalBonus || 0);
      }, 0);
    },
    // 已分配排名的排序显示
    sortedAllocatedRanks() {
      if (!this.allocatedRanks || this.allocatedRanks.length === 0) return '';
      // 创建副本进行排序，避免修改原数组
      return [...this.allocatedRanks].sort((a, b) => a - b).join('、');
    }
  },
  created() {
    this.getList();
    this.getDeptList();
  },
  methods: {
    /** 查询部门奖金分配列表 */
    getList() {
      this.loading = true;
      listDeptBonus(this.queryParams).then(response => {
        this.deptBonusList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取部门列表 */
    getDeptList() {
      listDept().then(response => {
        this.deptList = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        allocationMonth: null,
        deptBonusList: []
      };
      // 清空已分配排名
      this.allocatedRanks = [];
      // 安全地重置表单
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate();
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.title = "批量分配部门奖金";
      this.form.deptBonusList = this.deptList.map(dept => ({
        deptId: dept.deptId,
        deptName: dept.deptName,
        performanceRank: 1,
        totalBonus: 0,
        remark: ''
      }));
      this.open = true;
    },
    /** 补填部门按钮操作 */
    handleSupplementAdd() {
      // 检查是否选择了月份
      if (!this.queryParams.allocationMonth) {
        this.$modal.msgWarning("请先选择要补填的月份");
        return;
      }

      // 获取该月份已分配的部门
      checkMonth(this.queryParams.allocationMonth).then(response => {
        if (!response.data) {
          this.$modal.msgWarning("该月份尚未进行任何部门奖金分配，请使用批量分配功能");
          return;
        }

        // 获取该月份已分配的部门列表
        listDeptBonusByMonth(this.queryParams.allocationMonth).then(response => {
          // 检查响应数据结构
          if (!response || !response.data) {
            this.$modal.msgError("获取已分配部门列表失败：响应数据为空");
            return;
          }

          const allocatedData = Array.isArray(response.data) ? response.data : [];
          const allocatedDeptIds = allocatedData.map(item => item.deptId);
          const allocatedRanks = allocatedData.map(item => item.performanceRank);

          // 过滤出未分配的部门
          const unallocatedDepts = this.deptList.filter(dept =>
            !allocatedDeptIds.includes(dept.deptId)
          );

          if (unallocatedDepts.length === 0) {
            this.$message.info(`该月份所有部门都已分配奖金。\n\n已分配部门：${allocatedData.length}个\n总部门数：${this.deptList.length}个`);
            return;
          }

          this.reset();
          this.title = `补填部门奖金 (${this.queryParams.allocationMonth})`;
          this.form.allocationMonth = this.queryParams.allocationMonth;

          // 存储已分配的排名，用于校验
          this.allocatedRanks = allocatedRanks;

          // 找到可用的排名起始值
          let startRank = 1;
          while (allocatedRanks.includes(startRank)) {
            startRank++;
          }

          this.form.deptBonusList = unallocatedDepts.map((dept, index) => {
            // 找到下一个可用的排名
            let rank = startRank + index;
            while (allocatedRanks.includes(rank)) {
              rank++;
            }

            return {
              deptId: dept.deptId,
              deptName: dept.deptName,
              performanceRank: rank,
              totalBonus: 0,
              remark: ''
            };
          });

          // 显示补填信息
          this.$message({
            message: `找到 ${unallocatedDepts.length} 个未分配的部门，已自动设置不冲突的排名`,
            type: 'success',
            duration: 3000
          });

          this.open = true;
        }).catch(error => {
          let errorMsg = "获取已分配部门列表失败：";
          if (error.response && error.response.data && error.response.data.msg) {
            errorMsg += error.response.data.msg;
          } else if (error.msg) {
            errorMsg += error.msg;
          } else if (error.message) {
            errorMsg += error.message;
          } else {
            errorMsg += "未知错误";
          }
          this.$modal.msgError(errorMsg);
        });
      }).catch(error => {
        this.$modal.msgError("检查月份分配状态失败：" + (error.msg || error.message || error));
      });
    },
    /** 提交补填分配 */
    submitSupplementAllocation(validDepts) {
      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      // 逐个添加部门奖金分配
      const promises = validDepts.map(dept => {
        const deptData = {
          deptId: dept.deptId,
          deptName: dept.deptName,
          allocationMonth: this.form.allocationMonth,
          performanceRank: dept.performanceRank,
          totalBonus: dept.totalBonus,
          allocatedBonus: 0,
          remainingBonus: dept.totalBonus,
          allocationStatus: '0', // 未分配
          remark: dept.remark
        };

        return addDeptBonus(deptData).then(response => {
          successCount++;
          return { success: true, dept: dept.deptName };
        }).catch(error => {
          errorCount++;
          errors.push(`${dept.deptName}: ${error.msg || error.message || error}`);
          return { success: false, dept: dept.deptName, error: error };
        });
      });

      // 等待所有请求完成
      Promise.all(promises).then(results => {
        if (successCount > 0) {
          this.$modal.msgSuccess(`补填成功：${successCount}个部门`);
          this.open = false;
          this.getList();
        }

        if (errorCount > 0) {
          this.$modal.msgError(`补填失败：${errorCount}个部门\n${errors.join('\n')}`);
        }
      }).catch(error => {
        this.$modal.msgError("补填过程中发生错误：" + (error.msg || error.message || error));
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDeptBonus(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改部门奖金分配";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDeptBonus(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // 批量分配 - 添加校验
            const validDepts = this.form.deptBonusList.filter(item =>
              item.totalBonus !== 0 &&
              item.performanceRank > 0
            );

            if (validDepts.length === 0) {
              this.$modal.msgWarning("请至少为一个部门设置奖金金额和绩效排名");
              return;
            }

            // 检查绩效排名是否重复或冲突
            const duplicateRankInfo = this.getDuplicateRankInfo();
            if (duplicateRankInfo.length > 0) {
              const duplicateMessages = duplicateRankInfo.map(item => {
                if (item.type === 'conflict') {
                  return `排名 ${item.rank}：${item.depts.join('、')} (与已分配部门冲突)`;
                } else {
                  return `排名 ${item.rank}：${item.depts.join('、')} (重复)`;
                }
              });
              this.$modal.msgWarning(`绩效排名存在问题，请检查以下排名：\n${duplicateMessages.join('\n')}`);
              return;
            }

            // 检查是否有排名错误标记
            if (this.hasRankErrors()) {
              this.$modal.msgWarning("存在排名重复错误，请先修正后再提交");
              return;
            }

            // 检查是否有部门未填写完整信息
            const incompleteDepts = validDepts.filter(item =>
              !item.performanceRank || item.totalBonus === null || item.totalBonus === undefined
            );
            if (incompleteDepts.length > 0) {
              this.$modal.msgWarning("请完善所有部门的绩效排名和奖金金额");
              return;
            }

            const submitData = {
              allocationMonth: this.form.allocationMonth,
              deptBonusList: validDepts
            };

            this.$modal.confirm(`确认为${validDepts.length}个部门分配奖金吗？`).then(() => {
              // 判断是否为补填模式
              if (this.title.includes('补填')) {
                // 补填模式：逐个添加部门奖金分配
                this.submitSupplementAllocation(validDepts);
              } else {
                // 批量分配模式
                batchAllocateDeptBonus(submitData).then(response => {
                  this.$modal.msgSuccess("分配成功");
                  this.open = false;
                  this.getList();
                }).catch(error => {
                  this.$modal.msgError("分配失败：" + (error.msg || error.message || error));
                });
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除部门奖金分配编号为"' + ids + '"的数据项？').then(function() {
        return delDeptBonus(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/deptBonus/export', {
        ...this.queryParams
      }, `deptBonus_${new Date().getTime()}.xlsx`)
    },
    /** 格式化金额 */
    formatMoney(amount) {
      if (amount == null) return '0.00';
      return '¥' + Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    /** 获取排名标签类型 */
    getRankTagType(rank) {
      if (rank <= 3) return 'success';
      if (rank <= 6) return 'warning';
      return 'danger';
    },
    /** 自动设置排名 */
    autoSetRanks() {
      if (!this.form.deptBonusList || this.form.deptBonusList.length === 0) {
        this.$modal.msgWarning("没有部门数据");
        return;
      }

      // 清除所有排名错误状态
      this.form.deptBonusList.forEach(dept => {
        this.$set(dept, 'rankError', '');
      });

      // 自动设置排名
      this.form.deptBonusList.forEach((dept, index) => {
        dept.performanceRank = index + 1;
      });

      this.$modal.msgSuccess("已自动设置排名");
    },
    /** 清空所有数据 */
    clearAllData() {
      this.$modal.confirm('确定要清空所有填写的数据吗？').then(() => {
        this.form.deptBonusList.forEach(dept => {
          dept.performanceRank = 1;
          dept.totalBonus = 0;
          dept.remark = '';
          // 清除排名错误状态
          this.$set(dept, 'rankError', '');
        });
        this.$modal.msgSuccess("已清空所有数据");
      });
    },
    /** 统一设置奖金 */
    setUniformBonus() {
      this.$prompt('请输入统一的奖金金额', '统一设置奖金', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^-?\d+(\.\d{1,2})?$/,
        inputErrorMessage: '请输入有效的金额'
      }).then(({ value }) => {
        const amount = parseFloat(value);
        this.form.deptBonusList.forEach(dept => {
          dept.totalBonus = amount;
        });
        this.$modal.msgSuccess(`已为所有部门设置奖金：${this.formatMoney(amount)}`);
      });
    },
    /** 验证排名唯一性 */
    validateRankUniqueness(currentRow, currentIndex) {
      if (!this.form.deptBonusList || !currentRow.performanceRank) {
        // 清除当前行的错误状态
        this.$set(currentRow, 'rankError', '');
        return;
      }

      // 清除所有行的错误状态
      this.form.deptBonusList.forEach(row => {
        this.$set(row, 'rankError', '');
      });

      // 检查是否有重复排名
      const currentRank = currentRow.performanceRank;
      const duplicateRows = [];

      // 检查当前表单中的重复排名
      this.form.deptBonusList.forEach((row, index) => {
        if (row.performanceRank === currentRank && index !== currentIndex) {
          duplicateRows.push({ row, index });
        }
      });

      // 补填模式下，还要检查与已分配部门的排名冲突
      let conflictWithAllocated = false;
      if (this.title.includes('补填') && this.allocatedRanks && this.allocatedRanks.includes(currentRank)) {
        conflictWithAllocated = true;
      }

      // 如果有重复或冲突，标记错误
      if (duplicateRows.length > 0 || conflictWithAllocated) {
        let errorMessage = '';
        if (conflictWithAllocated) {
          errorMessage = '与已分配部门排名冲突';
        } else {
          errorMessage = '排名重复';
        }

        // 标记当前行错误
        this.$set(currentRow, 'rankError', errorMessage);

        // 标记所有重复的行错误
        duplicateRows.forEach(({ row }) => {
          this.$set(row, 'rankError', '排名重复');
        });

        // 显示提示消息
        let message = '';
        if (conflictWithAllocated) {
          message = `排名 ${currentRank} 与已分配部门冲突，请选择其他排名`;
        } else {
          message = `排名 ${currentRank} 重复，请修改为唯一排名`;
        }

        this.$message({
          message: message,
          type: 'warning',
          duration: 3000
        });
      }
    },
    /** 检查是否有排名错误 */
    hasRankErrors() {
      if (!this.form.deptBonusList) return false;
      return this.form.deptBonusList.some(row => row.rankError);
    },
    /** 获取重复排名的详细信息 */
    getDuplicateRankInfo() {
      if (!this.form.deptBonusList) return [];

      const rankCounts = {};
      const duplicates = [];

      // 统计每个排名的出现次数
      this.form.deptBonusList.forEach(row => {
        if (row.performanceRank && row.totalBonus !== 0) {
          const rank = row.performanceRank;
          if (!rankCounts[rank]) {
            rankCounts[rank] = [];
          }
          rankCounts[rank].push(row.deptName);
        }
      });

      // 找出重复的排名
      Object.keys(rankCounts).forEach(rank => {
        if (rankCounts[rank].length > 1) {
          duplicates.push({
            rank: rank,
            depts: rankCounts[rank],
            type: 'duplicate'
          });
        }
      });

      // 补填模式下，检查与已分配部门的排名冲突
      if (this.title.includes('补填') && this.allocatedRanks) {
        this.form.deptBonusList.forEach(row => {
          if (row.performanceRank && row.totalBonus !== 0 && this.allocatedRanks.includes(row.performanceRank)) {
            duplicates.push({
              rank: row.performanceRank,
              depts: [row.deptName],
              type: 'conflict'
            });
          }
        });
      }

      return duplicates;
    }
  }
};
</script>

<style scoped>
.text-success {
  color: #67C23A;
}
.text-danger {
  color: #F56C6C;
}

/* 排名错误样式 */
.rank-error {
  border-color: #F56C6C !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;
}

.rank-error-text {
  color: #F56C6C;
  font-size: 12px;
  margin-top: 2px;
  line-height: 1;
}

/* 排名输入框样式优化 */
.el-input-number.rank-error .el-input__inner {
  border-color: #F56C6C;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}

.el-input-number.rank-error .el-input__inner:focus {
  border-color: #F56C6C;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.3);
}
</style>
