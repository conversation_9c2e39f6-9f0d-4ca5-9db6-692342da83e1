<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.ProjectMembersMapper">
    
    <resultMap type="ProjectMembers" id="ProjectMembersResult">
        <result property="id"    column="id"    />
        <result property="projectId"    column="project_id"    />
        <result property="userName"    column="user_name"    />
        <result property="role"    column="role"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectProjectMembersVo">
        select id, project_id, user_name, role, created_at, updated_at from project_members
    </sql>

    <select id="selectProjectMembersList" parameterType="ProjectMembers" resultMap="ProjectMembersResult">
        select 
            m.id, 
            m.project_id, 
            m.user_name, 
            m.role, 
            m.created_at, 
            m.updated_at,
            u.nick_name as nickName,
            d.dept_name as deptName
        from project_members m
        left join sys_user u on m.user_name = u.user_name
        left join sys_dept d on u.dept_id = d.dept_id
        <where>  
            <if test="projectId != null "> and m.project_id = #{projectId}</if>
            <if test="userName != null  and userName != ''"> and m.user_name like concat('%', #{userName}, '%')</if>
            <if test="role != null  and role != ''"> and m.role = #{role}</if>
            <if test="createdAt != null "> and m.created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and m.updated_at = #{updatedAt}</if>
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectProjectMembersById" parameterType="Long" resultMap="ProjectMembersResult">
        select DISTINCT
            m.id, 
            m.project_id, 
            m.user_name, 
            m.role, 
            m.created_at, 
            m.updated_at,
            u.nick_name,
            d.dept_id,
            d.dept_name
        from project_members m
        left join sys_user u on m.user_name = u.user_name
        left join sys_dept d on u.dept_id = d.dept_id
        where m.id = #{id}
        limit 1
    </select>

    <insert id="insertProjectMembers" parameterType="ProjectMembers" useGeneratedKeys="true" keyProperty="id">
        insert into project_members
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="role != null">role,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="role != null">#{role},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateProjectMembers" parameterType="ProjectMembers">
        update project_members
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="role != null">role = #{role},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProjectMembersById" parameterType="Long">
        delete from project_members where id = #{id}
    </delete>

    <delete id="deleteProjectMembersByIds" parameterType="String">
        delete from project_members where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据项目ID删除项目成员关联 -->
    <delete id="deleteProjectMembersByProjectId" parameterType="Long">
        delete from project_members where project_id = #{projectId}
    </delete>

    <!-- 根据项目ID查询成员列表 -->
    <select id="selectProjectMembersByProjectId" resultType="java.util.Map">
        select 
            m.id,
            m.project_id as projectId,
            m.user_name as userName,
            m.role,
            u.nick_name as nickName,
            d.dept_name as deptName
        from project_members m
        left join sys_user u on m.user_name = u.user_name
        left join sys_dept d on u.dept_id = d.dept_id
        where m.project_id = #{projectId}
        order by 
            case m.role 
                when '负责人' then 1 
                when '配合人员' then 2 
                else 3 
            end
    </select>

    <!-- 根据用户名和角色统计项目数 -->
    <select id="countProjectsByUserNameAndRole" resultType="long">
        SELECT COUNT(DISTINCT project_id)
        FROM project_members
        WHERE user_name = #{userName}
        AND role = #{role}
    </select>

    <select id="selectProjectMemberByUserName" resultMap="ProjectMembersResult">
        select id, project_id, user_name, role, created_at, updated_at 
        from project_members
        where project_id = #{projectId} and user_name = #{userName}
    </select>

    <!-- 查询项目成员详细信息，确保返回完整的用户信息 -->
    <select id="selectMemberDetailsByProjectId" resultType="java.util.Map">
        select 
            m.id,
            m.project_id as projectId,
            m.user_name as userName,
            m.role,
            u.nick_name as nickName,
            d.dept_name as deptName,
            u.user_id as userId,
            u.dept_id as deptId
        from project_members m
        inner join sys_user u on m.user_name = u.user_name
        left join sys_dept d on u.dept_id = d.dept_id
        where m.project_id = #{projectId}
    </select>
    
    <!-- 获取用户参与的所有项目 -->
    <select id="selectUserProjects" resultType="java.util.Map">
        SELECT 
            pm.id as member_id,
            pm.project_id,
            pi.project_name,
            pi.project_short_name,
            pm.user_name,
            pm.role,
            pi.dept_id,
            d.dept_name,
            pi.remarks,
            pm.created_at,
            pm.updated_at
        FROM 
            project_members pm
        JOIN 
            project_info pi ON pm.project_id = pi.id
        LEFT JOIN
            sys_dept d ON pi.dept_id = d.dept_id
        WHERE 
            pm.user_name = #{userName}
        ORDER BY
            CASE pm.role 
                WHEN '负责人' THEN 1 
                WHEN '配合人员' THEN 2 
                ELSE 3 
            END,
            pi.project_name ASC
    </select>
    
    <!-- 获取部门下有项目的用户及其项目信息 -->
    <select id="selectDeptMembersWithProjects" resultType="java.util.Map">
        SELECT 
            u.user_id,
            u.user_name,
            u.nick_name,
            u.dept_id,
            d.dept_name,
            GROUP_CONCAT(DISTINCT pm.project_id) AS project_ids,
            COUNT(DISTINCT pm.project_id) AS project_count
        FROM 
            sys_user u
        JOIN 
            sys_dept d ON u.dept_id = d.dept_id
        JOIN 
            project_members pm ON u.user_name = pm.user_name
        WHERE
            u.dept_id IN
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            AND pm.role != '负责人'
            AND u.del_flag = '0'
        GROUP BY 
            u.user_id
        HAVING 
            COUNT(DISTINCT pm.project_id) > 0
        ORDER BY 
            u.user_name
    </select>
</mapper>