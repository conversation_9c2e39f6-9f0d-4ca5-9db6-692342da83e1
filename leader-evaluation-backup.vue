<template>
  <div class="app-container">
    <!-- 顶部搜索区域 -->
    <el-card class="search-card">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="100px">
        <el-form-item label="评价月份" prop="evaluationMonth">
          <el-date-picker
            v-model="queryParams.evaluationMonth"
            type="month"
            value-format="yyyy-MM"
            :picker-options="pickerOptions"
            placeholder="请选择评价月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 警告信息 -->
    <el-alert
      v-if="projectOptions.length === 0"
      title="您暂无负责的项目，无法进行项目负责人评价"
      type="info"
      :closable="false"
      show-icon
      style="margin: 15px 0;">
    </el-alert>

    <!-- 内容区域 -->
    <div v-else class="content-container">
      <!-- 左侧项目列表 -->
      <el-card class="project-card">
        <div slot="header" class="clearfix">
          <span>负责项目列表</span>
          <!-- <el-button style="float: right; padding: 3px 0" type="text" @click="refreshProjectList">刷新数据</el-button> -->
        </div>
        <div class="project-list">
          <div 
            v-for="(project, index) in projectOptions" 
            :key="'project-' + project.id + '-' + index"
            :class="['project-item', {active: currentProjectId === project.id}]"
            @click="selectProject(project.id)">
            <i class="el-icon-folder"></i>
            <span class="project-name">{{ project.projectName }}</span>
          </div>
        </div>
      </el-card>

      <!-- 右侧成员详情 -->
      <el-card class="member-card" v-loading="loading">
        <div slot="header" class="clearfix">
          <span>{{ getCurrentProjectName() }} - 成员列表</span>
          <span class="header-month">评价月份: {{ queryParams.evaluationMonth || '未选择' }}</span>
          <!-- <el-button 
            type="primary" 
            size="mini" 
            style="float: right;" 
            @click="batchSaveEvaluations">提交</el-button> -->
        </div>

        <div v-if="!currentProjectId" class="placeholder-info">
          请在左侧选择一个项目
        </div>
        <div v-else-if="!memberList[currentProjectId] || memberList[currentProjectId].length === 0" class="placeholder-info">
          该项目暂无成员或成员数据加载中...
        </div>
        <div v-else>
          <el-alert
            title="提示：直接对每个人评分，完成后点击提交按钮保存所有评分"
            type="info"
            :closable="false"
            show-icon
            style="margin-bottom: 15px;">
          </el-alert>
          
          <el-table 
            :data="memberList[currentProjectId]" 
            border 
            style="width: 100%"
            row-key="_uid"
            :cell-class-name="getCellClassName"
            :row-class-name="getRowClassName">
            <el-table-column label="序号" type="index" width="50" align="center">
              <template slot="header">
                <span>序号</span>
              </template>
              <template slot-scope="scope">
                <template v-if="scope.row._isAddButton">
                  <!-- 移除添加行按钮 -->
                </template>
                <template v-else>
                  {{ scope.$index + 1 }}
                </template>
              </template>
            </el-table-column>
            <el-table-column label="成员姓名" prop="nickName" min-width="120" align="center">
              <template slot-scope="scope">
                <template v-if="scope.row._isAddButton">
                  <!-- 空白，不显示文字 -->
                </template>
                <template v-else-if="scope.row.isNew">
                  <el-select 
                    v-model="scope.row.nickName" 
                    filterable 
                    placeholder="请选择成员" 
                    @change="handleMemberSelect($event, scope.row)">
                    <el-option
                      v-for="user in availableDeptUsers"
                      :key="user.userName"
                      :label="user.nickName || user.userName"
                      :value="user.nickName || user.userName">
                      <span>{{ user.nickName || user.userName }}</span>
                      <span style="float: right; color: #8492a6; font-size: 13px">{{ user.dept ? user.dept.deptName : '' }}</span>
                    </el-option>
                  </el-select>
                </template>
                <template v-else>
                  {{ scope.row.nickName || scope.row.userName }}
                </template>
              </template>
            </el-table-column>
            <el-table-column label="员工编号" prop="userName" min-width="120" align="center">
              <template slot-scope="scope">
                <template v-if="scope.row._isAddButton">
                  <!-- 空白，不显示文字 -->
                </template>
                <template v-else-if="scope.row.isNew">
                  <span>{{ scope.row.userName }}</span>
                </template>
                <template v-else>
                  {{ scope.row.userName }}
                </template>
              </template>
            </el-table-column>
            <el-table-column label="所属部门" prop="deptName" min-width="150" align="center">
              <template slot-scope="scope">
                <template v-if="scope.row._isAddButton">
                  <!-- 空白，不显示文字 -->
                </template>
                <template v-else-if="scope.row.isNew">
                  <span>{{ scope.row.deptName }}</span>
                </template>
                <template v-else>
                  {{ scope.row.deptName }}
                </template>
              </template>
            </el-table-column>
            <el-table-column label="评分" min-width="220" align="center">
              <template slot-scope="scope">
                <template v-if="scope.row._isAddButton">
                  <!-- 空白，不显示文字 -->
                </template>
                <template v-else>
                  <el-input-number
                    v-model="scope.row.newScore"
                    :min="0"
                    :max="100"
                    @change="handleScoreChange(scope.row)"
                    size="small"
                    controls-position="right">
                  </el-input-number>
                  <span class="score-unit">分</span>
                  <span class="score-limit">(0-100)</span>
                </template>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 添加按钮和表格底部 -->
          <div class="table-footer-wrapper">
            <!-- 移除添加按钮容器 -->
            
            <div class="table-footer">
              <el-button 
                type="primary" 
                @click="batchSaveEvaluations">提交</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 评价对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="项目名称">
          <span>{{ form.projectName }}</span>
        </el-form-item>
        <el-form-item label="被评价人">
          <span>{{ form.userName }}</span>
        </el-form-item>
        <el-form-item label="评价月份">
          <span>{{ form.evaluationMonth }}</span>
        </el-form-item>
        <el-form-item label="评分" prop="score">
          <el-input-number
            v-model="form.score"
            :min="0"
            :max="100"
            controls-position="right">
          </el-input-number>
          <span class="score-unit">分</span>
          <span class="score-limit">(0-100)</span>
        </el-form-item>
        <el-form-item label="评价意见" prop="comments">
          <el-input
            type="textarea"
            v-model="form.comments"
            placeholder="请输入评价意见"
            maxlength="500"
            show-word-limit
            :rows="4">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUserLeadProjects } from "@/api/system/info";
import { listMembers } from "@/api/system/members";
import { addEvaluation, listEvaluation, updateEvaluation } from "@/api/system/evaluation";
import { listUser } from "@/api/system/user";
import { listParticipation, listAllParticipation } from "@/api/system/participation";

export default {
  name: "LeaderEvaluation",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 项目选项
      projectOptions: [],
      // 当前选中的项目ID
      currentProjectId: null,
      // 成员列表 (按项目ID分组)
      memberList: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期选择器配置
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() > new Date().setDate(1);
        }
      },
      // 查询参数
      queryParams: {
        evaluationMonth: this.getCurrentMonth()
      },
      // 表单参数
      form: {
        projectId: null,
        projectName: null,
        evaluatorId: null,
        evaluateeId: null,
        userName: null,
        score: 0,
        evaluationMonth: null,
        evaluationType: "project_leader",
        comments: null
      },
      // 表单校验
      rules: {
        score: [
          { required: true, message: "评分不能为空", trigger: "change" }
        ]
      },
      // 是否有未保存的评分
      hasUnsavedChanges: false,
      // 所有用户信息 (用于获取nickName)
      userMap: {},
      // 可用部门用户
      availableDeptUsers: []
    };
  },
  created() {
    this.getUserLeadProjects();
    this.loadDeptUsers();
  },
  methods: {
    // 获取当前月份，格式为yyyy-MM
    getCurrentMonth() {
      const date = new Date();
      const year = date.getFullYear();
      let month = date.getMonth() + 1;
      month = month < 10 ? '0' + month : month;
      return `${year}-${month}`;
    },
    // 获取当前用户所属部门的所有用户
    loadDeptUsers() {
      // 如果已经加载过且数据不为空，则不再重新加载
      if (this.availableDeptUsers && this.availableDeptUsers.length > 0) {
        return Promise.resolve(this.availableDeptUsers);
      }
      
      // 加载所有用户信息
      return listUser({
        includeDeleted: false,
        includeDept: true
      }).then(response => {
        const users = response.rows || [];
        
        // 建立userName到用户信息的映射
        this.userMap = {};
        users.forEach(user => {
          this.userMap[user.userName] = user;
        });
        
        // 过滤出有部门信息的用户
        this.availableDeptUsers = users.filter(user => user.dept && user.dept.deptName);
        
        return this.availableDeptUsers;
      });
    },
    // 加载所有用户信息，用于获取nickName
    loadAllUsers() {
      return this.loadDeptUsers();
    },
    // 更新成员的nickName和部门信息
    updateMembersNickName(projectId) {
      if (!this.memberList[projectId]) return;
      
      const members = this.memberList[projectId];
      members.forEach(member => {
        const user = this.userMap[member.userName];
        if (user) {
          member.nickName = user.nickName;
          // 更新部门信息
          if (user.dept && user.dept.deptName) {
            member.deptName = user.dept.deptName;
          }
        }
      });
      
      // 更新成员列表
      this.$set(this.memberList, projectId, [...members]);
    },
    // 获取用户负责的项目
    getUserLeadProjects() {
      this.loading = true;
      return new Promise((resolve, reject) => {
        listUserLeadProjects().then(response => {
          // 对项目数据进行去重处理，基于id
          const projectMap = new Map();
          (response.rows || []).forEach(project => {
            // 只保留唯一的项目ID
            if (project && project.id && !projectMap.has(project.id)) {
              projectMap.set(project.id, project);
            }
          });
          
          // 将Map转换回数组并排序，确保每次顺序一致
          let projectArray = Array.from(projectMap.values());
          
          // 按项目ID或项目名称排序
          projectArray.sort((a, b) => {
            // 优先按项目ID排序（如果ID是数字）
            if (!isNaN(a.id) && !isNaN(b.id)) {
              return a.id - b.id;
            }
            // 如果ID不是数字或相同，则按项目名称排序
            return (a.projectName || '').localeCompare(b.projectName || '');
          });
          
          this.projectOptions = projectArray;
          
          this.loading = false;
          if (this.projectOptions.length > 0 && !this.currentProjectId) {
            // 默认选中第一个项目，但仅当没有选择项目时
            this.selectProject(this.projectOptions[0].id);
          }
          resolve(this.projectOptions);
        }).catch(error => {
          this.loading = false;
          reject(error);
        });
      });
    },
    // 选择项目
    selectProject(projectId) {
      if (this.currentProjectId === projectId) return;
      
      // 检查是否有未保存的更改
      if (this.hasUnsavedChanges) {
        this.$confirm('有未保存的评分，切换项目将丢失这些更改，是否继续?', '提示', {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.currentProjectId = projectId;
          this.hasUnsavedChanges = false;
          // 预先初始化一个空的成员列表，避免undefined
          if (!this.memberList[projectId]) {
            this.$set(this.memberList, projectId, []);
          }
          this.loadProjectMembers(projectId);
          
          // 加载部门用户
          this.loadDeptUsers();
        }).catch(() => {});
      } else {
        this.currentProjectId = projectId;
        // 预先初始化一个空的成员列表，避免undefined
        if (!this.memberList[projectId]) {
          this.$set(this.memberList, projectId, []);
        }
        this.loadProjectMembers(projectId);
        
        // 加载部门用户
        this.loadDeptUsers();
      }
    },
    // 加载项目成员
    loadProjectMembers(projectId) {
      if (!projectId) return;
      
      this.loading = true;
      
      // 保存当前请求的项目ID，用于后续验证
      const requestProjectId = projectId;
      
      // 获取当前选择的月份
      const month = this.queryParams.evaluationMonth;
      if (!month) {
        this.$modal.msgError("请先选择评价月份");
        this.loading = false;
        return;
      }
      
      // 使用listParticipation替代listMembers，查询参与该项目的所有人员，添加月份过滤条件
      listAllParticipation({ 
        projectId: projectId,
        month: month // 添加月份条件
      }).then(response => {
        // 检查当前处理的项目ID是否仍然是用户选中的项目
        if (requestProjectId !== this.currentProjectId) {
          this.loading = false;
          return;
        }
        
        // 从项目参与度表中获取成员数据
        let allParticipations = response.data || [];
        
        // 按照userName进行分组去重，只保留每个用户的一条记录
        const participationMap = new Map();
        
        allParticipations.forEach(part => {
          if (!participationMap.has(part.userName)) {
            participationMap.set(part.userName, part);
          }
        });
        
        // 将参与度记录转换为成员记录
        let members = Array.from(participationMap.values()).map((part, index) => ({
          userName: part.userName,
          _uid: `${projectId}-${part.userName || 'unknown'}-${index}`,
          newScore: 0, // 默认评分为0
          deptName: part.deptName || '未知部门', // 确保部门名称有默认值
          deptId: part.deptId,
          projectId: projectId, // 确保设置projectId
          projectName: this.getCurrentProjectName() // 添加项目名称
        }));
        
        // 使用userMap更新成员昵称和部门信息
        members.forEach(member => {
          const user = this.userMap[member.userName];
          if (user) {
            member.nickName = user.nickName;
            // 如果用户信息中有部门信息，则使用用户信息中的部门
            if (user.dept && user.dept.deptName) {
              member.deptName = user.dept.deptName;
            }
          }
        });
        
        // 按项目ID存储项目成员
        this.$set(this.memberList, projectId, [...members]);
        
        // 如果有成员，加载评价信息
        if (members.length > 0 && this.queryParams.evaluationMonth) {
          this.getEvaluations(projectId, members);
        } else {
          this.loading = false;
        }
      }).catch(error => {
        this.loading = false;
        console.error('加载项目成员失败:', error);
      });
    },
    // 获取评价记录
    getEvaluations(projectId, members) {
      const month = this.queryParams.evaluationMonth;
      
      // 保存当前请求的项目ID，用于后续验证
      const requestProjectId = projectId;
      
      this.loading = true;
      listEvaluation({
        projectId: projectId,
        evaluationMonth: month,
        evaluationType: "project_leader"
      }).then(response => {
        // 检查当前处理的项目ID是否仍然是用户选中的项目
        // 如果用户已经切换到其他项目，则不处理这个响应
        if (requestProjectId !== this.currentProjectId) {
          this.loading = false;
          return;
        }
        
        // 获取并去重评价记录
        let evaluations = response.rows || [];
        
        // 去除重复的评价记录（基于evaluateeId）
        const uniqueEvaluations = [];
        const evaluationMap = new Map();
        
        evaluations.forEach(evaluation => {
          const key = `${evaluation.evaluateeId}`;
          if (!evaluationMap.has(key)) {
            evaluationMap.set(key, evaluation);
            uniqueEvaluations.push(evaluation);
          } else {
            // 如果发现重复，保留ID较大的记录（通常是较新的）
            const existingEval = evaluationMap.get(key);
            if (evaluation.id > existingEval.id) {
              evaluationMap.set(key, evaluation);
              // 替换数组中的记录
              const index = uniqueEvaluations.indexOf(existingEval);
              if (index !== -1) {
                uniqueEvaluations[index] = evaluation;
              }
            }
          }
        });
        
        evaluations = uniqueEvaluations;
        
        // 找到当前项目
        const project = this.projectOptions.find(p => p.id === projectId);
        const projectName = project ? project.projectName : "";
        
        // 创建员工编号到评价记录的映射，方便查找
        const evaluationMap2 = {};
        evaluations.forEach(evaluation => {
          evaluationMap2[evaluation.evaluateeId] = evaluation;
        });
        
        // 把评价信息合并到成员列表
        members.forEach(member => {
          // 获取用户的完整信息，包括userId
          const userInfo = this.userMap[member.userName];
          if (userInfo) {
            member.userId = userInfo.userId;
          }
          
          // 查找当前月份该成员的评价记录
          const evaluation = evaluationMap2[member.userId];
          
          if (evaluation) {
            member.evaluated = true;
            member.currentScore = evaluation.score;
            member.newScore = evaluation.score; // 添加新分数字段，用于跟踪更改
            member.evaluationId = evaluation.id;
            member.comments = evaluation.comments || "";
          } else {
            member.evaluated = false;
            member.currentScore = null;
            member.newScore = 0; // 默认分数为0分
            member.comments = "";
          }
          
          member.changed = false; // 标记是否有更改
          member.projectId = projectId;
          member.projectName = projectName;
          member.evaluationMonth = month; // 添加评价月份
          // 确保保留_uid字段
        });
        
        // 更新成员列表
        this.$set(this.memberList, projectId, [...members]); // 使用展开运算符创建新数组，确保Vue检测到变化
        this.hasUnsavedChanges = false;
        this.loading = false;
      }).catch(error => {
        // 即使请求失败，也确保设置默认分数为0
        if (this.memberList[projectId]) {
          this.memberList[projectId].forEach(member => {
            if (!member.evaluated) {
              member.newScore = 0;
            }
          });
          
          this.$set(this.memberList, projectId, [...this.memberList[projectId]]);
        }
        this.loading = false;
      });
    },
    // 处理分数变更
    handleScoreChange(row) {
      // 确保分数在0-100范围内
      if (row.newScore < 0) {
        row.newScore = 0;
      } else if (row.newScore > 100) {
        row.newScore = 100;
      }
      
      // 标记为已更改
      if (row.evaluated) {
        row.changed = row.currentScore !== row.newScore;
      } else {
        row.changed = true;
      }
      
      // 更新是否有未保存的变更状态
      this.checkUnsavedChanges();
    },
    // 检查是否有未保存的更改
    checkUnsavedChanges() {
      if (!this.currentProjectId || !this.memberList[this.currentProjectId]) {
        this.hasUnsavedChanges = false;
        return;
      }
      
      this.hasUnsavedChanges = this.memberList[this.currentProjectId].some(member => member.changed);
    },
    // 批量保存评分
    batchSaveEvaluations() {
      if (!this.currentProjectId || !this.memberList[this.currentProjectId]) return;
      
      // 获取所有成员，不再需要过滤_isAddButton
      const currentMembers = this.memberList[this.currentProjectId];
      
      if (currentMembers.length === 0) {
        this.$message.info('没有可提交的评分');
        return;
      }
      
      // 检查重复人员
      const userNameMap = {};
      let hasDuplicate = false;
      let duplicateUserName = '';
      
      for (const member of currentMembers) {
        if (!member.userName) {
          this.$modal.msgError("存在未填写员工编号的记录，请完善信息后再提交");
          return;
        }
        
        if (userNameMap[member.userName]) {
          hasDuplicate = true;
          duplicateUserName = member.userName;
          break;
        }
        
        userNameMap[member.userName] = true;
      }
      
      if (hasDuplicate) {
        this.$modal.msgError(`存在重复的人员【${duplicateUserName}】，请检查后再提交`);
        return;
      }
      
      // 检查零分评价
      const zeroScoreMembers = currentMembers.filter(member => member.newScore === 0);
      if (zeroScoreMembers.length > 0) {
        const zeroScoreName = zeroScoreMembers[0].nickName || zeroScoreMembers[0].userName;
        this.$modal.msgError(`人员【${zeroScoreName}】的评分为0分，请评分后再提交`);
        return;
      }
      
      // 确保有评价人ID
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$modal.msgError("获取当前用户信息失败，请重新登录后再试");
        return;
      }
      
      // 确保选择了评价月份
      const evaluationMonth = this.queryParams.evaluationMonth;
      if (!evaluationMonth) {
        this.$modal.msgError("请先选择评价月份");
        return;
      }
      
      this.loading = true;
      
      // 查询本月已有的评价记录
      listEvaluation({
        projectId: this.currentProjectId,
        evaluationMonth: evaluationMonth,
        evaluationType: "project_leader"
      }).then(response => {
        const existingEvaluations = response.rows || [];
        
        // 创建员工编号到评价记录的映射
        const evaluationMap = {};
        existingEvaluations.forEach(evaluation => {
          evaluationMap[evaluation.evaluateeId] = evaluation;
        });
        
        // 创建保存请求
        const savePromises = currentMembers.map(member => {
          // 检查员工编号是否为空
          if (!member.userName) {
            return Promise.reject(new Error(`有成员的员工编号为空，请完善信息后再提交`));
          }
          
                              // 查找该用户本月是否已有评价          const existingEval = evaluationMap[member.userId];                    const data = {            projectId: member.projectId,            evaluatorId: evaluatorId,            evaluateeId: member.userId,            score: member.newScore,            evaluationMonth: evaluationMonth,            evaluationType: "project_leader",            comments: member.comments || ""          };
          
          if (existingEval) {
            // 如果本月已有评价，则更新
            data.id = existingEval.id;
            return updateEvaluation(data);
          } else {
            // 否则新增
            return addEvaluation(data);
          }
        });
        
        // 执行所有保存请求
        Promise.all(savePromises)
          .then(() => {
            this.$modal.msgSuccess("提交成功");
            
            // 完整刷新数据
            this.refreshData(this.currentProjectId);
          })
          .catch((error) => {
            this.$modal.msgError(error.message || "保存失败，请重试");
            this.loading = false;
          });
      }).catch(error => {
        this.$modal.msgError("获取评价记录失败，请重试");
        this.loading = false;
      });
    },
    // 获取当前项目名称
    getCurrentProjectName() {
      if (!this.currentProjectId) return "请选择项目";
      const project = this.projectOptions.find(p => p.id === this.currentProjectId);
      return project ? project.projectName : "未知项目";
    },
    // 获取已评价成员数量
    getEvaluatedCount(projectId) {
      if (!this.memberList[projectId]) return 0;
      return this.memberList[projectId].filter(m => m.evaluated).length;
    },
    // 获取总成员数量
    getTotalCount(projectId) {
      if (!this.memberList[projectId]) return 0;
      return this.memberList[projectId].length;
    },
    // 获取状态标签类型
    getTagType(row) {
      if (row.evaluated) return 'success';
      return 'info';
    },
    // 获取状态文本
    getStatusText(row) {
      if (row.evaluated) return '已评价';
      return '未评价';
    },
    // 设置行的类名
    getRowClassName({row, rowIndex}) {
      if (row.evaluated) return 'evaluated-row';
      return '';
    },
    // 刷新项目列表
    refreshProjectList() {
      const currentId = this.currentProjectId;
      this.getUserLeadProjects();
      // 保持当前选中的项目
      if (currentId) {
        this.selectProject(currentId);
      }
    },
    // 搜索按钮操作
    handleQuery() {
      if (this.currentProjectId) {
        // 直接调用loadProjectMembers重新加载当前月份的成员数据
        this.loadProjectMembers(this.currentProjectId);
      }
    },
    // 重置按钮操作
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.evaluationMonth = this.getCurrentMonth();
      this.handleQuery();
    },
    // 打开评价对话框
    handleEvaluate(row) {
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$modal.msgError("获取当前用户信息失败，请重新登录后再试");
        return;
      }
      
            this.form = {        projectId: row.projectId,        projectName: row.projectName,        evaluatorId: evaluatorId,        evaluateeId: row.userId,        userName: row.nickName || row.userName, // 显示昵称        score: 0,        evaluationMonth: this.queryParams.evaluationMonth,        evaluationType: "project_leader",        comments: ""      };
      this.title = "项目负责人评价";
      this.open = true;
    },
    // 查看评价详情
    handleViewEvaluation(row) {
      const evaluatorId = this.$store.state.user.id;
      if (!evaluatorId) {
        this.$modal.msgError("获取当前用户信息失败，请重新登录后再试");
        return;
      }
      
      this.form = {
        id: row.evaluationId,
        projectId: row.projectId,
        projectName: row.projectName,
        evaluatorId: evaluatorId,
        evaluateeId: row.userName,
        userName: row.nickName || row.userName, // 显示昵称
        score: row.currentScore,
        evaluationMonth: this.queryParams.evaluationMonth,
        evaluationType: "project_leader",
        comments: row.comments || ""
      };
      this.title = "查看/修改评价";
      this.open = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        projectId: null,
        projectName: null,
        evaluatorId: null,
        evaluateeId: null,
        userName: null,
        score: 0,
        evaluationMonth: null,
        evaluationType: "project_leader",
        comments: null
      };
      this.resetForm("form");
    },
    // 提交评价
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保分数在0-100范围内
          if (this.form.score <= 0) {
            this.$modal.msgError("评分必须大于0");
            return;
          }
          if (this.form.score > 100) {
            this.form.score = 100;
          }
          
          this.loading = true;
          
          // 保存当前项目ID
          const currentProjectId = this.form.projectId;
          
          if (this.form.id) {
            updateEvaluation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              
              // 完整刷新数据
              this.refreshData(currentProjectId);
            }).catch(error => {
              this.$modal.msgError("修改失败，请重试");
              this.loading = false;
            });
          } else {
            addEvaluation(this.form).then(response => {
              this.$modal.msgSuccess("评价成功");
              this.open = false;
              
              // 完整刷新数据
              this.refreshData(currentProjectId);
            }).catch(error => {
              this.$modal.msgError("评价失败，请重试");
              this.loading = false;
            });
          }
        }
      });
    },
    
    // 完整刷新数据
    refreshData(projectId) {
      this.loading = true;
      
      // 先重新获取项目列表
      this.getUserLeadProjects().then(() => {
        // 如果指定的项目仍然存在，则重新选择它
        if (projectId && this.projectOptions.some(p => p.id === projectId)) {
          this.currentProjectId = projectId;
          // 重新加载该项目的成员和评价数据
          this.loadProjectMembers(projectId);
        } else if (this.projectOptions.length > 0) {
          // 如果原项目不存在，则选择第一个项目
          this.selectProject(this.projectOptions[0].id);
        } else {
          this.loading = false;
        }
      }).catch(error => {
        this.loading = false;
      });
    },
    // 设置单元格的类名
    getCellClassName({row, column, rowIndex, columnIndex}) {
      return '';
    },
  }
};
</script>

<style scoped>
.search-card {
  margin-bottom: 15px;
}

.content-container {
  display: flex;
  gap: 15px;
}

.project-card {
  width: 300px;
  flex-shrink: 0;
}

.member-card {
  flex-grow: 1;
}

.project-list {
  max-height: 600px;
  overflow-y: auto;
}

.project-item {
  padding: 12px 15px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

.project-item:hover {
  background-color: #f5f7fa;
}

.project-item.active {
  background-color: #ecf5ff;
  color: #409eff;
  font-weight: bold;
}

.project-name {
  margin-left: 10px;
  flex-grow: 1;
}

.header-month {
  margin-left: 15px;
  color: #909399;
  font-size: 14px;
}

.placeholder-info {
  text-align: center;
  padding: 50px 0;
  color: #909399;
  font-size: 14px;
}

.table-footer-wrapper {
  position: relative;
  width: 100%;
}

.table-footer {
  margin-top: 20px;
  margin-bottom: 10px;
  text-align: center;
}

.evaluated-row {
  background-color: #f0f9eb;
}

.changed-row {
  background-color: #fdf6ec;
}

.el-rate {
  display: inline-block;
}

.score-changed {
  margin-left: 10px;
  color: #e6a23c;
  font-size: 12px;
}

.score-unit {
  margin-left: 5px;
  color: #606266;
}

.score-limit {
  margin-left: 5px;
  color: #909399;
  font-size: 12px;
}
</style> 