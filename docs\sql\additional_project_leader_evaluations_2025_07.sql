-- ===== 补充段琦玮、杨文博、蒋成的项目负责人评分记录 =====
-- 评价月份：2025-07
-- 基于用户提供的精力分配明细和评分状态生成

-- ===== 段琦玮（155）的未评分项目 =====

-- 1. 大唐多伦15万千瓦风光制氢一体化能量管控平台（项目316）- 负责人：刘海洋（181）
-- 精力分配：70.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2001, 316, 181, 155, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 杨文博（174）的未评分项目 =====

-- 1. 二氧化碳提纯和高值利用关键技术研究（项目271）- 负责人：冯少广（156）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2002, 271, 156, 174, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 无储能风电离网制氢系统关键技术研究（项目272）- 负责人：段琦玮（155）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2003, 272, 155, 174, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 绿氢制备系统高频谐波诊断与集群协同控制技术研究（项目273）- 负责人：冯少广（156）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2004, 273, 156, 174, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 新型低成本制氢技术开发（项目312）- 负责人：刘海洋（181）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2005, 312, 181, 174, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 5. 大唐多伦15万千瓦风光制氢一体化能量管控平台（项目316）- 负责人：刘海洋（181）
-- 精力分配：40.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2006, 316, 181, 174, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 6. 氢电耦合零碳园区建设模式与机制研究（项目324）- 负责人：冯少广（156）
-- 精力分配：5.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2007, 324, 156, 174, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 7. 新型低成本光伏电催化制氢技术及成套装备（项目326）- 负责人：刘海洋（181）
-- 精力分配：15.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2008, 326, 181, 174, 96.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 蒋成（202）的未评分项目 =====

-- 1. 化学链燃烧发电关键技术（项目257）- 负责人：刘海洋（181）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2009, 257, 181, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 无储能风电离网制氢系统关键技术研究（项目272）- 负责人：段琦玮（155）
-- 精力分配：10.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2010, 272, 155, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 绿氢制备系统高频谐波诊断与集群协同控制技术研究（项目273）- 负责人：冯少广（156）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2011, 273, 156, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 氢电耦合零碳园区建设模式与机制研究（项目324）- 负责人：冯少广（156）
-- 精力分配：30.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2012, 324, 156, 202, 99.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 5. 新型低成本制氢技术开发（项目312）- 负责人：刘海洋（181）
-- 精力分配：40.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2013, 312, 181, 202, 98.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 6. 大唐甘肃白银风光耦合离网制氢示范项目技术服务（项目325）- 负责人：蒋成（202）
-- 精力分配：20.0%，评分状态：未评分（自评）
INSERT INTO `project_evaluation` VALUES (2014, 325, 202, 202, 99.00, '2025-07', 'project_leader', '项目负责人自评', NOW(), NOW());

-- 7. 新型低成本光伏电催化制氢技术及成套装备（项目326）- 负责人：刘海洋（181）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2015, 326, 181, 202, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 张杰（203）的未评分项目 =====

-- 1. 绿氢制备系统高频谐波诊断与集群协同控制技术研究（项目273）- 负责人：冯少广（156）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2016, 273, 156, 203, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 新型低成本制氢技术开发（项目312）- 负责人：刘海洋（181）
-- 精力分配：40.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2017, 312, 181, 203, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 大唐甘肃白银风光耦合离网制氢示范项目技术服务（项目325）- 负责人：蒋成（202）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2018, 325, 202, 203, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 新型低成本光伏电催化制氢技术及成套装备（项目326）- 负责人：刘海洋（181）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (2019, 326, 181, 203, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 验证插入的记录 =====
SELECT 
    '新增评分记录验证' as verification_type,
    pe.id,
    pi.project_name,
    su_evaluator.nick_name as evaluator_name,
    su_evaluatee.nick_name as evaluatee_name,
    pe.score,
    pe.evaluation_month,
    pe.evaluation_type,
    pe.comments
FROM project_evaluation pe
INNER JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE pe.id BETWEEN 2001 AND 2019
ORDER BY pe.id;

-- ===== 统计各人的项目负责人评分完成情况 =====

-- 段琦玮的项目负责人评分统计
SELECT 
    '段琦玮项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '段琦玮'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 杨文博的项目负责人评分统计
SELECT 
    '杨文博项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '杨文博'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 蒋成的项目负责人评分统计
SELECT
    '蒋成项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '蒋成'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- 张杰的项目负责人评分统计
SELECT
    '张杰项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '张杰'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- ===== 计算最终评分验证 =====
-- 验证最终评分计算（机构负责人评分 * 0.4 + 项目负责人平均分 * 0.6）

-- 段琦玮最终评分计算
SELECT 
    '段琦玮最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '段琦玮' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2 
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id 
         WHERE su2.nick_name = '段琦玮' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 杨文博最终评分计算
SELECT 
    '杨文博最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '杨文博' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2 
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id 
         WHERE su2.nick_name = '杨文博' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 蒋成最终评分计算
SELECT
    '蒋成最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        (SELECT pe1.score FROM project_evaluation pe1
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id
         WHERE su1.nick_name = '蒋成' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '蒋成' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- 张杰最终评分计算
SELECT
    '张杰最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score
FROM (
    SELECT
        (SELECT pe1.score FROM project_evaluation pe1
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id
         WHERE su1.nick_name = '张杰' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id
         WHERE su2.nick_name = '张杰' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- ===== 按精力分配比例加权的项目负责人评分计算 =====
-- 注意：这里提供了一个可选的加权计算方法，考虑精力分配比例

-- 段琦玮加权项目负责人评分（考虑精力分配比例）
SELECT 
    '段琦玮加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (84 * 0.15 + 84 * 0.15 + 94 * 0.70) / (0.15 + 0.15 + 0.70), 2
    ) as weighted_project_leader_score
FROM dual;

-- 杨文博加权项目负责人评分（考虑精力分配比例）
SELECT 
    '杨文博加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (98 * 0.10 + 94 * 0.10 + 99 * 0.10 + 98 * 0.10 + 94 * 0.40 + 94 * 0.05 + 96 * 0.15) / 
        (0.10 + 0.10 + 0.10 + 0.10 + 0.40 + 0.05 + 0.15), 2
    ) as weighted_project_leader_score
FROM dual;

-- 蒋成加权项目负责人评分（考虑精力分配比例）
SELECT 
    '蒋成加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (99 * 0.10 + 99 * 0.10 + 99 * 0.20 + 99 * 0.30 + 98 * 0.40 + 99 * 0.20 + 94 * 0.20) / 
        (0.10 + 0.10 + 0.20 + 0.30 + 0.40 + 0.20 + 0.20), 2
    ) as weighted_project_leader_score
FROM dual;
