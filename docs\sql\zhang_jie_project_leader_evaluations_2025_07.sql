-- ===== 补充张杰的项目负责人评分记录 =====
-- 评价月份：2025-07
-- 基于用户提供的精力分配明细和评分状态生成

-- 张杰用户信息：
-- 用户ID: 203
-- 姓名: 张杰
-- 所属部门: 氢能制备及耦合技术研究所
-- 机构负责人评分: 92分

-- ===== 张杰（203）的未评分项目 =====

-- 1. 绿氢制备系统高频谐波诊断与集群协同控制技术研究（项目273）- 负责人：冯少广（156）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (3001, 273, 156, 203, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 2. 新型低成本制氢技术开发（项目312）- 负责人：刘海洋（181）
-- 精力分配：40.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (3002, 312, 181, 203, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 3. 大唐甘肃白银风光耦合离网制氢示范项目技术服务（项目325）- 负责人：蒋成（202）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (3003, 325, 202, 203, 92.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- 4. 新型低成本光伏电催化制氢技术及成套装备（项目326）- 负责人：刘海洋（181）
-- 精力分配：20.0%，评分状态：未评分
INSERT INTO `project_evaluation` VALUES (3004, 326, 181, 203, 94.00, '2025-07', 'project_leader', '', NOW(), NOW());

-- ===== 验证插入的记录 =====
SELECT 
    '张杰新增评分记录验证' as verification_type,
    pe.id,
    pi.project_name,
    su_evaluator.nick_name as evaluator_name,
    su_evaluatee.nick_name as evaluatee_name,
    pe.score,
    pe.evaluation_month,
    pe.evaluation_type,
    pe.comments
FROM project_evaluation pe
INNER JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE pe.id BETWEEN 3001 AND 3004
ORDER BY pe.id;

-- ===== 张杰的项目负责人评分统计 =====
SELECT 
    '张杰项目负责人评分统计' as person,
    COUNT(*) as total_evaluations,
    AVG(pe.score) as average_score,
    MIN(pe.score) as min_score,
    MAX(pe.score) as max_score
FROM project_evaluation pe
INNER JOIN sys_user su ON pe.evaluatee_id = su.user_id
WHERE su.nick_name = '张杰'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07';

-- ===== 张杰的详细评分明细 =====
SELECT 
    '张杰详细评分明细' as detail_type,
    pi.project_name,
    su_evaluator.nick_name as evaluator_name,
    pe.score,
    CASE 
        WHEN pi.project_name LIKE '%绿氢制备系统高频谐波诊断%' THEN '20.0%'
        WHEN pi.project_name LIKE '%新型低成本制氢技术开发%' THEN '40.0%'
        WHEN pi.project_name LIKE '%大唐甘肃白银风光耦合%' THEN '20.0%'
        WHEN pi.project_name LIKE '%新型低成本光伏电催化%' THEN '20.0%'
        ELSE '未知'
    END as effort_allocation,
    pe.created_at
FROM project_evaluation pe
INNER JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE su_evaluatee.nick_name = '张杰'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07'
ORDER BY pe.score DESC;

-- ===== 张杰最终评分计算 =====
-- 计算公式：机构负责人评分 * 0.4 + 项目负责人平均分 * 0.6
SELECT 
    '张杰最终评分计算' as person,
    manager_score,
    project_leader_avg,
    ROUND(manager_score * 0.4 + project_leader_avg * 0.6, 2) as final_score,
    '机构负责人评分 * 0.4 + 项目负责人平均分 * 0.6' as calculation_formula
FROM (
    SELECT 
        (SELECT pe1.score FROM project_evaluation pe1 
         INNER JOIN sys_user su1 ON pe1.evaluatee_id = su1.user_id 
         WHERE su1.nick_name = '张杰' AND pe1.evaluation_type = 'manager' AND pe1.evaluation_month = '2025-07' LIMIT 1) as manager_score,
        (SELECT AVG(pe2.score) FROM project_evaluation pe2 
         INNER JOIN sys_user su2 ON pe2.evaluatee_id = su2.user_id 
         WHERE su2.nick_name = '张杰' AND pe2.evaluation_type = 'project_leader' AND pe2.evaluation_month = '2025-07') as project_leader_avg
) as scores;

-- ===== 按精力分配比例加权的项目负责人评分计算 =====
-- 考虑精力分配比例的加权平均计算
SELECT 
    '张杰加权项目负责人评分' as person,
    '考虑精力分配比例的加权平均' as calculation_method,
    ROUND(
        (92 * 0.20 + 94 * 0.40 + 92 * 0.20 + 94 * 0.20) / (0.20 + 0.40 + 0.20 + 0.20), 2
    ) as weighted_project_leader_score,
    '绿氢制备系统(92*0.2) + 新型低成本制氢(94*0.4) + 大唐甘肃白银(92*0.2) + 新型低成本光伏(94*0.2)' as calculation_detail
FROM dual;

-- ===== 张杰加权最终评分计算 =====
-- 使用加权项目负责人评分计算最终得分
SELECT 
    '张杰加权最终评分' as person,
    92 as manager_score,
    93.2 as weighted_project_leader_avg,
    ROUND(92 * 0.4 + 93.2 * 0.6, 2) as weighted_final_score,
    '使用精力分配比例加权的最终评分' as note
FROM dual;

-- ===== 对比分析 =====
-- 对比简单平均和加权平均的差异
SELECT 
    '张杰评分对比分析' as analysis_type,
    '简单平均' as method1,
    93.0 as simple_avg_project_score,
    ROUND(92 * 0.4 + 93.0 * 0.6, 2) as simple_final_score,
    '加权平均' as method2,
    93.2 as weighted_avg_project_score,
    ROUND(92 * 0.4 + 93.2 * 0.6, 2) as weighted_final_score
FROM dual;

-- ===== 项目负责人评价情况汇总 =====
-- 显示各项目负责人对张杰的评价情况
SELECT 
    '项目负责人评价汇总' as summary_type,
    su_evaluator.nick_name as project_leader,
    COUNT(*) as evaluation_count,
    AVG(pe.score) as avg_score_given,
    GROUP_CONCAT(DISTINCT pi.project_name SEPARATOR '; ') as projects
FROM project_evaluation pe
INNER JOIN project_info pi ON pe.project_id = pi.id
INNER JOIN sys_user su_evaluator ON pe.evaluator_id = su_evaluator.user_id
INNER JOIN sys_user su_evaluatee ON pe.evaluatee_id = su_evaluatee.user_id
WHERE su_evaluatee.nick_name = '张杰'
AND pe.evaluation_type = 'project_leader'
AND pe.evaluation_month = '2025-07'
GROUP BY su_evaluator.nick_name
ORDER BY avg_score_given DESC;

-- ===== 执行完成确认 =====
SELECT 
    '执行完成' as status,
    NOW() as completion_time,
    '张杰的4个项目负责人评分记录已添加完成' as result,
    '预期项目负责人平均分: 93.0分' as expected_avg,
    '预期最终得分: 92.8分' as expected_final_score;
