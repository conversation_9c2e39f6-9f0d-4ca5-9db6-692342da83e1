<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评价人" prop="evaluatorName">
        <el-input
          v-model="queryParams.evaluatorName"
          placeholder="请输入评价人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被评价人" prop="evaluateeName">
        <el-input
          v-model="queryParams.evaluateeName"
          placeholder="请输入被评价人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="评分" prop="score">
        <el-input
          v-model="queryParams.score"
          placeholder="请输入评分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="评价类型" prop="evaluationType">
        <el-select
          v-model="queryParams.evaluationType"
          placeholder="请选择评价类型"
          clearable
          style="width: 180px"
          @change="handleQuery">
          <el-option
            label="机构负责人评价"
            value="manager">
          </el-option>
          <el-option
            label="项目负责人评价"
            value="project_leader">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="评价月份" prop="evaluationMonth">
        <el-date-picker
          v-model="queryParams.evaluationMonth"
          type="month"
          placeholder="请选择评价月份"
          value-format="yyyy-MM"
          format="yyyy年MM月"
          clearable
          style="width: 180px">
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="创建时间" prop="createdAt">
        <el-date-picker clearable
          v-model="queryParams.createdAt"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="更新时间" prop="updatedAt">
        <el-date-picker clearable
          v-model="queryParams.updatedAt"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择更新时间">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:evaluation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:evaluation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:evaluation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:evaluation:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="evaluationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键ID" align="center" prop="id" width="80" />
      <el-table-column label="项目ID" align="center" prop="projectId" width="80" /> -->
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="150" show-overflow-tooltip />
      <!-- <el-table-column label="评价人ID" align="center" prop="evaluatorId" width="100" /> -->
      <el-table-column label="评价人" align="center" prop="evaluatorName" min-width="120" show-overflow-tooltip />
      <!-- <el-table-column label="被评价人ID" align="center" prop="evaluateeId" width="100" /> -->
      <el-table-column label="被评价人" align="center" prop="evaluateeName" min-width="120" show-overflow-tooltip />
      <el-table-column label="评分" align="center" prop="score" width="80" />
      <el-table-column label="评价月份" align="center" prop="evaluationMonth" width="120" />
      <el-table-column label="评价类型" align="center" prop="evaluationType" width="150">
        <template slot-scope="scope">
          <el-tag :type="scope.row.evaluationType === 'manager' ? 'primary' : 'success'" size="small">
            {{ scope.row.evaluationType === 'manager' ? '机构负责人评价' : '项目负责人评价' }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column label="评价意见" align="center" prop="comments" min-width="150" show-overflow-tooltip /> -->
      <!-- <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="更新时间" align="center" prop="updatedAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:evaluation:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:evaluation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目评价对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目ID" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目ID" />
        </el-form-item>
        <el-form-item label="评价人ID" prop="evaluatorId">
          <el-input v-model="form.evaluatorId" placeholder="请输入评价人ID" />
        </el-form-item>
        <el-form-item label="被评价人ID" prop="evaluateeId">
          <el-input v-model="form.evaluateeId" placeholder="请输入被评价人ID" />
        </el-form-item>
        <el-form-item label="评分" prop="score">
          <el-input v-model="form.score" placeholder="请输入评分" />
        </el-form-item>
        <el-form-item label="评价月份 格式：yyyy-MM" prop="evaluationMonth">
          <el-input v-model="form.evaluationMonth" placeholder="请输入评价月份 格式：yyyy-MM" />
        </el-form-item>
        <el-form-item label="评价意见" prop="comments">
          <el-input v-model="form.comments" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createdAt">
          <el-date-picker clearable
            v-model="form.createdAt"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新时间" prop="updatedAt">
          <el-date-picker clearable
            v-model="form.updatedAt"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择更新时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEvaluation, getEvaluation, delEvaluation, addEvaluation, updateEvaluation } from "@/api/system/evaluation";
import { listUser, listAllUser } from "@/api/system/user";
import { listInfo } from "@/api/system/info";

export default {
  name: "Evaluation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目评价表格数据
      evaluationList: [],
      // 用户映射（ID -> 用户信息）
      userMap: {},
      // 项目映射（ID -> 项目信息）
      projectMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        projectName: null,
        evaluatorId: null,
        evaluatorName: null,
        evaluateeId: null,
        evaluateeName: null,
        score: null,
        evaluationMonth: null,
        evaluationType: null,
        comments: null,
        createdAt: null,
        updatedAt: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectId: [
          { required: true, message: "项目ID不能为空", trigger: "blur" }
        ],
        evaluatorId: [
          { required: true, message: "评价人ID不能为空", trigger: "blur" }
        ],
        evaluateeId: [
          { required: true, message: "被评价人ID不能为空", trigger: "blur" }
        ],
        score: [
          { required: true, message: "评分不能为空", trigger: "blur" }
        ],
        evaluationMonth: [
          { required: true, message: "评价月份 格式：yyyy-MM不能为空", trigger: "blur" }
        ],
        evaluationType: [
          { required: true, message: "评价类型：manager-机构负责人评价, project_leader-项目负责人评价不能为空", trigger: "change" }
        ],
        createdAt: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updatedAt: [
          { required: true, message: "更新时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.loadBasicData();
  },
  methods: {
    // 加载基础数据（用户和项目信息）
    async loadBasicData() {
      try {
        // 并行加载用户和项目数据
        const [userResponse, projectResponse] = await Promise.all([
          listAllUser({ includeDeleted: true }), // 获取所有用户，包括已删除的用户
          listInfo({ pageNum: 1, pageSize: 10000 }) // 获取所有项目
        ]);

        // 构建用户映射
        this.userMap = {};
        // listAllUser 返回的数据结构是 response.data，而不是 response.rows
        const users = userResponse.data || userResponse.rows || [];
        users.forEach(user => {
          this.userMap[user.userId] = user;
        });

        // 构建项目映射
        this.projectMap = {};
        if (projectResponse.rows) {
          projectResponse.rows.forEach(project => {
            this.projectMap[project.id] = project;
          });
        }

        // 加载评价列表
        this.getList();
      } catch (error) {
        this.getList(); // 即使基础数据加载失败，也要加载评价列表
      }
    },

    /** 查询项目评价列表 */
    getList() {
      this.loading = true;
      listEvaluation(this.queryParams).then(response => {
        // 为每条评价记录添加项目名称和用户昵称
        this.evaluationList = (response.rows || []).map(evaluation => {
          const evaluator = this.userMap[evaluation.evaluatorId];
          const evaluatee = this.userMap[evaluation.evaluateeId];
          const project = this.projectMap[evaluation.projectId];

          return {
            ...evaluation,
            projectName: evaluation.projectId === 0 ? '' :
                        (project ? project.projectName : '未知项目'),
            evaluatorName: evaluator ? (evaluator.nickName || evaluator.userName) : '未知用户',
            evaluateeName: evaluatee ? (evaluatee.nickName || evaluatee.userName) : '未知用户'
          };
        });

        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        evaluatorId: null,
        evaluateeId: null,
        score: null,
        evaluationMonth: null,
        evaluationType: null,
        comments: null,
        createdAt: null,
        updatedAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目评价";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getEvaluation(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改项目评价";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateEvaluation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEvaluation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除项目评价编号为"' + ids + '"的数据项？').then(function() {
        return delEvaluation(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/evaluation/export', {
        ...this.queryParams
      }, `evaluation_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
