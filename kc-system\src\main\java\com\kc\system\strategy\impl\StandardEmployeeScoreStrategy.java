package com.kc.system.strategy.impl;

import com.kc.system.domain.ProjectEvaluation;
import com.kc.system.dto.ScoreCalculationContext;
import com.kc.system.strategy.ScoreCalculationStrategy;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import com.kc.system.service.IProjectEvaluationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

/**
 * 标准员工评分策略
 * 适用于有项目参与的普通员工
 * 计算规则：机构负责人评分×60% + 项目负责人评分×40%
 * 
 * <AUTHOR>
 */
@Component
public class StandardEmployeeScoreStrategy implements ScoreCalculationStrategy {

    private static final Logger log = LoggerFactory.getLogger(StandardEmployeeScoreStrategy.class);

    private static final String STRATEGY_TYPE = "STANDARD_EMPLOYEE";
    private static final BigDecimal MANAGER_WEIGHT = new BigDecimal("0.6");
    private static final BigDecimal PROJECT_WEIGHT = new BigDecimal("0.4");
    
    @Override
    public BigDecimal calculateScore(ScoreCalculationContext context) {
        List<ProjectEvaluation> evaluations = context.getEvaluations();

        // 获取机构负责人评分
        BigDecimal managerScore = getManagerScore(evaluations);

        // 获取项目负责人评分（包含自评替代逻辑）
        BigDecimal projectScore = calculateProjectLeaderScore(context, managerScore);

        log.info("用户[{}]标准员工策略计算：机构负责人评分={}, 项目负责人评分={}",
                context.getEvaluatee().getNickName(), managerScore, projectScore);

        if (managerScore == null && projectScore == null) {
            return BigDecimal.ZERO;
        }

        if (managerScore == null) {
            return projectScore;
        }

        if (projectScore == null) {
            return managerScore;
        }

        // 计算加权平均分
        BigDecimal weightedManagerScore = managerScore.multiply(MANAGER_WEIGHT);
        BigDecimal weightedProjectScore = projectScore.multiply(PROJECT_WEIGHT);

        BigDecimal finalScore = weightedManagerScore.add(weightedProjectScore)
                .setScale(2, RoundingMode.HALF_UP);

        log.info("用户[{}]最终计算：{}×60% + {}×40% = {}",
                context.getEvaluatee().getNickName(), managerScore, projectScore, finalScore);

        return finalScore;
    }
    
    @Override
    public String getStrategyType() {
        return STRATEGY_TYPE;
    }
    
    @Override
    public boolean isApplicable(ScoreCalculationContext context) {
        // 适用于有项目参与且不是子部门成员的员工
        return !Boolean.TRUE.equals(context.getIsSubDeptMember()) 
                && hasProjectParticipation(context);
    }
    
    @Override
    public String getCalculationDetails(ScoreCalculationContext context) {
        List<ProjectEvaluation> evaluations = context.getEvaluations();
        BigDecimal managerScore = getManagerScore(evaluations);
        BigDecimal projectScore = getProjectScore(evaluations);

        return String.format("标准员工评分：机构负责人评分(%.2f)×60%% + 项目负责人评分(%.2f)×40%%",
                managerScore != null ? managerScore : 0,
                projectScore != null ? projectScore : 0);
    }

    private BigDecimal getManagerScore(List<ProjectEvaluation> evaluations) {
        return evaluations.stream()
                .filter(eval -> "manager".equals(eval.getEvaluationType()))
                .map(ProjectEvaluation::getScore)
                .findFirst()
                .orElse(null);
    }

    private BigDecimal getProjectScore(List<ProjectEvaluation> evaluations) {
        return evaluations.stream()
                .filter(eval -> "project_leader".equals(eval.getEvaluationType()))
                .map(ProjectEvaluation::getScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(
                    evaluations.stream()
                            .filter(eval -> "project_leader".equals(eval.getEvaluationType()))
                            .count()
                ), 2, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算项目负责人评分（包含自评替代逻辑）
     *
     * @param context 评分计算上下文
     * @param managerScore 机构负责人评分，用于自评替代
     * @return 项目负责人评分
     */
    private BigDecimal calculateProjectLeaderScore(ScoreCalculationContext context, BigDecimal managerScore) {
        List<ProjectEvaluation> evaluations = context.getEvaluations();
        String userName = context.getEvaluatee().getNickName();

        log.info("=== 开始计算用户[{}]的项目负责人评分 ===", userName);

        // 获取项目负责人评分
        List<ProjectEvaluation> projectEvaluations = evaluations.stream()
                .filter(eval -> "project_leader".equals(eval.getEvaluationType()))
                .collect(java.util.stream.Collectors.toList());

        if (projectEvaluations.isEmpty()) {
            log.info("用户[{}]没有项目负责人评分记录，返回0分", userName);
            return BigDecimal.ZERO;
        }

        // 简化处理：计算平均分
        BigDecimal totalScore = BigDecimal.ZERO;
        int count = 0;

        for (ProjectEvaluation eval : projectEvaluations) {
            totalScore = totalScore.add(eval.getScore());
            count++;
            log.info("用户[{}]项目[{}]评分：{}", userName, eval.getProjectId(), eval.getScore());
        }

        BigDecimal avgScore = totalScore.divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP);
        log.info("用户[{}]项目负责人平均评分：{}", userName, avgScore);

        // 特殊处理：如果用户同时是项目负责人（通过用户名判断），需要考虑自评替代
        // 这里采用一个启发式方法：如果机构负责人评分存在且项目负责人评分较低，可能需要调整
        if (managerScore != null && avgScore.compareTo(managerScore) < 0) {
            // 如果项目负责人评分明显低于机构负责人评分，可能存在自评替代的情况
            // 采用加权调整：70%项目负责人评分 + 30%机构负责人评分
            BigDecimal adjustedScore = avgScore.multiply(new BigDecimal("0.7"))
                    .add(managerScore.multiply(new BigDecimal("0.3")))
                    .setScale(2, RoundingMode.HALF_UP);

            log.info("用户[{}]项目负责人评分调整：原始{}，机构负责人{}，调整后{}",
                    userName, avgScore, managerScore, adjustedScore);

            return adjustedScore;
        }

        return avgScore;
    }

    private boolean hasProjectParticipation(ScoreCalculationContext context) {
        return context.getProjectParticipations() != null
                && !context.getProjectParticipations().isEmpty();
    }
}
