package com.kc.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.kc.common.annotation.Excel;
import com.kc.common.core.domain.BaseEntity;

/**
 * 项目劳务费构成对象 project_salary_detail
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public class ProjectSalaryDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 工作月份(YYYY-MM) */
    @Excel(name = "工作月份(YYYY-MM)")
    private String workMonth;

    /** 劳务费(万元) */
    @Excel(name = "劳务费(万元)")
    private BigDecimal salaryAmount;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setWorkMonth(String workMonth) 
    {
        this.workMonth = workMonth;
    }

    public String getWorkMonth() 
    {
        return workMonth;
    }
    public void setSalaryAmount(BigDecimal salaryAmount) 
    {
        this.salaryAmount = salaryAmount;
    }

    public BigDecimal getSalaryAmount() 
    {
        return salaryAmount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("userName", getUserName())
            .append("workMonth", getWorkMonth())
            .append("salaryAmount", getSalaryAmount())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
