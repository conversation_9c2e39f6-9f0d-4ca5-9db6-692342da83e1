<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kc.system.mapper.DeptBonusAllocationMapper">
    
    <resultMap type="DeptBonusAllocation" id="DeptBonusAllocationResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="allocationMonth"    column="allocation_month"    />
        <result property="performanceRank"    column="performance_rank"    />
        <result property="totalBonus"    column="total_bonus"    />
        <result property="allocatedBonus"    column="allocated_bonus"    />
        <result property="remainingBonus"    column="remaining_bonus"    />
        <result property="allocationStatus"    column="allocation_status"    />
        <result property="allocatorId"    column="allocator_id"    />
        <result property="allocatorName"    column="allocator_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDeptBonusAllocationVo">
        select id, dept_id, dept_name, allocation_month, performance_rank, total_bonus, allocated_bonus, remaining_bonus, allocation_status, allocator_id, allocator_name, create_by, create_time, update_by, update_time, remark from dept_bonus_allocation
    </sql>

    <select id="selectDeptBonusAllocationList" parameterType="DeptBonusAllocation" resultMap="DeptBonusAllocationResult">
        <include refid="selectDeptBonusAllocationVo"/>
        <where>  
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="allocationMonth != null  and allocationMonth != ''"> and allocation_month = #{allocationMonth}</if>
            <if test="performanceRank != null "> and performance_rank = #{performanceRank}</if>
            <if test="allocationStatus != null  and allocationStatus != ''"> and allocation_status = #{allocationStatus}</if>
            <if test="allocatorId != null "> and allocator_id = #{allocatorId}</if>
        </where>
        order by allocation_month desc, performance_rank asc
    </select>
    
    <select id="selectDeptBonusAllocationById" parameterType="Long" resultMap="DeptBonusAllocationResult">
        <include refid="selectDeptBonusAllocationVo"/>
        where id = #{id}
    </select>

    <select id="selectByDeptIdAndMonth" resultMap="DeptBonusAllocationResult">
        <include refid="selectDeptBonusAllocationVo"/>
        where dept_id = #{deptId} and allocation_month = #{allocationMonth}
    </select>

    <select id="selectByMonth" resultMap="DeptBonusAllocationResult">
        <include refid="selectDeptBonusAllocationVo"/>
        where allocation_month = #{allocationMonth}
        order by performance_rank asc
    </select>
        
    <insert id="insertDeptBonusAllocation" parameterType="DeptBonusAllocation" useGeneratedKeys="true" keyProperty="id">
        insert into dept_bonus_allocation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null and deptName != ''">dept_name,</if>
            <if test="allocationMonth != null and allocationMonth != ''">allocation_month,</if>
            <if test="performanceRank != null">performance_rank,</if>
            <if test="totalBonus != null">total_bonus,</if>
            <if test="allocatedBonus != null">allocated_bonus,</if>
            <if test="remainingBonus != null">remaining_bonus,</if>
            <if test="allocationStatus != null">allocation_status,</if>
            <if test="allocatorId != null">allocator_id,</if>
            <if test="allocatorName != null">allocator_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null and deptName != ''">#{deptName},</if>
            <if test="allocationMonth != null and allocationMonth != ''">#{allocationMonth},</if>
            <if test="performanceRank != null">#{performanceRank},</if>
            <if test="totalBonus != null">#{totalBonus},</if>
            <if test="allocatedBonus != null">#{allocatedBonus},</if>
            <if test="remainingBonus != null">#{remainingBonus},</if>
            <if test="allocationStatus != null">#{allocationStatus},</if>
            <if test="allocatorId != null">#{allocatorId},</if>
            <if test="allocatorName != null">#{allocatorName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertDeptBonusAllocation" parameterType="java.util.List">
        insert into dept_bonus_allocation(dept_id, dept_name, allocation_month, performance_rank, total_bonus, allocated_bonus, remaining_bonus, allocation_status, allocator_id, allocator_name, create_by, create_time, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.deptId}, #{item.deptName}, #{item.allocationMonth}, #{item.performanceRank}, #{item.totalBonus}, #{item.allocatedBonus}, #{item.remainingBonus}, #{item.allocationStatus}, #{item.allocatorId}, #{item.allocatorName}, #{item.createBy}, #{item.createTime}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateDeptBonusAllocation" parameterType="DeptBonusAllocation">
        update dept_bonus_allocation
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="allocationMonth != null and allocationMonth != ''">allocation_month = #{allocationMonth},</if>
            <if test="performanceRank != null">performance_rank = #{performanceRank},</if>
            <if test="totalBonus != null">total_bonus = #{totalBonus},</if>
            <if test="allocatedBonus != null">allocated_bonus = #{allocatedBonus},</if>
            <if test="remainingBonus != null">remaining_bonus = #{remainingBonus},</if>
            <if test="allocationStatus != null">allocation_status = #{allocationStatus},</if>
            <if test="allocatorId != null">allocator_id = #{allocatorId},</if>
            <if test="allocatorName != null">allocator_name = #{allocatorName},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateAllocationStatus">
        update dept_bonus_allocation 
        set allocated_bonus = #{allocatedBonus}, 
            remaining_bonus = #{remainingBonus}, 
            allocation_status = #{allocationStatus}
        where id = #{id}
    </update>

    <delete id="deleteDeptBonusAllocationById" parameterType="Long">
        delete from dept_bonus_allocation where id = #{id}
    </delete>

    <delete id="deleteDeptBonusAllocationByIds" parameterType="String">
        delete from dept_bonus_allocation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByMonth">
        delete from dept_bonus_allocation where allocation_month = #{allocationMonth}
    </delete>

</mapper>
