package com.kc.system.mapper;

import java.util.List;
import java.util.Map;
import com.kc.system.domain.ProjectMembers;
import org.apache.ibatis.annotations.Param;

/**
 * 项目成员关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-14
 */
public interface ProjectMembersMapper 
{
    /**
     * 查询项目成员关联
     * 
     * @param id 项目成员关联主键
     * @return 项目成员关联
     */
    public ProjectMembers selectProjectMembersById(Long id);

    /**
     * 查询项目成员关联列表
     * 
     * @param projectMembers 项目成员关联
     * @return 项目成员关联集合
     */
    public List<ProjectMembers> selectProjectMembersList(ProjectMembers projectMembers);

    /**
     * 新增项目成员关联
     * 
     * @param projectMembers 项目成员关联
     * @return 结果
     */
    public int insertProjectMembers(ProjectMembers projectMembers);

    /**
     * 修改项目成员关联
     * 
     * @param projectMembers 项目成员关联
     * @return 结果
     */
    public int updateProjectMembers(ProjectMembers projectMembers);

    /**
     * 删除项目成员关联
     * 
     * @param id 项目成员关联主键
     * @return 结果
     */
    public int deleteProjectMembersById(Long id);

    /**
     * 批量删除项目成员关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectMembersByIds(Long[] ids);

    /**
     * 根据项目ID删除项目成员关联
     * 
     * @param projectId 项目ID
     * @return 结果
     */
    public int deleteProjectMembersByProjectId(Long projectId);

    /**
     * 根据项目ID查询项目成员关联
     * 
     * @param projectId 项目ID
     * @return 项目成员关联集合
     */
    public List<Map<String, Object>> selectProjectMembersByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据用户名和角色统计项目数
     */
    long countProjectsByUserNameAndRole(@Param("userName") String userName, @Param("role") String role);

    /**
     * 根据项目ID和用户名查询项目成员
     */
    public ProjectMembers selectProjectMemberByUserName(@Param("projectId") Long projectId, @Param("userName") String userName);

    /**
     * 查询项目成员详细信息
     * 
     * @param projectId 项目ID
     * @return 成员信息
     */
    public List<Map<String, Object>> selectMemberDetailsByProjectId(Long projectId);

    /**
     * 获取用户参与的所有项目
     * 
     * @param userName 用户名
     * @return 项目列表
     */
    public List<Map<String, Object>> selectUserProjects(@Param("userName") String userName);

    /**
     * 获取部门下有项目的用户及其项目信息
     * 
     * @param deptIds 部门ID列表
     * @return 有项目的用户及其项目信息
     */
    public List<Map<String, Object>> selectDeptMembersWithProjects(@Param("deptIds") List<Long> deptIds);
}
