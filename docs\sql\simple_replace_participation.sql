-- ===== 简单替换方案：用2025-07数据覆盖2025-06数据 =====
-- 警告：此方案会丢失原有的2025-06数据

-- ===== 第一步：备份 =====
CREATE TABLE project_participation_backup_simple_20250715 AS 
SELECT * FROM project_participation WHERE month IN ('2025-06', '2025-07');

-- ===== 第二步：预览将被删除的2025-06数据 =====
SELECT 
    '将被删除的2025-06数据' as warning_type,
    p06.user_name,
    p06.project_id,
    p06.project_name,
    p06.participation_rate as will_be_lost_rate,
    p07.participation_rate as will_replace_rate
FROM project_participation p06
INNER JOIN project_participation p07 ON 
    p06.user_name = p07.user_name 
    AND p06.project_id = p07.project_id
WHERE p06.month = '2025-06' 
AND p07.month = '2025-07'
ORDER BY p06.user_name, p06.project_id;

-- ===== 第三步：执行替换 =====
-- 注意：这会永久删除冲突的2025-06数据
/*
-- 删除有冲突的2025-06记录
DELETE p06 FROM project_participation p06
INNER JOIN project_participation p07 ON 
    p06.user_name = p07.user_name 
    AND p06.project_id = p07.project_id
WHERE p06.month = '2025-06' 
AND p07.month = '2025-07';

-- 将所有2025-07记录改为2025-06
UPDATE project_participation 
SET month = '2025-06'
WHERE month = '2025-07';
*/

-- ===== 第四步：验证结果 =====
/*
SELECT 
    '替换后验证' as check_type,
    month,
    COUNT(*) as count
FROM project_participation 
WHERE month IN ('2025-05', '2025-06', '2025-07', '2025-08')
GROUP BY month
ORDER BY month;
*/

-- ===== 回滚方案 =====
/*
DELETE FROM project_participation WHERE month IN ('2025-06', '2025-07');
INSERT INTO project_participation SELECT * FROM project_participation_backup_simple_20250715;
*/
